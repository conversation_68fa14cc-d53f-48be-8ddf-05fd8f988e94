export class InvoiceResponseDto {
  id: string;
  userId: string;
  planId?: string;
  purchaseId?: string;
  items: {
    id: string;
    description: string;
    quantity: number;
    price: number;
  }[];
  totals: {
    type: string;
    description: string;
    amount: number;
  }[];
  paidDate: Date | null;
  metadata: any;
  status: string;
  invoiceCode: string;
  invoiceLink: string;
  paymentLink: string;
  paymentToken: string;
  createdAt: Date;
  updatedAt: Date;
}