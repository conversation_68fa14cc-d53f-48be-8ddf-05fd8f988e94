import { IsObject } from "class-validator";
import { BfiAnswer, SelfDisclosureAnswer } from "../schemas/personality-test.schema";

export interface BfiScores {
  extraversion: number;
  agreeableness: number;
  conscientiousness: number;
  negativeEmotionality: number;
  openMindedness: number;
}

/**
 * Format jawaban untuk pertanyaan BFI
 * Key adalah ID pertanyaan (contoh: 'BFI_E1')
 * Value adalah skor jawaban (1-5)
 */
export type BfiAnswers = Record<string, number>;

/**
 * Format jawaban untuk pertanyaan Self-Disclosure
 * Key adalah ID pertanyaan (contoh: 'SD_KN1')
 * Value adalah skor jawaban (1-5)
 */
export type SelfDisclosureAnswers = Record<string, number>;

/**
 * DTO untuk mengirim jawaban tes kepribadian
 */
export class SubmitPersonalityTestDto {
  @IsObject()
  bfiAnswers: BfiAnswers;

  @IsObject()
  selfDisclosureAnswers: SelfDisclosureAnswers;
}

export class PersonalityTestResponseDto {
  id: string;
  userId: string;
  bfi: BfiScores;
  selfDisclosure: number;
  bfiAnswers: BfiAnswer[];
  selfDisclosureAnswers: SelfDisclosureAnswer[];
  createdAt: Date;
  updatedAt: Date;
}

