export interface QuestionDto {
  id: string;
  text: string;
  dimension?: string;
  category?: string;
}

export interface QuestionsResponseDto {
  bfi: {
    dimensions: {
      id: string;
      name: string;
      description: string;
      questions: QuestionDto[];
    }[];
  };
  selfDisclosure: {
    categories: {
      id: string;
      name: string;
      description: string;
      questions: QuestionDto[];
    }[];
  };
}
