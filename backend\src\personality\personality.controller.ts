import { Body, Controller, Get, Post, UseGuards, Request, Logger, BadRequestException, Inject } from '@nestjs/common';
import { PersonalityService } from './services/personality.service';
import { 
  SubmitPersonalityTestDto,
  BfiAnswers,
  SelfDisclosureAnswers,
  PersonalityTestResponseDto
} from './dto/personality-test.dto';
import { RequestWithUser } from 'src/common/interfaces/request-with-user.interface';
import { QuestionsResponseDto } from './dto/question-response.dto';
import { QuestionService } from './services/question.service';

@Controller('personality')
export class PersonalityController {
  private readonly logger = new Logger(PersonalityController.name);
  constructor(
    private readonly personalityService: PersonalityService,
    private readonly questionService: QuestionService
  ) {}

  /**
   * Submit personality test
   * 
   * @param req 
   * @param submitDto 
   * @returns 
   */
  @Post('test')
  async submitTest(@Request() req: RequestWithUser, @Body() submitDto: SubmitPersonalityTestDto): Promise<PersonalityTestResponseDto> {
    try {
      // If already submitted, throw error
      const personalityTest = await this.personalityService.getPersonalityTestByUserId(req.user.userId);

      if (personalityTest) {
        throw new BadRequestException('User has already submitted the personality test');
      }
      
      const userId = req.user.userId;
      
      // Validate and transform data
      const [validatedBfiAnswers, validatedSdAnswers] = await Promise.all([
        this.validateBfiAnswers(submitDto.bfiAnswers || {}),
        this.validateSelfDisclosureAnswers(submitDto.selfDisclosureAnswers || {})
      ]);

      const validatedDto: SubmitPersonalityTestDto = {
        bfiAnswers: validatedBfiAnswers,
        selfDisclosureAnswers: validatedSdAnswers
      };
      
      const result = await this.personalityService.submitTest(userId, validatedDto);

      return {
        id: result.id,
        userId: result.userId.toString(),
        bfi: result.bfi,
        selfDisclosure: result.selfDisclosure,
        bfiAnswers: result.bfiAnswers,
        selfDisclosureAnswers: result.selfDisclosureAnswers,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt
      }
    } catch (error) {
      this.logger.error('Error submitting test:', error);
      throw error;
    }
  }
  
  private async validateBfiAnswers(answers: BfiAnswers): Promise<BfiAnswers> {
    // Get all required BFI question IDs from the database
    const requiredQuestions = await this.questionService.findAllBfiQuestions();
    const requiredQuestionIds = requiredQuestions.map(q => q.questionId);
    
    // Check if all required questions are answered
    const answeredQuestionIds = Object.keys(answers);
    const missingQuestions = requiredQuestionIds.filter(id => !answeredQuestionIds.includes(id));
    
    if (missingQuestions.length > 0) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'Some required BFI questions are not answered',
        missingQuestions,
      });
    }

    // Validate answer values
    const result: BfiAnswers = {};
    for (const [key, value] of Object.entries(answers)) {
      const numValue = Number(value);
      if (isNaN(numValue) || numValue < 1 || numValue > 5) {
        throw new BadRequestException({
          statusCode: 400,
          message: `Invalid answer value for BFI question ${key}. Value must be between 1 and 5`,
          questionId: key,
          invalidValue: value,
        });
      }
      result[key] = numValue as 1 | 2 | 3 | 4 | 5;
    }
    
    return result;
  }
  
  private async validateSelfDisclosureAnswers(answers: SelfDisclosureAnswers): Promise<SelfDisclosureAnswers> {
    // Get all required Self-Disclosure question IDs from the database
    const requiredQuestions = await this.questionService.findAllSelfDisclosureQuestions();
    const requiredQuestionIds = requiredQuestions.map(q => q.questionId);
    
    // Check if all required questions are answered
    const answeredQuestionIds = Object.keys(answers);
    const missingQuestions = requiredQuestionIds.filter(id => !answeredQuestionIds.includes(id));
    
    if (missingQuestions.length > 0) {
      throw new BadRequestException({
        statusCode: 400,
        message: 'Some required Self-Disclosure questions are not answered',
        missingQuestions,
      });
    }

    // Validate answer values
    const result: SelfDisclosureAnswers = {};
    for (const [key, value] of Object.entries(answers)) {
      const numValue = Number(value);
      if (isNaN(numValue) || numValue < 1 || numValue > 5) {
        throw new BadRequestException({
          statusCode: 400,
          message: `Invalid answer value for Self-Disclosure question ${key}. Value must be between 1 and 5`,
          questionId: key,
          invalidValue: value,
        });
      }
      result[key] = numValue as 1 | 2 | 3 | 4 | 5;
    }
    
    return result;
  }

  /**
   * Get all personality test questions
   * 
   * @returns 
   */
  @Get('questions')
  async getQuestions(): Promise<QuestionsResponseDto> {
    return this.personalityService.getQuestions();
  }
}
