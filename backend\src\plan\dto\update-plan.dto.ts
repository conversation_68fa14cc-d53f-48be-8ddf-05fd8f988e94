import { IsString, <PERSON>N<PERSON>ber, IsEnum, IsOptional, IsBoolean, Min, IsObject } from 'class-validator';
import { PlanInterval, PlanStatus } from '../schemas/plan.schema';

export class UpdatePlanDto {
  @IsString()
  @IsOptional()
  code?: string;

  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsNumber()
  @Min(0)
  @IsOptional()
  price?: number;

  @IsEnum(PlanInterval)
  @IsOptional()
  interval?: PlanInterval;

  @IsNumber()
  @Min(0)
  @IsOptional()
  psychologistConsultations?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  chats?: number;

  @IsObject()
  @IsOptional()
  features?: Record<string, any>;

  @IsEnum(PlanStatus)
  @IsOptional()
  status?: PlanStatus;

  @IsNumber()
  @IsOptional()
  sortOrder?: number;

  @IsBoolean()
  @IsOptional()
  isPopular?: boolean;

  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;

  @IsBoolean()
  @IsOptional()
  isHidden?: boolean;
}
