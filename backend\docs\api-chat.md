# Chat API Documentation

This document provides detailed information about the Chat API endpoints, WebSocket events, request/response formats, and examples.

## Table of Contents
- [Base URL](#base-url)
- [Authentication](#authentication)
- [REST API Endpoints](#rest-api-endpoints)
  - [Get User Chats](#get-user-chats)
  - [Get Chat Messages](#get-chat-messages)
- [WebSocket Events](#websocket-events)
  - [Connecting to Cha<PERSON>](#connecting-to-chat)
  - [Sending Messages](#sending-messages)
  - [Receiving Messages](#receiving-messages)
- [Data Types](#data-types)
  - [Chat Response](#chat-response)
  - [Chat Message](#chat-message)
  - [Pagination](#pagination)
- [Client Implementation](#client-implementation)
  - [Next.js Component Example](#nextjs-component-example)

## Base URL
```
{{base_url}}/chats
```

## Authentication
All endpoints require JWT authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## REST API Endpoints

### Get User Chats
Retrieves a list of chats for the authenticated user.

#### Endpoint
```
GET /
```

#### Response
```json
[
  {
    "id": "507f1f77bcf86cd799439011",
    "matchId": "match_123",
    "user": {
      "id": "507f1f77bcf86cd799439012",
      "name": "John Doe",
      "image": "https://example.com/profile.jpg",
      "isOnline": true
    },
    "lastMessage": {
      "id": "507f1f77bcf86cd799439013",
      "senderId": "507f1f77bcf86cd799439012",
      "message": "Hello there!",
      "sentAt": "2023-06-15T10:30:00.000Z"
    }
  }
]
```

### Get Chat Messages
Retrieves messages for a specific chat. The `chatId` parameter can be either a chat ID or match ID.

#### Endpoint
```
GET /:chatId/messages?page=1&limit=10
```

#### Query Parameters
| Parameter | Type   | Required | Description                  |
|-----------|--------|----------|------------------------------|
| page      | number | No       | Page number (default: 1)     |
| limit     | number | No       | Items per page (default: 10) |

#### Response
```json
{
  "data": [
    {
      "id": "507f1f77bcf86cd799439013",
      "chatId": "507f1f77bcf86cd799439011",
      "senderId": "507f1f77bcf86cd799439012",
      "message": "Hello there!",
      "sentAt": "2023-06-15T10:30:00.000Z"
    }
  ],
  "totalItems": 1,
  "itemsPerPage": 10,
  "totalPages": 1,
  "currentPage": 1
}
```

## WebSocket Events

### Connecting to Chat
To establish a WebSocket connection, connect to the chat namespace with the following parameters:

```typescript
const socket = io(`${process.env.NEXT_PUBLIC_API_URL}/chat`, {
  transports: ['websocket'],
  auth: {
    token: `Bearer ${userToken}`
  },
  query: {
    chatId: 'chat_123' // or matchId: 'match_123'
  }
});
```

### Sending Messages
To send a message, emit a `sendMessage` event:

```typescript
socket.emit('sendMessage', {
  message: 'Hello, world!'
});
```

### Receiving Messages
Listen for the `message` event to receive new messages:

```typescript
socket.on('message', (message) => {
  console.log('New message:', message);
  // Handle the new message
});
```

## Data Types

### Chat Response
```typescript
{
  id: string;           // Chat ID
  matchId: string;      // Match ID (if applicable)
  user: {
    id: string;         // User ID
    name: string;       // User's display name
    image: string;      // URL to user's profile image
    isOnline: boolean;  // User's online status
  };
  lastMessage: {
    id: string;         // Message ID
    senderId: string;   // ID of the message sender
    message: string;    // Message content
    sentAt: Date;       // When the message was sent
  } | null;
}
```

### Chat Message
```typescript
{
  id: string;           // Message ID
  chatId: string;       // Chat ID
  senderId: string;     // ID of the message sender
  message: string;      // Message content
  sentAt: Date;         // When the message was sent
}
```

### Pagination
```typescript
{
  data: T[];            // Array of items
  totalItems: number;    // Total number of items
  itemsPerPage: number;  // Number of items per page
  totalPages: number;    // Total number of pages
  currentPage: number;   // Current page number
}
```

## Client Implementation

### Next.js Component Example

```tsx
'use client';

import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { ChatMessage } from '../types/chat';

interface ChatProps {
  chatId: string;
  token: string;
  userId: string;
}

export default function Chat({ chatId, token, userId }: ChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    // Initialize socket connection
    socketRef.current = io(`${process.env.NEXT_PUBLIC_API_URL}/chat`, {
      transports: ['websocket'],
      auth: {
        token: `Bearer ${token}`
      },
      query: { chatId }
    });

    // Handle incoming messages
    socketRef.current.on('message', (message: ChatMessage) => {
      setMessages((prev) => [...prev, message]);
    });

    // Handle connection events
    socketRef.current.on('connect', () => {
      console.log('Connected to chat server');
    });

    socketRef.current.on('disconnect', () => {
      console.log('Disconnected from chat server');
    });

    // Clean up on unmount
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  }, [chatId, token]);

  const sendMessage = () => {
    if (!newMessage.trim() || !socketRef.current) return;
    
    socketRef.current.emit('sendMessage', {
      message: newMessage.trim()
    });
    
    setNewMessage('');
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto p-4">
        {messages.map((msg) => (
          <div 
            key={msg.id} 
            className={`mb-4 ${
              msg.senderId === userId ? 'text-right' : 'text-left'
            }`}
          >
            <div 
              className={`inline-block p-3 rounded-lg ${
                msg.senderId === userId 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-200 text-gray-800'
              }`}
            >
              {msg.message}
              <div className="text-xs mt-1 opacity-75">
                {new Date(msg.sentAt).toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="p-4 border-t">
        <div className="flex gap-2">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
            className="flex-1 p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Type a message..."
          />
          <button
            onClick={sendMessage}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );
}
```

### Usage Example

```tsx
// pages/chat/[chatId].tsx
import { GetServerSideProps } from 'next';
import { getSession } from 'next-auth/react';
import Chat from '../../components/Chat';

export const getServerSideProps: GetServerSideProps = async (context) => {
  const session = await getSession(context);
  
  if (!session) {
    return {
      redirect: {
        destination: '/login',
        permanent: false,
      },
    };
  }

  return {
    props: {
      chatId: context.params?.chatId,
      token: session.accessToken,
      userId: session.user.id,
    },
  };
};

export default function ChatPage({ chatId, token, userId }) {
  return (
    <div className="max-w-2xl mx-auto h-screen flex flex-col">
      <h1 className="text-2xl font-bold p-4 border-b">Chat</h1>
      <Chat chatId={chatId} token={token} userId={userId} />
    </div>
  );
}
```

## Notes

1. The `chatId` parameter in the WebSocket connection can be either a chat ID or a match ID.
2. The WebSocket connection will automatically handle reconnection if the connection is lost.
3. All timestamps are in UTC.
4. The chat component is responsive and works on both desktop and mobile devices.
5. Make sure to handle authentication errors and reconnection logic in your production application.
