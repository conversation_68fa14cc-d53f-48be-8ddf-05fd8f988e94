import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { RegisterForm } from "@/components/auth/register-form";
import { OtpVerificationForm } from "@/components/auth/otp-verification-form";

export default function RegisterPage() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col items-center space-y-2 text-center">
        <Image 
          src="/logo.png" 
          alt="Pairsona Logo" 
          width={160} 
          height={160}
          className="mb-2"
          priority
        />
        <p className="text-sm text-muted-foreground">
          Temukan pasangan sempurna berdasarkan psikologi
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Buat Akun</CardTitle>
          <CardDescription>
            Masukkan detail Anda untuk membuat akun baru
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RegisterForm />
        </CardContent>
        <CardFooter className="flex flex-col items-center space-y-2">
          <div className="text-sm text-muted-foreground">
            Sudah punya akun?{" "}
            <Link href="/login" className="text-accent-red hover:underline">
              Masuk
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
