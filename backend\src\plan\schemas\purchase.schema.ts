import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum PurchaseStatus {
  PAID = 'paid',
  UNPAID = 'unpaid',
  CANCELLED = 'cancelled'
}

export type PurchaseItem = {
  id: string;
  name: string;
  description: string;
  price: number;
  quantity: number;
}

export type PurchaseDocument = Purchase & Document;

@Schema({ timestamps: true })
export class Purchase {
  @Prop()
  userId: string;

  @Prop()
  items: PurchaseItem[];

  @Prop({ default: 0 })
  total: number;

  @Prop({ default: null })
  description: string;

  @Prop({ enum: PurchaseStatus, default: PurchaseStatus.UNPAID })
  status: PurchaseStatus;

  @Prop({ type: String, default: null })
  invoiceId: string;
}

export const PurchaseSchema = SchemaFactory.createForClass(Purchase);
