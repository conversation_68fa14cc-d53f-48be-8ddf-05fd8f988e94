import { IsEmail, IsNotEmpty, IsEnum, ValidateIf, IsString } from 'class-validator';

export enum AuthProvider {
  EMAIL = 'email',
  WHATSAPP = 'whatsapp',
  SMS = 'sms'
}

export class SignInDto {
  @IsEnum(AuthProvider)
  @IsNotEmpty()
  provider: AuthProvider;

  @ValidateIf(o => o.provider === AuthProvider.EMAIL)
  @IsEmail()
  @IsNotEmpty()
  email?: string;

  @ValidateIf(o => o.provider === AuthProvider.WHATSAPP || o.provider === AuthProvider.SMS)
  @IsString()
  @IsNotEmpty()
  phoneNumber?: string;
}