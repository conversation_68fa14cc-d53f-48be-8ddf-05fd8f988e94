import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type MatchDocument = Match & Document;

export enum MatchStatus {
  PENDING = 'pending',
  INVITED = 'invited',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
}

@Schema({ _id: false })
export class MatchDetails {
  @Prop({ required: true })
  extraversion: number;

  @Prop({ required: true })
  agreeableness: number;

  @Prop({ required: true })
  conscientiousness: number;

  @Prop({ required: true })
  negativeEmotionality: number;

  @Prop({ required: true })
  openMindedness: number;

  @Prop({ required: true })
  selfDisclosure: number;
};

@Schema({ timestamps: true })
export class Match {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  user1Id: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  user2Id: Types.ObjectId;

  @Prop({ required: true })
  matchScore: number;

  @Prop({ type: MatchDetails, required: true })
  matchDetails: MatchDetails;

  @Prop({ enum: MatchStatus, default: MatchStatus.PENDING })
  status: string;

  @Prop({ type: Types.ObjectId, ref: 'User', default: null })
  invitedBy: Types.ObjectId | null;

  @Prop({ type: Date, default: null })
  invitedAt: Date;

  @Prop({ type: Types.ObjectId, ref: 'User', default: null })
  acceptedBy: Types.ObjectId | null;

  @Prop({ type: Date, default: null })
  acceptedAt: Date;

  @Prop({ type: Types.ObjectId, ref: 'User', default: null })
  rejectedBy: Types.ObjectId | null;

  @Prop({ type: Date, default: null })
  rejectedAt: Date;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const MatchSchema = SchemaFactory.createForClass(Match);

// Create a compound index to ensure unique pairs
MatchSchema.index({ user1Id: 1, user2Id: 1 }, { unique: true });

// Create indexes for efficient querying
MatchSchema.index({ user1Id: 1, status: 1 });
MatchSchema.index({ user2Id: 1, status: 1 });
MatchSchema.index({ matchScore: -1 }); // For sorting by match score