{"version": 3, "file": "socket.io.min.js", "sources": ["../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/index.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../node_modules/engine.io-client/build/esm/globalThis.browser.js", "../node_modules/engine.io-client/build/esm/util.js", "../node_modules/engine.io-client/build/esm/transport.js", "../node_modules/engine.io-client/build/esm/contrib/yeast.js", "../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../node_modules/engine.io-client/build/esm/transports/xmlhttprequest.browser.js", "../node_modules/engine.io-client/build/esm/transports/polling.js", "../node_modules/engine.io-client/build/esm/transports/websocket-constructor.browser.js", "../node_modules/engine.io-client/build/esm/transports/websocket.js", "../node_modules/engine.io-client/build/esm/transports/webtransport.js", "../node_modules/engine.io-client/build/esm/transports/index.js", "../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../node_modules/engine.io-client/build/esm/socket.js", "../node_modules/engine.io-client/build/esm/index.js", "../node_modules/socket.io-parser/build/esm/is-binary.js", "../node_modules/socket.io-parser/build/esm/binary.js", "../node_modules/socket.io-parser/build/esm/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js", "../build/esm/url.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data\n            .arrayBuffer()\n            .then(toArray)\n            .then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, encoded => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, encodedPacket => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        }\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else if (state === 2 /* READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        }\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\nexport function createCookieJar() { }\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { createCookieJar, XHR as XMLHttpRequest, } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n        if (this.opts.withCredentials) {\n            this.cookieJar = createCookieJar();\n        }\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, cookieJar: this.cookieJar }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        var _a;\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, true);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(xhr);\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"./websocket-constructor.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        // @ts-ignore\n        if (typeof WebTransport !== \"function\") {\n            return;\n        }\n        // @ts-ignore\n        this.transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        this.transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this.transport.ready.then(() => {\n            this.transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this.writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this.writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this.writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this.transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: Polling,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { defaultBinaryType } from \"./transports/websocket-constructor.js\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\n            \"polling\",\n            \"websocket\",\n            \"webtransport\",\n        ];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this.upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            this.resetPingTimeout();\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n        }\n        else if (this.connected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        this.acks[id] = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, [null, ...args]);\n        };\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        // the timeout flag is optional\n        const withErr = this.flags.timeout !== undefined || this._opts.ackTimeout !== undefined;\n        return new Promise((resolve, reject) => {\n            args.push((arg1, arg2) => {\n                if (withErr) {\n                    return arg1 ? reject(arg1) : resolve(arg2);\n                }\n                else {\n                    return resolve(arg1);\n                }\n            });\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "TEXT_ENCODER", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "_ref", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "chars", "lookup", "i", "charCodeAt", "TEXT_DECODER", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "length", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "createPacketEncoderStream", "TransformStream", "transform", "packet", "controller", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "encodePacketToBinary", "header", "payloadLength", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "_len", "attr", "_key", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "prev", "TransportError", "_Error", "_inherits", "_super", "_createSuper", "reason", "description", "context", "_this", "_classCallCheck", "_createClass", "_wrapNativeSuper", "Error", "Transport", "_Emitter", "_super2", "_this2", "writable", "_assertThisInitialized", "query", "socket", "value", "_get", "_getPrototypeOf", "readyState", "doOpen", "doClose", "onClose", "packets", "write", "onPacket", "details", "onPause", "schema", "undefined", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "str", "encodeURIComponent", "alphabet", "map", "seed", "num", "Math", "floor", "yeast", "now", "Date", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Polling", "_Transport", "polling", "location", "isSSL", "protocol", "xd", "forceBase64", "withCredentials", "cookieJar", "createCookieJar", "get", "poll", "pause", "total", "doPoll", "_this3", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "onOpen", "_this4", "close", "_this5", "count", "encodePayload", "doWrite", "timestampRequests", "timestampParam", "sid", "b64", "createUri", "_extends", "Request", "uri", "_this6", "req", "request", "method", "xhrStatus", "onError", "_this7", "onData", "pollXhr", "_this8", "_a", "_this9", "xhr", "open", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "addCookies", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "status", "onLoad", "send", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "WebSocket", "MozWebSocket", "isReactNative", "navigator", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "_loop", "lastPacket", "WT", "WebTransport", "transport", "transportOptions", "name", "closed", "ready", "createBidirectionalStream", "stream", "decoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "pow", "createPacketDecoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "writer", "getWriter", "read", "done", "transports", "websocket", "webtransport", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "writeBuffer", "_typeof", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "offlineEventListener", "EIO", "priorWebsocketSuccess", "createTransport", "setTransport", "onDrain", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "probe", "resetPingTimeout", "onHandshake", "JSON", "sendPacket", "code", "filterUpgrades", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "Socket$1", "withNativeFile", "File", "hasBinary", "toJSON", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "newData", "reconstructPacket", "_reconstructPacket", "PacketType", "RESERVED_EVENTS", "Encoder", "replacer", "EVENT", "ACK", "encodeAsString", "encodeAsBinary", "BINARY_EVENT", "BINARY_ACK", "nsp", "stringify", "deconstruction", "unshift", "isObject", "Decoder", "reviver", "reconstructor", "isBinaryEvent", "decodeString", "BinaryReconstructor", "takeBinaryData", "start", "buf", "next", "payload", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "finishedReconstruction", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "reconPack", "binData", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_opts", "_autoConnect", "subs", "onpacket", "subEvents", "_readyState", "_len2", "_key2", "retries", "fromQueue", "_addToQueue", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "notifyOutgoingListeners", "ackTimeout", "timer", "_len3", "_key3", "_len4", "_key4", "withErr", "reject", "arg1", "arg2", "tryCount", "pending", "_len5", "responseArgs", "_key5", "_drainQueue", "force", "_packet", "_sendConnectPacket", "_pid", "pid", "offset", "_lastOffset", "onconnect", "onevent", "onack", "ondisconnect", "destroy", "message", "emitEvent", "_anyListeners", "_step", "_iterator", "_createForOfIteratorHelper", "s", "f", "sent", "_len6", "_key6", "emitBuffered", "subDestroy", "listener", "_anyOutgoingListeners", "_step2", "_iterator2", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "rand", "random", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "nsps", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "decoder", "autoConnect", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "_reconnecting", "reconnect", "Engine", "skipReconnect", "openSubDestroy", "maybeReconnectOnOpen", "errorSub", "onping", "ondata", "ondecoded", "add", "active", "_i", "_nsps", "_close", "delay", "onreconnect", "attempt", "cache", "parsed", "loc", "test", "href", "url", "sameNamespace", "forceNew", "multiplex"], "mappings": ";;;;;6lJAAA,IAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,IAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQ,SAAAC,GAC9BH,EAAqBH,EAAaM,IAAQA,CAC9C,IACA,ICuCIC,EDvCEC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCX,OAAOY,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAE/BC,EAAS,SAAAC,GACX,MAAqC,mBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,GAAOA,EAAIC,kBAAkBH,WACvC,EACMI,EAAe,SAAHC,EAAoBC,EAAgBC,GAAa,IAA3Cf,EAAIa,EAAJb,KAAMC,EAAIY,EAAJZ,KAC1B,OAAIC,GAAkBD,aAAgBE,KAC9BW,EACOC,EAASd,GAGTe,EAAmBf,EAAMc,GAG/BR,IACJN,aAAgBO,aAAeC,EAAOR,IACnCa,EACOC,EAASd,GAGTe,EAAmB,IAAIb,KAAK,CAACF,IAAQc,GAI7CA,EAASxB,EAAaS,IAASC,GAAQ,IAClD,EACMe,EAAqB,SAACf,EAAMc,GAC9B,IAAME,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,IAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CP,EAAS,KAAOK,GAAW,MAExBH,EAAWM,cAActB,EACpC,EACA,SAASuB,EAAQvB,GACb,OAAIA,aAAgBwB,WACTxB,EAEFA,aAAgBO,YACd,IAAIiB,WAAWxB,GAGf,IAAIwB,WAAWxB,EAAKU,OAAQV,EAAKyB,WAAYzB,EAAK0B,WAEjE,CC9CA,IAHA,IAAMC,EAAQ,mEAERC,EAA+B,oBAAfJ,WAA6B,GAAK,IAAIA,WAAW,KAC9DK,EAAI,EAAGA,EAAIF,GAAcE,IAC9BD,EAAOD,EAAMG,WAAWD,IAAMA,EAkB3B,ICyCHE,EC9DEzB,EAA+C,mBAAhBC,YACxByB,EAAe,SAACC,EAAeC,GACxC,GAA6B,iBAAlBD,EACP,MAAO,CACHlC,KAAM,UACNC,KAAMmC,EAAUF,EAAeC,IAGvC,IAAMnC,EAAOkC,EAAcG,OAAO,GAClC,MAAa,MAATrC,EACO,CACHA,KAAM,UACNC,KAAMqC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAG1CzC,EAAqBM,GAIjCkC,EAAcM,OAAS,EACxB,CACExC,KAAMN,EAAqBM,GAC3BC,KAAMiC,EAAcK,UAAU,IAEhC,CACEvC,KAAMN,EAAqBM,IARxBD,CAUf,EACMuC,EAAqB,SAACrC,EAAMkC,GAC9B,GAAI5B,EAAuB,CACvB,IAAMkC,EFTQ,SAACC,GACnB,IAA8DZ,EAAUa,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOF,OAAeQ,EAAMN,EAAOF,OAAWS,EAAI,EACnC,MAA9BP,EAAOA,EAAOF,OAAS,KACvBO,IACkC,MAA9BL,EAAOA,EAAOF,OAAS,IACvBO,KAGR,IAAMG,EAAc,IAAI1C,YAAYuC,GAAeI,EAAQ,IAAI1B,WAAWyB,GAC1E,IAAKpB,EAAI,EAAGA,EAAIkB,EAAKlB,GAAK,EACtBa,EAAWd,EAAOa,EAAOX,WAAWD,IACpCc,EAAWf,EAAOa,EAAOX,WAAWD,EAAI,IACxCe,EAAWhB,EAAOa,EAAOX,WAAWD,EAAI,IACxCgB,EAAWjB,EAAOa,EAAOX,WAAWD,EAAI,IACxCqB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CACX,CEVwBE,CAAOnD,GACvB,OAAOmC,EAAUK,EAASN,EAC9B,CAEI,MAAO,CAAEO,QAAQ,EAAMzC,KAAAA,EAE/B,EACMmC,EAAY,SAACnC,EAAMkC,GACrB,MACS,SADDA,EAEIlC,aAAgBE,KAETF,EAIA,IAAIE,KAAK,CAACF,IAIjBA,aAAgBO,YAETP,EAIAA,EAAKU,MAG5B,ED1DM0C,EAAYC,OAAOC,aAAa,IA4B/B,SAASC,IACZ,OAAO,IAAIC,gBAAgB,CACvBC,UAASA,SAACC,EAAQC,IFmBnB,SAA8BD,EAAQ5C,GACrCb,GAAkByD,EAAO1D,gBAAgBE,KAClCwD,EAAO1D,KACT4D,cACAC,KAAKtC,GACLsC,KAAK/C,GAELR,IACJoD,EAAO1D,gBAAgBO,aAAeC,EAAOkD,EAAO1D,OAC9Cc,EAASS,EAAQmC,EAAO1D,OAEnCW,EAAa+C,GAAQ,GAAO,SAAAI,GACnBjE,IACDA,EAAe,IAAIkE,aAEvBjD,EAASjB,EAAamE,OAAOF,GACjC,GACJ,CEnCYG,CAAqBP,GAAQ,SAAAzB,GACzB,IACIiC,EADEC,EAAgBlC,EAAcM,OAGpC,GAAI4B,EAAgB,IAChBD,EAAS,IAAI1C,WAAW,GACxB,IAAI4C,SAASF,EAAOxD,QAAQ2D,SAAS,EAAGF,QAEvC,GAAIA,EAAgB,MAAO,CAC5BD,EAAS,IAAI1C,WAAW,GACxB,IAAM8C,EAAO,IAAIF,SAASF,EAAOxD,QACjC4D,EAAKD,SAAS,EAAG,KACjBC,EAAKC,UAAU,EAAGJ,EACtB,KACK,CACDD,EAAS,IAAI1C,WAAW,GACxB,IAAM8C,EAAO,IAAIF,SAASF,EAAOxD,QACjC4D,EAAKD,SAAS,EAAG,KACjBC,EAAKE,aAAa,EAAGC,OAAON,GAChC,CAEIT,EAAO1D,MAA+B,iBAAhB0D,EAAO1D,OAC7BkE,EAAO,IAAM,KAEjBP,EAAWe,QAAQR,GACnBP,EAAWe,QAAQzC,EACvB,GACJ,GAER,CAEA,SAAS0C,EAAYC,GACjB,OAAOA,EAAOC,QAAO,SAACC,EAAKC,GAAK,OAAKD,EAAMC,EAAMxC,MAAM,GAAE,EAC7D,CACA,SAASyC,EAAaJ,EAAQK,GAC1B,GAAIL,EAAO,GAAGrC,SAAW0C,EACrB,OAAOL,EAAOM,QAIlB,IAFA,IAAMxE,EAAS,IAAIc,WAAWyD,GAC1BE,EAAI,EACCtD,EAAI,EAAGA,EAAIoD,EAAMpD,IACtBnB,EAAOmB,GAAK+C,EAAO,GAAGO,KAClBA,IAAMP,EAAO,GAAGrC,SAChBqC,EAAOM,QACPC,EAAI,GAMZ,OAHIP,EAAOrC,QAAU4C,EAAIP,EAAO,GAAGrC,SAC/BqC,EAAO,GAAKA,EAAO,GAAGQ,MAAMD,IAEzBzE,CACX,CE/EO,SAAS2E,EAAQ5E,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIb,KAAOyF,EAAQlF,UACtBM,EAAIb,GAAOyF,EAAQlF,UAAUP,GAE/B,OAAOa,CACT,CAhBkB6E,CAAM7E,EACxB,CA0BA4E,EAAQlF,UAAUoF,GAClBF,EAAQlF,UAAUqF,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,GACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACT,EAYAN,EAAQlF,UAAU2F,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,UACjB,CAIA,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACT,EAYAN,EAAQlF,UAAU4F,IAClBV,EAAQlF,UAAU+F,eAClBb,EAAQlF,UAAUgG,mBAClBd,EAAQlF,UAAUiG,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAGjC,GAAKK,UAAU1D,OAEjB,OADAoD,KAAKC,WAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,WAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAU1D,OAEjB,cADOoD,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI9D,EAAI,EAAGA,EAAIyE,EAAU/D,OAAQV,IAEpC,IADAwE,EAAKC,EAAUzE,MACJ6D,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO1E,EAAG,GACpB,KACF,CASF,OAJyB,IAArByE,EAAU/D,eACLoD,KAAKC,WAAW,IAAMH,GAGxBE,IACT,EAUAN,EAAQlF,UAAUqG,KAAO,SAASf,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAU1D,OAAS,GACpC+D,EAAYX,KAAKC,WAAW,IAAMH,GAE7B5D,EAAI,EAAGA,EAAIoE,UAAU1D,OAAQV,IACpC4E,EAAK5E,EAAI,GAAKoE,UAAUpE,GAG1B,GAAIyE,EAEG,CAAIzE,EAAI,EAAb,IAAK,IAAWkB,GADhBuD,EAAYA,EAAUlB,MAAM,IACI7C,OAAQV,EAAIkB,IAAOlB,EACjDyE,EAAUzE,GAAGmE,MAAML,KAAMc,EADKlE,CAKlC,OAAOoD,IACT,EAGAN,EAAQlF,UAAUwG,aAAetB,EAAQlF,UAAUqG,KAUnDnB,EAAQlF,UAAUyG,UAAY,SAASnB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAC9BD,KAAKC,WAAW,IAAMH,IAAU,EACzC,EAUAJ,EAAQlF,UAAU0G,aAAe,SAASpB,GACxC,QAAUE,KAAKiB,UAAUnB,GAAOlD,MAClC,ECxKO,IAAMuE,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKzG,GAAc,IAAA0G,IAAAA,EAAAlB,UAAA1D,OAAN6E,MAAIV,MAAAS,EAAAA,EAAAA,OAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJD,EAAIC,EAAApB,GAAAA,UAAAoB,GAC7B,OAAOD,EAAKvC,QAAO,SAACC,EAAKwC,GAIrB,OAHI7G,EAAI8G,eAAeD,KACnBxC,EAAIwC,GAAK7G,EAAI6G,IAEVxC,CACV,GAAE,CAAE,EACT,CAEA,IAAM0C,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsBpH,EAAKqH,GACnCA,EAAKC,iBACLtH,EAAIuH,aAAeR,EAAmBS,KAAKR,GAC3ChH,EAAIyH,eAAiBP,EAAqBM,KAAKR,KAG/ChH,EAAIuH,aAAeP,EAAWC,WAAWO,KAAKR,GAC9ChH,EAAIyH,eAAiBT,EAAWG,aAAaK,KAAKR,GAE1D,CClB8C,ICAzBU,EDCfC,WAAcC,GAAAC,EAAAF,EAAAC,GAAA,IAAAE,EAAAC,EAAAJ,GAChB,SAAAA,EAAYK,EAAQC,EAAaC,GAAS,IAAAC,EAIT,OAJSC,OAAAT,IACtCQ,EAAAL,EAAAlI,UAAMoI,IACDC,YAAcA,EACnBE,EAAKD,QAAUA,EACfC,EAAK7I,KAAO,iBAAiB6I,CACjC,CAAC,OAAAE,EAAAV,EAAA,EAAAW,EANwBC,QAQhBC,WAASC,GAAAZ,EAAAW,EAAAC,GAAA,IAAAC,EAAAX,EAAAS,GAOlB,SAAAA,EAAYnB,GAAM,IAAAsB,EAMY,OANZP,OAAAI,IACdG,EAAAD,EAAA9I,KAAAsF,OACK0D,UAAW,EAChBxB,EAAqByB,EAAAF,GAAOtB,GAC5BsB,EAAKtB,KAAOA,EACZsB,EAAKG,MAAQzB,EAAKyB,MAClBH,EAAKI,OAAS1B,EAAK0B,OAAOJ,CAC9B,CAiHC,OAhHDN,EAAAG,EAAA,CAAA,CAAArJ,IAAA,UAAA6J,MASA,SAAQhB,EAAQC,EAAaC,GAEzB,OADAe,EAAAC,EAAAV,EAAA9I,gCAAAE,KAAAsF,KAAmB,QAAS,IAAIyC,EAAeK,EAAQC,EAAaC,IAC7DhD,IACX,GACA,CAAA/F,IAAA,OAAA6J,MAGA,WAGI,OAFA9D,KAAKiE,WAAa,UAClBjE,KAAKkE,SACElE,IACX,GACA,CAAA/F,IAAA,QAAA6J,MAGA,WAKI,MAJwB,YAApB9D,KAAKiE,YAAgD,SAApBjE,KAAKiE,aACtCjE,KAAKmE,UACLnE,KAAKoE,WAEFpE,IACX,GACA,CAAA/F,IAAA,OAAA6J,MAKA,SAAKO,GACuB,SAApBrE,KAAKiE,YACLjE,KAAKsE,MAAMD,EAKnB,GACA,CAAApK,IAAA,SAAA6J,MAKA,WACI9D,KAAKiE,WAAa,OAClBjE,KAAK0D,UAAW,EAChBK,EAAAC,EAAAV,EAAA9I,WAAA,eAAAwF,MAAAtF,KAAAsF,KAAmB,OACvB,GACA,CAAA/F,IAAA,SAAA6J,MAMA,SAAOzJ,GACH,IAAM0D,EAAS1B,EAAahC,EAAM2F,KAAK6D,OAAOtH,YAC9CyD,KAAKuE,SAASxG,EAClB,GACA,CAAA9D,IAAA,WAAA6J,MAKA,SAAS/F,GACLgG,EAAAC,EAAAV,EAAA9I,WAAA,eAAAwF,MAAAtF,KAAAsF,KAAmB,SAAUjC,EACjC,GACA,CAAA9D,IAAA,UAAA6J,MAKA,SAAQU,GACJxE,KAAKiE,WAAa,SAClBF,EAAAC,EAAAV,EAAA9I,WAAA,eAAAwF,MAAAtF,KAAAsF,KAAmB,QAASwE,EAChC,GACA,CAAAvK,IAAA,QAAA6J,MAKA,SAAMW,GAAW,GAAC,CAAAxK,IAAA,YAAA6J,MAClB,SAAUY,GAAoB,IAAZd,EAAKtD,UAAA1D,OAAA,QAAA+H,IAAArE,UAAA,GAAAA,UAAA,GAAG,CAAA,EACtB,OAAQoE,EACJ,MACA1E,KAAK4E,YACL5E,KAAK6E,QACL7E,KAAKmC,KAAK2C,KACV9E,KAAK+E,OAAOnB,EACpB,GAAC,CAAA3J,IAAA,YAAA6J,MACD,WACI,IAAMkB,EAAWhF,KAAKmC,KAAK6C,SAC3B,OAAkC,IAA3BA,EAASC,QAAQ,KAAcD,EAAW,IAAMA,EAAW,GACtE,GAAC,CAAA/K,IAAA,QAAA6J,MACD,WACI,OAAI9D,KAAKmC,KAAK+C,OACRlF,KAAKmC,KAAKgD,QAAUC,OAA0B,MAAnBpF,KAAKmC,KAAK+C,QACjClF,KAAKmC,KAAKgD,QAAqC,KAA3BC,OAAOpF,KAAKmC,KAAK+C,OACpC,IAAMlF,KAAKmC,KAAK+C,KAGhB,EAEf,GAAC,CAAAjL,IAAA,SAAA6J,MACD,SAAOF,GACH,IAAMyB,EEjIP,SAAgBvK,GACnB,IAAIwK,EAAM,GACV,IAAK,IAAIpJ,KAAKpB,EACNA,EAAI8G,eAAe1F,KACfoJ,EAAI1I,SACJ0I,GAAO,KACXA,GAAOC,mBAAmBrJ,GAAK,IAAMqJ,mBAAmBzK,EAAIoB,KAGpE,OAAOoJ,CACX,CFuH6BjH,CAAOuF,GAC5B,OAAOyB,EAAazI,OAAS,IAAMyI,EAAe,EACtD,KAAC/B,CAAA,EA/H0B5D,GCVzB8F,EAAW,mEAAmE9J,MAAM,IAAKkB,EAAS,GAAI6I,EAAM,CAAA,EAC9GC,EAAO,EAAGxJ,EAAI,EAQX,SAASmC,EAAOsH,GACnB,IAAIxH,EAAU,GACd,GACIA,EAAUqH,EAASG,EAAM/I,GAAUuB,EACnCwH,EAAMC,KAAKC,MAAMF,EAAM/I,SAClB+I,EAAM,GACf,OAAOxH,CACX,CAqBO,SAAS2H,IACZ,IAAMC,EAAM1H,GAAQ,IAAI2H,MACxB,OAAID,IAAQvD,GACDkD,EAAO,EAAGlD,EAAOuD,GACrBA,EAAM,IAAM1H,EAAOqH,IAC9B,CAIA,KAAOxJ,EAAIU,EAAQV,IACfuJ,EAAID,EAAStJ,IAAMA,EEhDvB,IAAI4H,IAAQ,EACZ,IACIA,GAAkC,oBAAnBmC,gBACX,oBAAqB,IAAIA,cACjC,CACA,MAAOC,GAEH,CAEG,IAAMC,GAAUrC,GCPhB,SAASsC,GAAIjE,GAChB,IAAMkE,EAAUlE,EAAKkE,QAErB,IACI,GAAI,oBAAuBJ,kBAAoBI,GAAWF,IACtD,OAAO,IAAIF,cAEnB,CACA,MAAOK,GAAK,CACZ,IAAKD,EACD,IACI,OAAO,IAAIvE,EAAW,CAAC,UAAUyE,OAAO,UAAUC,KAAK,OAAM,oBACjE,CACA,MAAOF,GAAK,CAEpB,CCXA,SAASG,KAAU,CACnB,IAAMC,GAIK,MAHK,IAAIT,GAAe,CAC3BI,SAAS,IAEMM,aAEVC,YAAOC,GAAAlE,EAAAiE,EAAAC,GAAA,IAAAjE,EAAAC,EAAA+D,GAOhB,SAAAA,EAAYzE,GAAM,IAAAc,EAGd,GAHcC,OAAA0D,IACd3D,EAAAL,EAAAlI,UAAMyH,IACD2E,SAAU,EACS,oBAAbC,SAA0B,CACjC,IAAMC,EAAQ,WAAaD,SAASE,SAChC/B,EAAO6B,SAAS7B,KAEfA,IACDA,EAAO8B,EAAQ,MAAQ,MAE3B/D,EAAKiE,GACoB,oBAAbH,UACJ5E,EAAK6C,WAAa+B,SAAS/B,UAC3BE,IAAS/C,EAAK+C,IAC1B,CAIA,IAAMiC,EAAchF,GAAQA,EAAKgF,YAIhC,OAHDlE,EAAK/H,eAAiBwL,KAAYS,EAC9BlE,EAAKd,KAAKiF,kBACVnE,EAAKoE,eAAYC,GACpBrE,CACL,CAgLC,OAhLAE,EAAAyD,EAAA,CAAA,CAAA3M,IAAA,OAAAsN,IACD,WACI,MAAO,SACX,GACA,CAAAtN,IAAA,SAAA6J,MAMA,WACI9D,KAAKwH,MACT,GACA,CAAAvN,IAAA,QAAA6J,MAMA,SAAMW,GAAS,IAAAhB,EAAAzD,KACXA,KAAKiE,WAAa,UAClB,IAAMwD,EAAQ,WACVhE,EAAKQ,WAAa,SAClBQ,KAEJ,GAAIzE,KAAK8G,UAAY9G,KAAK0D,SAAU,CAChC,IAAIgE,EAAQ,EACR1H,KAAK8G,UACLY,IACA1H,KAAKG,KAAK,gBAAgB,aACpBuH,GAASD,GACf,KAECzH,KAAK0D,WACNgE,IACA1H,KAAKG,KAAK,SAAS,aACbuH,GAASD,GACf,IAER,MAEIA,GAER,GACA,CAAAxN,IAAA,OAAA6J,MAKA,WACI9D,KAAK8G,SAAU,EACf9G,KAAK2H,SACL3H,KAAKgB,aAAa,OACtB,GACA,CAAA/G,IAAA,SAAA6J,MAKA,SAAOzJ,GAAM,IAAAuN,EAAA5H,MVpFK,SAAC6H,EAAgBtL,GAGnC,IAFA,IAAMuL,EAAiBD,EAAenM,MAAM+B,GACtC4G,EAAU,GACPnI,EAAI,EAAGA,EAAI4L,EAAelL,OAAQV,IAAK,CAC5C,IAAM6L,EAAgB1L,EAAayL,EAAe5L,GAAIK,GAEtD,GADA8H,EAAQnE,KAAK6H,GACc,UAAvBA,EAAc3N,KACd,KAER,CACA,OAAOiK,CACX,EUwFQ2D,CAAc3N,EAAM2F,KAAK6D,OAAOtH,YAAYvC,SAd3B,SAAC+D,GAMd,GAJI,YAAc6J,EAAK3D,YAA8B,SAAhBlG,EAAO3D,MACxCwN,EAAKK,SAGL,UAAYlK,EAAO3D,KAEnB,OADAwN,EAAKxD,QAAQ,CAAErB,YAAa,oCACrB,EAGX6E,EAAKrD,SAASxG,MAKd,WAAaiC,KAAKiE,aAElBjE,KAAK8G,SAAU,EACf9G,KAAKgB,aAAa,gBACd,SAAWhB,KAAKiE,YAChBjE,KAAKwH,OAKjB,GACA,CAAAvN,IAAA,UAAA6J,MAKA,WAAU,IAAAoE,EAAAlI,KACAmI,EAAQ,WACVD,EAAK5D,MAAM,CAAC,CAAElK,KAAM,YAEpB,SAAW4F,KAAKiE,WAChBkE,IAKAnI,KAAKG,KAAK,OAAQgI,EAE1B,GACA,CAAAlO,IAAA,QAAA6J,MAMA,SAAMO,GAAS,IAAA+D,EAAApI,KACXA,KAAK0D,UAAW,EVxJF,SAACW,EAASlJ,GAE5B,IAAMyB,EAASyH,EAAQzH,OACjBkL,EAAiB,IAAI/G,MAAMnE,GAC7ByL,EAAQ,EACZhE,EAAQrK,SAAQ,SAAC+D,EAAQ7B,GAErBlB,EAAa+C,GAAQ,GAAO,SAAAzB,GACxBwL,EAAe5L,GAAKI,IACd+L,IAAUzL,GACZzB,EAAS2M,EAAetB,KAAK/I,GAErC,GACJ,GACJ,CU2IQ6K,CAAcjE,GAAS,SAAChK,GACpB+N,EAAKG,QAAQlO,GAAM,WACf+N,EAAK1E,UAAW,EAChB0E,EAAKpH,aAAa,QACtB,GACJ,GACJ,GACA,CAAA/G,IAAA,MAAA6J,MAKA,WACI,IAAMY,EAAS1E,KAAKmC,KAAKgD,OAAS,QAAU,OACtCvB,EAAQ5D,KAAK4D,OAAS,GAQ5B,OANI,IAAU5D,KAAKmC,KAAKqG,oBACpB5E,EAAM5D,KAAKmC,KAAKsG,gBAAkB3C,KAEjC9F,KAAK9E,gBAAmB0I,EAAM8E,MAC/B9E,EAAM+E,IAAM,GAET3I,KAAK4I,UAAUlE,EAAQd,EAClC,GACA,CAAA3J,IAAA,UAAA6J,MAMA,WAAmB,IAAX3B,EAAI7B,UAAA1D,OAAA,QAAA+H,IAAArE,UAAA,GAAAA,UAAA,GAAG,CAAA,EAEX,OADAuI,EAAc1G,EAAM,CAAE+E,GAAIlH,KAAKkH,GAAIG,UAAWrH,KAAKqH,WAAarH,KAAKmC,MAC9D,IAAI2G,GAAQ9I,KAAK+I,MAAO5G,EACnC,GACA,CAAAlI,IAAA,UAAA6J,MAOA,SAAQzJ,EAAM0F,GAAI,IAAAiJ,EAAAhJ,KACRiJ,EAAMjJ,KAAKkJ,QAAQ,CACrBC,OAAQ,OACR9O,KAAMA,IAEV4O,EAAIrJ,GAAG,UAAWG,GAClBkJ,EAAIrJ,GAAG,SAAS,SAACwJ,EAAWpG,GACxBgG,EAAKK,QAAQ,iBAAkBD,EAAWpG,EAC9C,GACJ,GACA,CAAA/I,IAAA,SAAA6J,MAKA,WAAS,IAAAwF,EAAAtJ,KACCiJ,EAAMjJ,KAAKkJ,UACjBD,EAAIrJ,GAAG,OAAQI,KAAKuJ,OAAOjH,KAAKtC,OAChCiJ,EAAIrJ,GAAG,SAAS,SAACwJ,EAAWpG,GACxBsG,EAAKD,QAAQ,iBAAkBD,EAAWpG,EAC9C,IACAhD,KAAKwJ,QAAUP,CACnB,KAACrC,CAAA,EA9MwBtD,GAgNhBwF,YAAOvF,GAAAZ,EAAAmG,EAAAvF,GAAA,IAAAC,EAAAX,EAAAiG,GAOhB,SAAAA,EAAYC,EAAK5G,GAAM,IAAAsH,EAOL,OAPKvG,OAAA4F,GAEnB5G,EAAqByB,EADrB8F,EAAAjG,EAAA9I,KAAAsF,OAC4BmC,GAC5BsH,EAAKtH,KAAOA,EACZsH,EAAKN,OAAShH,EAAKgH,QAAU,MAC7BM,EAAKV,IAAMA,EACXU,EAAKpP,UAAOsK,IAAcxC,EAAK9H,KAAO8H,EAAK9H,KAAO,KAClDoP,EAAK5P,SAAS4P,CAClB,CA8HC,OA7HDtG,EAAA2F,EAAA,CAAA,CAAA7O,IAAA,SAAA6J,MAKA,WAAS,IACD4F,EADCC,EAAA3J,KAECmC,EAAOZ,EAAKvB,KAAKmC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAKkE,UAAYrG,KAAKmC,KAAK+E,GAC3B,IAAM0C,EAAO5J,KAAK4J,IAAM,IAAI3D,GAAe9D,GAC3C,IACIyH,EAAIC,KAAK7J,KAAKmJ,OAAQnJ,KAAK+I,KAAK,GAChC,IACI,GAAI/I,KAAKmC,KAAK2H,aAEV,IAAK,IAAI5N,KADT0N,EAAIG,uBAAyBH,EAAIG,uBAAsB,GACzC/J,KAAKmC,KAAK2H,aAChB9J,KAAKmC,KAAK2H,aAAalI,eAAe1F,IACtC0N,EAAII,iBAAiB9N,EAAG8D,KAAKmC,KAAK2H,aAAa5N,GAI/D,CACA,MAAOoK,GAAK,CACZ,GAAI,SAAWtG,KAAKmJ,OAChB,IACIS,EAAII,iBAAiB,eAAgB,2BACzC,CACA,MAAO1D,GAAK,CAEhB,IACIsD,EAAII,iBAAiB,SAAU,MACnC,CACA,MAAO1D,GAAK,CACmB,QAA9BoD,EAAK1J,KAAKmC,KAAKkF,iBAA8B,IAAPqC,GAAyBA,EAAGO,WAAWL,GAE1E,oBAAqBA,IACrBA,EAAIxC,gBAAkBpH,KAAKmC,KAAKiF,iBAEhCpH,KAAKmC,KAAK+H,iBACVN,EAAIO,QAAUnK,KAAKmC,KAAK+H,gBAE5BN,EAAIQ,mBAAqB,WACrB,IAAIV,EACmB,IAAnBE,EAAI3F,aAC2B,QAA9ByF,EAAKC,EAAKxH,KAAKkF,iBAA8B,IAAPqC,GAAyBA,EAAGW,aAAaT,IAEhF,IAAMA,EAAI3F,aAEV,MAAQ2F,EAAIU,QAAU,OAASV,EAAIU,OACnCX,EAAKY,SAKLZ,EAAKtH,cAAa,WACdsH,EAAKN,QAA8B,iBAAfO,EAAIU,OAAsBV,EAAIU,OAAS,EAC9D,GAAE,KAGXV,EAAIY,KAAKxK,KAAK3F,KACjB,CACD,MAAOiM,GAOH,YAHAtG,KAAKqC,cAAa,WACdsH,EAAKN,QAAQ/C,EAChB,GAAE,EAEP,CACwB,oBAAbmE,WACPzK,KAAK0K,MAAQ5B,EAAQ6B,gBACrB7B,EAAQ8B,SAAS5K,KAAK0K,OAAS1K,KAEvC,GACA,CAAA/F,IAAA,UAAA6J,MAKA,SAAQoC,GACJlG,KAAKgB,aAAa,QAASkF,EAAKlG,KAAK4J,KACrC5J,KAAK6K,SAAQ,EACjB,GACA,CAAA5Q,IAAA,UAAA6J,MAKA,SAAQgH,GACJ,QAAI,IAAuB9K,KAAK4J,KAAO,OAAS5J,KAAK4J,IAArD,CAIA,GADA5J,KAAK4J,IAAIQ,mBAAqB3D,GAC1BqE,EACA,IACI9K,KAAK4J,IAAImB,OACb,CACA,MAAOzE,GAAK,CAEQ,oBAAbmE,iBACA3B,EAAQ8B,SAAS5K,KAAK0K,OAEjC1K,KAAK4J,IAAM,IAXX,CAYJ,GACA,CAAA3P,IAAA,SAAA6J,MAKA,WACI,IAAMzJ,EAAO2F,KAAK4J,IAAIoB,aACT,OAAT3Q,IACA2F,KAAKgB,aAAa,OAAQ3G,GAC1B2F,KAAKgB,aAAa,WAClBhB,KAAK6K,UAEb,GACA,CAAA5Q,IAAA,QAAA6J,MAKA,WACI9D,KAAK6K,SACT,KAAC/B,CAAA,EA7IwBpJ,GAsJ7B,GAPAoJ,GAAQ6B,cAAgB,EACxB7B,GAAQ8B,SAAW,CAAA,EAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,SAEvB,GAAgC,mBAArBrL,iBAAiC,CAE7CA,iBADyB,eAAgBiC,EAAa,WAAa,SAChCoJ,IAAe,EACtD,CAEJ,SAASA,KACL,IAAK,IAAIhP,KAAK4M,GAAQ8B,SACd9B,GAAQ8B,SAAShJ,eAAe1F,IAChC4M,GAAQ8B,SAAS1O,GAAG6O,OAGhC,CCpYO,IAAMI,GACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAEhE,SAAC3K,GAAE,OAAK0K,QAAQC,UAAUnN,KAAKwC,EAAG,EAGlC,SAACA,EAAI2B,GAAY,OAAKA,EAAa3B,EAAI,EAAE,EAG3C4K,GAAYxJ,EAAWwJ,WAAaxJ,EAAWyJ,aCJtDC,GAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACTC,YAAE/E,GAAAlE,EAAAiJ,EAAA/E,GAAA,IAAAjE,EAAAC,EAAA+I,GAOX,SAAAA,EAAYzJ,GAAM,IAAAc,EAE0B,OAF1BC,OAAA0I,IACd3I,EAAAL,EAAAlI,UAAMyH,IACDjH,gBAAkBiH,EAAKgF,YAAYlE,CAC5C,CAmIC,OAnIAE,EAAAyI,EAAA,CAAA,CAAA3R,IAAA,OAAAsN,IACD,WACI,MAAO,WACX,GAAC,CAAAtN,IAAA,SAAA6J,MACD,WACI,GAAK9D,KAAK6L,QAAV,CAIA,IAAM9C,EAAM/I,KAAK+I,MACX+C,EAAY9L,KAAKmC,KAAK2J,UAEtB3J,EAAOqJ,GACP,CAAA,EACAjK,EAAKvB,KAAKmC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMnC,KAAKmC,KAAK2H,eACV3H,EAAK4J,QAAU/L,KAAKmC,KAAK2H,cAE7B,IACI9J,KAAKgM,GACyBR,GAIpB,IAAIF,GAAUvC,EAAK+C,EAAW3J,GAH9B2J,EACI,IAAIR,GAAUvC,EAAK+C,GACnB,IAAIR,GAAUvC,EAE/B,CACD,MAAO7C,GACH,OAAOlG,KAAKgB,aAAa,QAASkF,EACtC,CACAlG,KAAKgM,GAAGzP,WAAayD,KAAK6D,OAAOtH,WACjCyD,KAAKiM,mBAtBL,CAuBJ,GACA,CAAAhS,IAAA,oBAAA6J,MAKA,WAAoB,IAAAL,EAAAzD,KAChBA,KAAKgM,GAAGE,OAAS,WACTzI,EAAKtB,KAAKgK,WACV1I,EAAKuI,GAAGI,QAAQC,QAEpB5I,EAAKwE,UAETjI,KAAKgM,GAAGM,QAAU,SAACC,GAAU,OAAK9I,EAAKW,QAAQ,CAC3CrB,YAAa,8BACbC,QAASuJ,GACX,EACFvM,KAAKgM,GAAGQ,UAAY,SAACC,GAAE,OAAKhJ,EAAK8F,OAAOkD,EAAGpS,KAAK,EAChD2F,KAAKgM,GAAGU,QAAU,SAACpG,GAAC,OAAK7C,EAAK4F,QAAQ,kBAAmB/C,EAAE,CAC/D,GAAC,CAAArM,IAAA,QAAA6J,MACD,SAAMO,GAAS,IAAAuD,EAAA5H,KACXA,KAAK0D,UAAW,EAGhB,IADA,IAAAiJ,EAAAA,WAEI,IAAM5O,EAASsG,EAAQnI,GACjB0Q,EAAa1Q,IAAMmI,EAAQzH,OAAS,EAC1C5B,EAAa+C,EAAQ6J,EAAK1M,gBAAgB,SAACb,GAmBvC,IAGQuN,EAAKoE,GAAGxB,KAAKnQ,EAKrB,CACA,MAAOiM,GACP,CACIsG,GAGAzB,IAAS,WACLvD,EAAKlE,UAAW,EAChBkE,EAAK5G,aAAa,QACtB,GAAG4G,EAAKvF,aAEhB,KAzCKnG,EAAI,EAAGA,EAAImI,EAAQzH,OAAQV,IAAGyQ,GA2C3C,GAAC,CAAA1S,IAAA,UAAA6J,MACD,gBAC2B,IAAZ9D,KAAKgM,KACZhM,KAAKgM,GAAG7D,QACRnI,KAAKgM,GAAK,KAElB,GACA,CAAA/R,IAAA,MAAA6J,MAKA,WACI,IAAMY,EAAS1E,KAAKmC,KAAKgD,OAAS,MAAQ,KACpCvB,EAAQ5D,KAAK4D,OAAS,GAS5B,OAPI5D,KAAKmC,KAAKqG,oBACV5E,EAAM5D,KAAKmC,KAAKsG,gBAAkB3C,KAGjC9F,KAAK9E,iBACN0I,EAAM+E,IAAM,GAET3I,KAAK4I,UAAUlE,EAAQd,EAClC,GACA,CAAA3J,IAAA,QAAA6J,MAMA,WACI,QAASwH,EACb,KAACM,CAAA,EA7ImBtI,GCNXuJ,YAAEhG,GAAAlE,EAAAkK,EAAAhG,GAAA,IAAAjE,EAAAC,EAAAgK,GAAA,SAAAA,IAAA,OAAA3J,OAAA2J,GAAAjK,EAAAvC,MAAAL,KAAAM,UAAA,CAkEV,OAlEU6C,EAAA0J,EAAA,CAAA,CAAA5S,IAAA,OAAAsN,IACX,WACI,MAAO,cACX,GAAC,CAAAtN,IAAA,SAAA6J,MACD,WAAS,IAAAb,EAAAjD,KAEuB,mBAAjB8M,eAIX9M,KAAK+M,UAAY,IAAID,aAAa9M,KAAK4I,UAAU,SAAU5I,KAAKmC,KAAK6K,iBAAiBhN,KAAKiN,OAC3FjN,KAAK+M,UAAUG,OACVhP,MAAK,WACN+E,EAAKmB,SACT,IAAE,OACS,SAAC8B,GACRjD,EAAKoG,QAAQ,qBAAsBnD,EACvC,IAEAlG,KAAK+M,UAAUI,MAAMjP,MAAK,WACtB+E,EAAK8J,UAAUK,4BAA4BlP,MAAK,SAACmP,GAC7C,IAAMC,Eb8Df,SAAmCC,EAAYhR,GAC7CH,IACDA,EAAe,IAAIoR,aAEvB,IAAMvO,EAAS,GACXwO,EAAQ,EACRC,GAAkB,EAClBC,GAAW,EACf,OAAO,IAAI9P,gBAAgB,CACvBC,UAASA,SAACsB,EAAOpB,GAEb,IADAiB,EAAOiB,KAAKd,KACC,CACT,GAAc,IAAVqO,EAA+B,CAC/B,GAAIzO,EAAYC,GAAU,EACtB,MAEJ,IAAMV,EAASc,EAAaJ,EAAQ,GACpC0O,EAAkC,MAAV,IAAZpP,EAAO,IACnBmP,EAA6B,IAAZnP,EAAO,GAEpBkP,EADAC,EAAiB,IACT,EAEgB,MAAnBA,EACG,EAGA,CAEhB,MACK,GAAc,IAAVD,EAA2C,CAChD,GAAIzO,EAAYC,GAAU,EACtB,MAEJ,IAAM2O,EAAcvO,EAAaJ,EAAQ,GACzCyO,EAAiB,IAAIjP,SAASmP,EAAY7S,OAAQ6S,EAAY9R,WAAY8R,EAAYhR,QAAQiR,UAAU,GACxGJ,EAAQ,CACZ,MACK,GAAc,IAAVA,EAA2C,CAChD,GAAIzO,EAAYC,GAAU,EACtB,MAEJ,IAAM2O,EAAcvO,EAAaJ,EAAQ,GACnCN,EAAO,IAAIF,SAASmP,EAAY7S,OAAQ6S,EAAY9R,WAAY8R,EAAYhR,QAC5EkR,EAAInP,EAAKoP,UAAU,GACzB,GAAID,EAAIlI,KAAKoI,IAAI,EAAG,IAAW,EAAG,CAE9BhQ,EAAWe,QAAQ5E,GACnB,KACJ,CACAuT,EAAiBI,EAAIlI,KAAKoI,IAAI,EAAG,IAAMrP,EAAKoP,UAAU,GACtDN,EAAQ,CACZ,KACK,CACD,GAAIzO,EAAYC,GAAUyO,EACtB,MAEJ,IAAMrT,EAAOgF,EAAaJ,EAAQyO,GAClC1P,EAAWe,QAAQ1C,EAAasR,EAAWtT,EAAO+B,EAAaoB,OAAOnD,GAAOkC,IAC7EkR,EAAQ,CACZ,CACA,GAAuB,IAAnBC,GAAwBA,EAAiBH,EAAY,CACrDvP,EAAWe,QAAQ5E,GACnB,KACJ,CACJ,CACJ,GAER,CajIsC8T,CAA0B7I,OAAO8I,iBAAkBjL,EAAKY,OAAOtH,YAC/E4R,EAASd,EAAOe,SAASC,YAAYf,GAAegB,YACpDC,EAAgB3Q,IACtB2Q,EAAcH,SAASI,OAAOnB,EAAO3J,UACrCT,EAAKwL,OAASF,EAAc7K,SAASgL,aACxB,SAAPC,IACFR,EACKQ,OACAzQ,MAAK,SAAAjD,GAAqB,IAAlB2T,EAAI3T,EAAJ2T,KAAM9K,EAAK7I,EAAL6I,MACX8K,IAGJ3L,EAAKsB,SAAST,GACd6K,IACH,WACU,SAACzI,GACX,IAELyI,GACA,IAAM5Q,EAAS,CAAE3D,KAAM,QACnB6I,EAAKW,MAAM8E,MACX3K,EAAO1D,KAAI,WAAAkM,OAActD,EAAKW,MAAM8E,IAAO,OAE/CzF,EAAKwL,OAAOnK,MAAMvG,GAAQG,MAAK,WAAA,OAAM+E,EAAKgF,WAC9C,GACJ,IACJ,GAAC,CAAAhO,IAAA,QAAA6J,MACD,SAAMO,GAAS,IAAAZ,EAAAzD,KACXA,KAAK0D,UAAW,EAChB,IADsB,IAAAiJ,EAAAA,WAElB,IAAM5O,EAASsG,EAAQnI,GACjB0Q,EAAa1Q,IAAMmI,EAAQzH,OAAS,EAC1C6G,EAAKgL,OAAOnK,MAAMvG,GAAQG,MAAK,WACvB0O,GACAzB,IAAS,WACL1H,EAAKC,UAAW,EAChBD,EAAKzC,aAAa,QACtB,GAAGyC,EAAKpB,aAEhB,KAVKnG,EAAI,EAAGA,EAAImI,EAAQzH,OAAQV,IAAGyQ,GAY3C,GAAC,CAAA1S,IAAA,UAAA6J,MACD,WACI,IAAI4F,EACsB,QAAzBA,EAAK1J,KAAK+M,iBAA8B,IAAPrD,GAAyBA,EAAGvB,OAClE,KAAC0E,CAAA,EAlEmBvJ,GCAXuL,GAAa,CACtBC,UAAWlD,GACXmD,aAAclC,GACd/F,QAASF,ICaPoI,GAAK,sPACLC,GAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,GAAM5J,GAClB,IAAM6J,EAAM7J,EAAK8J,EAAI9J,EAAIL,QAAQ,KAAMqB,EAAIhB,EAAIL,QAAQ,MAC7C,GAANmK,IAAiB,GAAN9I,IACXhB,EAAMA,EAAI3I,UAAU,EAAGyS,GAAK9J,EAAI3I,UAAUyS,EAAG9I,GAAG+I,QAAQ,KAAM,KAAO/J,EAAI3I,UAAU2J,EAAGhB,EAAI1I,SAG9F,IADA,IAwBmBgH,EACbvJ,EAzBFiV,EAAIN,GAAGO,KAAKjK,GAAO,IAAKyD,EAAM,CAAE,EAAE7M,EAAI,GACnCA,KACH6M,EAAIkG,GAAM/S,IAAMoT,EAAEpT,IAAM,GAU5B,OARU,GAANkT,IAAiB,GAAN9I,IACXyC,EAAIyG,OAASL,EACbpG,EAAI0G,KAAO1G,EAAI0G,KAAK9S,UAAU,EAAGoM,EAAI0G,KAAK7S,OAAS,GAAGyS,QAAQ,KAAM,KACpEtG,EAAI2G,UAAY3G,EAAI2G,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9EtG,EAAI4G,SAAU,GAElB5G,EAAI6G,UAIR,SAAmB9U,EAAKgK,GACpB,IAAM+K,EAAO,WAAYC,EAAQhL,EAAKuK,QAAQQ,EAAM,KAAKnU,MAAM,KACvC,KAApBoJ,EAAKrF,MAAM,EAAG,IAA6B,IAAhBqF,EAAKlI,QAChCkT,EAAMlP,OAAO,EAAG,GAEE,KAAlBkE,EAAKrF,OAAO,IACZqQ,EAAMlP,OAAOkP,EAAMlT,OAAS,EAAG,GAEnC,OAAOkT,CACX,CAboBF,CAAU7G,EAAKA,EAAU,MACzCA,EAAIgH,UAaenM,EAbUmF,EAAW,MAclC1O,EAAO,CAAA,EACbuJ,EAAMyL,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACA5V,EAAK4V,GAAMC,EAEnB,IACO7V,GAnBA0O,CACX,CClCaoH,IAAAA,YAAM5M,GAAAZ,EAAAwN,EAAA5M,GAAA,IAAAX,EAAAC,EAAAsN,GAOf,SAAAA,EAAYpH,GAAgB,IAAA9F,EAAXd,EAAI7B,UAAA1D,OAAA,QAAA+H,IAAArE,UAAA,GAAAA,UAAA,GAAG,CAAA,EAgGR,OAhGU4C,OAAAiN,IACtBlN,EAAAL,EAAAlI,KAAAsF,OACKzD,WLJoB,cKKzB0G,EAAKmN,YAAc,GACfrH,GAAO,WAAQsH,EAAYtH,KAC3B5G,EAAO4G,EACPA,EAAM,MAENA,GACAA,EAAMmG,GAAMnG,GACZ5G,EAAK6C,SAAW+D,EAAI0G,KACpBtN,EAAKgD,OAA0B,UAAjB4D,EAAI9B,UAAyC,QAAjB8B,EAAI9B,SAC9C9E,EAAK+C,KAAO6D,EAAI7D,KACZ6D,EAAInF,QACJzB,EAAKyB,MAAQmF,EAAInF,QAEhBzB,EAAKsN,OACVtN,EAAK6C,SAAWkK,GAAM/M,EAAKsN,MAAMA,MAErCvN,EAAqByB,EAAAV,GAAOd,GAC5Bc,EAAKkC,OACD,MAAQhD,EAAKgD,OACPhD,EAAKgD,OACe,oBAAb4B,UAA4B,WAAaA,SAASE,SAC/D9E,EAAK6C,WAAa7C,EAAK+C,OAEvB/C,EAAK+C,KAAOjC,EAAKkC,OAAS,MAAQ,MAEtClC,EAAK+B,SACD7C,EAAK6C,WACoB,oBAAb+B,SAA2BA,SAAS/B,SAAW,aAC/D/B,EAAKiC,KACD/C,EAAK+C,OACoB,oBAAb6B,UAA4BA,SAAS7B,KACvC6B,SAAS7B,KACTjC,EAAKkC,OACD,MACA,MAClBlC,EAAK4L,WAAa1M,EAAK0M,YAAc,CACjC,UACA,YACA,gBAEJ5L,EAAKmN,YAAc,GACnBnN,EAAKqN,cAAgB,EACrBrN,EAAKd,KAAO0G,EAAc,CACtB/D,KAAM,aACNyL,OAAO,EACPnJ,iBAAiB,EACjBoJ,SAAS,EACT/H,eAAgB,IAChBgI,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEf7D,iBAAkB,CAAE,EACpB8D,qBAAqB,GACtB3O,GACHc,EAAKd,KAAK2C,KACN7B,EAAKd,KAAK2C,KAAKuK,QAAQ,MAAO,KACzBpM,EAAKd,KAAKuO,iBAAmB,IAAM,IACb,iBAApBzN,EAAKd,KAAKyB,QACjBX,EAAKd,KAAKyB,MTrDf,SAAgBmN,GAGnB,IAFA,IAAIC,EAAM,CAAA,EACNC,EAAQF,EAAGrV,MAAM,KACZQ,EAAI,EAAGgV,EAAID,EAAMrU,OAAQV,EAAIgV,EAAGhV,IAAK,CAC1C,IAAIiV,EAAOF,EAAM/U,GAAGR,MAAM,KAC1BsV,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,GAC/D,CACA,OAAOH,CACX,CS6C8BxT,CAAOyF,EAAKd,KAAKyB,QAGvCX,EAAKoO,GAAK,KACVpO,EAAKqO,SAAW,KAChBrO,EAAKsO,aAAe,KACpBtO,EAAKuO,YAAc,KAEnBvO,EAAKwO,iBAAmB,KACQ,mBAArB5R,mBACHoD,EAAKd,KAAK2O,sBAIV7N,EAAKyO,0BAA4B,WACzBzO,EAAK8J,YAEL9J,EAAK8J,UAAUvM,qBACfyC,EAAK8J,UAAU5E,UAGvBtI,iBAAiB,eAAgBoD,EAAKyO,2BAA2B,IAE/C,cAAlBzO,EAAK+B,WACL/B,EAAK0O,qBAAuB,WACxB1O,EAAKmB,QAAQ,kBAAmB,CAC5BrB,YAAa,6BAGrBlD,iBAAiB,UAAWoD,EAAK0O,sBAAsB,KAG/D1O,EAAK4G,OAAO5G,CAChB,CAgeC,OA/dDE,EAAAgN,EAAA,CAAA,CAAAlW,IAAA,kBAAA6J,MAOA,SAAgBmJ,GACZ,IAAMrJ,EAAQiF,EAAc,CAAA,EAAI7I,KAAKmC,KAAKyB,OAE1CA,EAAMgO,IhBgCU,EgB9BhBhO,EAAMmJ,UAAYE,EAEdjN,KAAKqR,KACLzN,EAAM8E,IAAM1I,KAAKqR,IACrB,IAAMlP,EAAO0G,EAAc,GAAI7I,KAAKmC,KAAM,CACtCyB,MAAAA,EACAC,OAAQ7D,KACRgF,SAAUhF,KAAKgF,SACfG,OAAQnF,KAAKmF,OACbD,KAAMlF,KAAKkF,MACZlF,KAAKmC,KAAK6K,iBAAiBC,IAC9B,OAAO,IAAI4B,GAAW5B,GAAM9K,EAChC,GACA,CAAAlI,IAAA,OAAA6J,MAKA,WAAO,IACCiJ,EADDtJ,EAAAzD,KAEH,GAAIA,KAAKmC,KAAKsO,iBACVN,EAAO0B,wBACmC,IAA1C7R,KAAK6O,WAAW5J,QAAQ,aACxB8H,EAAY,gBAEX,IAAI,IAAM/M,KAAK6O,WAAWjS,OAK3B,YAHAoD,KAAKqC,cAAa,WACdoB,EAAKzC,aAAa,QAAS,0BAC9B,GAAE,GAIH+L,EAAY/M,KAAK6O,WAAW,EAChC,CACA7O,KAAKiE,WAAa,UAElB,IACI8I,EAAY/M,KAAK8R,gBAAgB/E,EACpC,CACD,MAAOzG,GAGH,OAFAtG,KAAK6O,WAAWtP,aAChBS,KAAK6J,MAET,CACAkD,EAAUlD,OACV7J,KAAK+R,aAAahF,EACtB,GACA,CAAA9S,IAAA,eAAA6J,MAKA,SAAaiJ,GAAW,IAAAnF,EAAA5H,KAChBA,KAAK+M,WACL/M,KAAK+M,UAAUvM,qBAGnBR,KAAK+M,UAAYA,EAEjBA,EACKnN,GAAG,QAASI,KAAKgS,QAAQ1P,KAAKtC,OAC9BJ,GAAG,SAAUI,KAAKuE,SAASjC,KAAKtC,OAChCJ,GAAG,QAASI,KAAKqJ,QAAQ/G,KAAKtC,OAC9BJ,GAAG,SAAS,SAACkD,GAAM,OAAK8E,EAAKxD,QAAQ,kBAAmBtB,KACjE,GACA,CAAA7I,IAAA,QAAA6J,MAMA,SAAMmJ,GAAM,IAAA/E,EAAAlI,KACJ+M,EAAY/M,KAAK8R,gBAAgB7E,GACjCgF,GAAS,EACb9B,EAAO0B,uBAAwB,EAC/B,IAAMK,EAAkB,WAChBD,IAEJlF,EAAUvC,KAAK,CAAC,CAAEpQ,KAAM,OAAQC,KAAM,WACtC0S,EAAU5M,KAAK,UAAU,SAACgS,GACtB,IAAIF,EAEJ,GAAI,SAAWE,EAAI/X,MAAQ,UAAY+X,EAAI9X,KAAM,CAG7C,GAFA6N,EAAKkK,WAAY,EACjBlK,EAAKlH,aAAa,YAAa+L,IAC1BA,EACD,OACJoD,EAAO0B,sBAAwB,cAAgB9E,EAAUE,KACzD/E,EAAK6E,UAAUtF,OAAM,WACbwK,GAEA,WAAa/J,EAAKjE,aAEtB4G,IACA3C,EAAK6J,aAAahF,GAClBA,EAAUvC,KAAK,CAAC,CAAEpQ,KAAM,aACxB8N,EAAKlH,aAAa,UAAW+L,GAC7BA,EAAY,KACZ7E,EAAKkK,WAAY,EACjBlK,EAAKmK,QACT,GACJ,KACK,CACD,IAAMnM,EAAM,IAAI7C,MAAM,eAEtB6C,EAAI6G,UAAYA,EAAUE,KAC1B/E,EAAKlH,aAAa,eAAgBkF,EACtC,CACJ,MAEJ,SAASoM,IACDL,IAGJA,GAAS,EACTpH,IACAkC,EAAU5E,QACV4E,EAAY,KAChB,CAEA,IAAML,EAAU,SAACxG,GACb,IAAMqM,EAAQ,IAAIlP,MAAM,gBAAkB6C,GAE1CqM,EAAMxF,UAAYA,EAAUE,KAC5BqF,IACApK,EAAKlH,aAAa,eAAgBuR,IAEtC,SAASC,IACL9F,EAAQ,mBACZ,CAEA,SAASJ,IACLI,EAAQ,gBACZ,CAEA,SAAS+F,EAAUC,GACX3F,GAAa2F,EAAGzF,OAASF,EAAUE,MACnCqF,GAER,CAEA,IAAMzH,EAAU,WACZkC,EAAUxM,eAAe,OAAQ2R,GACjCnF,EAAUxM,eAAe,QAASmM,GAClCK,EAAUxM,eAAe,QAASiS,GAClCtK,EAAK9H,IAAI,QAASkM,GAClBpE,EAAK9H,IAAI,YAAaqS,IAE1B1F,EAAU5M,KAAK,OAAQ+R,GACvBnF,EAAU5M,KAAK,QAASuM,GACxBK,EAAU5M,KAAK,QAASqS,GACxBxS,KAAKG,KAAK,QAASmM,GACnBtM,KAAKG,KAAK,YAAasS,IACwB,IAA3CzS,KAAKsR,SAASrM,QAAQ,iBACb,iBAATgI,EAEAjN,KAAKqC,cAAa,WACT4P,GACDlF,EAAUlD,MAEjB,GAAE,KAGHkD,EAAUlD,MAElB,GACA,CAAA5P,IAAA,SAAA6J,MAKA,WAOI,GANA9D,KAAKiE,WAAa,OAClBkM,EAAO0B,sBAAwB,cAAgB7R,KAAK+M,UAAUE,KAC9DjN,KAAKgB,aAAa,QAClBhB,KAAKqS,QAGD,SAAWrS,KAAKiE,YAAcjE,KAAKmC,KAAKqO,QAGxC,IAFA,IAAItU,EAAI,EACFgV,EAAIlR,KAAKsR,SAAS1U,OACjBV,EAAIgV,EAAGhV,IACV8D,KAAK2S,MAAM3S,KAAKsR,SAASpV,GAGrC,GACA,CAAAjC,IAAA,WAAA6J,MAKA,SAAS/F,GACL,GAAI,YAAciC,KAAKiE,YACnB,SAAWjE,KAAKiE,YAChB,YAAcjE,KAAKiE,WAKnB,OAJAjE,KAAKgB,aAAa,SAAUjD,GAE5BiC,KAAKgB,aAAa,aAClBhB,KAAK4S,mBACG7U,EAAO3D,MACX,IAAK,OACD4F,KAAK6S,YAAYC,KAAK5D,MAAMnR,EAAO1D,OACnC,MACJ,IAAK,OACD2F,KAAK+S,WAAW,QAChB/S,KAAKgB,aAAa,QAClBhB,KAAKgB,aAAa,QAClB,MACJ,IAAK,QACD,IAAMkF,EAAM,IAAI7C,MAAM,gBAEtB6C,EAAI8M,KAAOjV,EAAO1D,KAClB2F,KAAKqJ,QAAQnD,GACb,MACJ,IAAK,UACDlG,KAAKgB,aAAa,OAAQjD,EAAO1D,MACjC2F,KAAKgB,aAAa,UAAWjD,EAAO1D,MAMpD,GACA,CAAAJ,IAAA,cAAA6J,MAMA,SAAYzJ,GACR2F,KAAKgB,aAAa,YAAa3G,GAC/B2F,KAAKqR,GAAKhX,EAAKqO,IACf1I,KAAK+M,UAAUnJ,MAAM8E,IAAMrO,EAAKqO,IAChC1I,KAAKsR,SAAWtR,KAAKiT,eAAe5Y,EAAKiX,UACzCtR,KAAKuR,aAAelX,EAAKkX,aACzBvR,KAAKwR,YAAcnX,EAAKmX,YACxBxR,KAAKuN,WAAalT,EAAKkT,WACvBvN,KAAKiI,SAED,WAAajI,KAAKiE,YAEtBjE,KAAK4S,kBACT,GACA,CAAA3Y,IAAA,mBAAA6J,MAKA,WAAmB,IAAAsE,EAAApI,KACfA,KAAKuC,eAAevC,KAAKyR,kBACzBzR,KAAKyR,iBAAmBzR,KAAKqC,cAAa,WACtC+F,EAAKhE,QAAQ,eAChB,GAAEpE,KAAKuR,aAAevR,KAAKwR,aACxBxR,KAAKmC,KAAKgK,WACVnM,KAAKyR,iBAAiBpF,OAE9B,GACA,CAAApS,IAAA,UAAA6J,MAKA,WACI9D,KAAKoQ,YAAYxP,OAAO,EAAGZ,KAAKsQ,eAIhCtQ,KAAKsQ,cAAgB,EACjB,IAAMtQ,KAAKoQ,YAAYxT,OACvBoD,KAAKgB,aAAa,SAGlBhB,KAAKqS,OAEb,GACA,CAAApY,IAAA,QAAA6J,MAKA,WACI,GAAI,WAAa9D,KAAKiE,YAClBjE,KAAK+M,UAAUrJ,WACd1D,KAAKoS,WACNpS,KAAKoQ,YAAYxT,OAAQ,CACzB,IAAMyH,EAAUrE,KAAKkT,qBACrBlT,KAAK+M,UAAUvC,KAAKnG,GAGpBrE,KAAKsQ,cAAgBjM,EAAQzH,OAC7BoD,KAAKgB,aAAa,QACtB,CACJ,GACA,CAAA/G,IAAA,qBAAA6J,MAMA,WAII,KAH+B9D,KAAKuN,YACR,YAAxBvN,KAAK+M,UAAUE,MACfjN,KAAKoQ,YAAYxT,OAAS,GAE1B,OAAOoD,KAAKoQ,YAGhB,IADA,IZtZmBtV,EYsZfqY,EAAc,EACTjX,EAAI,EAAGA,EAAI8D,KAAKoQ,YAAYxT,OAAQV,IAAK,CAC9C,IAAM7B,EAAO2F,KAAKoQ,YAAYlU,GAAG7B,KAIjC,GAHIA,IACA8Y,GZzZO,iBADIrY,EY0ZeT,GZnZ1C,SAAoBiL,GAEhB,IADA,IAAI8N,EAAI,EAAGxW,EAAS,EACXV,EAAI,EAAGgV,EAAI5L,EAAI1I,OAAQV,EAAIgV,EAAGhV,KACnCkX,EAAI9N,EAAInJ,WAAWD,IACX,IACJU,GAAU,EAELwW,EAAI,KACTxW,GAAU,EAELwW,EAAI,OAAUA,GAAK,MACxBxW,GAAU,GAGVV,IACAU,GAAU,GAGlB,OAAOA,CACX,CAxBeyW,CAAWvY,GAGf8K,KAAK0N,KAPQ,MAOFxY,EAAIiB,YAAcjB,EAAIwE,QYuZ5BpD,EAAI,GAAKiX,EAAcnT,KAAKuN,WAC5B,OAAOvN,KAAKoQ,YAAY3Q,MAAM,EAAGvD,GAErCiX,GAAe,CACnB,CACA,OAAOnT,KAAKoQ,WAChB,GACA,CAAAnW,IAAA,QAAA6J,MAQA,SAAMqO,EAAKoB,EAASxT,GAEhB,OADAC,KAAK+S,WAAW,UAAWZ,EAAKoB,EAASxT,GAClCC,IACX,GAAC,CAAA/F,IAAA,OAAA6J,MACD,SAAKqO,EAAKoB,EAASxT,GAEf,OADAC,KAAK+S,WAAW,UAAWZ,EAAKoB,EAASxT,GAClCC,IACX,GACA,CAAA/F,IAAA,aAAA6J,MASA,SAAW1J,EAAMC,EAAMkZ,EAASxT,GAS5B,GARI,mBAAsB1F,IACtB0F,EAAK1F,EACLA,OAAOsK,GAEP,mBAAsB4O,IACtBxT,EAAKwT,EACLA,EAAU,MAEV,YAAcvT,KAAKiE,YAAc,WAAajE,KAAKiE,WAAvD,EAGAsP,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,IAAMzV,EAAS,CACX3D,KAAMA,EACNC,KAAMA,EACNkZ,QAASA,GAEbvT,KAAKgB,aAAa,eAAgBjD,GAClCiC,KAAKoQ,YAAYlQ,KAAKnC,GAClBgC,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAKqS,OAZL,CAaJ,GACA,CAAApY,IAAA,QAAA6J,MAGA,WAAQ,IAAAkF,EAAAhJ,KACEmI,EAAQ,WACVa,EAAK5E,QAAQ,gBACb4E,EAAK+D,UAAU5E,SAEbsL,EAAkB,SAAlBA,IACFzK,EAAK5I,IAAI,UAAWqT,GACpBzK,EAAK5I,IAAI,eAAgBqT,GACzBtL,KAEEuL,EAAiB,WAEnB1K,EAAK7I,KAAK,UAAWsT,GACrBzK,EAAK7I,KAAK,eAAgBsT,IAqB9B,MAnBI,YAAczT,KAAKiE,YAAc,SAAWjE,KAAKiE,aACjDjE,KAAKiE,WAAa,UACdjE,KAAKoQ,YAAYxT,OACjBoD,KAAKG,KAAK,SAAS,WACX6I,EAAKoJ,UACLsB,IAGAvL,GAER,IAEKnI,KAAKoS,UACVsB,IAGAvL,KAGDnI,IACX,GACA,CAAA/F,IAAA,UAAA6J,MAKA,SAAQoC,GACJiK,EAAO0B,uBAAwB,EAC/B7R,KAAKgB,aAAa,QAASkF,GAC3BlG,KAAKoE,QAAQ,kBAAmB8B,EACpC,GACA,CAAAjM,IAAA,UAAA6J,MAKA,SAAQhB,EAAQC,GACR,YAAc/C,KAAKiE,YACnB,SAAWjE,KAAKiE,YAChB,YAAcjE,KAAKiE,aAEnBjE,KAAKuC,eAAevC,KAAKyR,kBAEzBzR,KAAK+M,UAAUvM,mBAAmB,SAElCR,KAAK+M,UAAU5E,QAEfnI,KAAK+M,UAAUvM,qBACoB,mBAAxBC,sBACPA,oBAAoB,eAAgBT,KAAK0R,2BAA2B,GACpEjR,oBAAoB,UAAWT,KAAK2R,sBAAsB,IAG9D3R,KAAKiE,WAAa,SAElBjE,KAAKqR,GAAK,KAEVrR,KAAKgB,aAAa,QAAS8B,EAAQC,GAGnC/C,KAAKoQ,YAAc,GACnBpQ,KAAKsQ,cAAgB,EAE7B,GACA,CAAArW,IAAA,iBAAA6J,MAMA,SAAewN,GAIX,IAHA,IAAMqC,EAAmB,GACrBzX,EAAI,EACFsD,EAAI8R,EAAS1U,OACZV,EAAIsD,EAAGtD,KACL8D,KAAK6O,WAAW5J,QAAQqM,EAASpV,KAClCyX,EAAiBzT,KAAKoR,EAASpV,IAEvC,OAAOyX,CACX,KAACxD,CAAA,EAxkBuBzQ,GA0kBtBkU,GAAC3M,ShBvbiB,EiBxJAkJ,GAAOlJ,SCF/B,IAAMtM,GAA+C,mBAAhBC,YAC/BC,GAAS,SAACC,GACZ,MAAqC,mBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,EAAIC,kBAAkBH,WAChC,EACMH,GAAWb,OAAOY,UAAUC,SAC5BH,GAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBE,GAASC,KAAKH,MAChBsZ,GAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBrZ,GAASC,KAAKoZ,MAMf,SAASnG,GAAS7S,GACrB,OAASH,KAA0BG,aAAeF,aAAeC,GAAOC,KACnER,IAAkBQ,aAAeP,MACjCsZ,IAAkB/Y,aAAegZ,IAC1C,CACO,SAASC,GAAUjZ,EAAKkZ,GAC3B,IAAKlZ,GAAsB,WAAfuV,EAAOvV,GACf,OAAO,EAEX,GAAIiG,MAAMkT,QAAQnZ,GAAM,CACpB,IAAK,IAAIoB,EAAI,EAAGgV,EAAIpW,EAAI8B,OAAQV,EAAIgV,EAAGhV,IACnC,GAAI6X,GAAUjZ,EAAIoB,IACd,OAAO,EAGf,OAAO,CACX,CACA,GAAIyR,GAAS7S,GACT,OAAO,EAEX,GAAIA,EAAIkZ,QACkB,mBAAflZ,EAAIkZ,QACU,IAArB1T,UAAU1D,OACV,OAAOmX,GAAUjZ,EAAIkZ,UAAU,GAEnC,IAAK,IAAM/Z,KAAOa,EACd,GAAIlB,OAAOY,UAAUoH,eAAelH,KAAKI,EAAKb,IAAQ8Z,GAAUjZ,EAAIb,IAChE,OAAO,EAGf,OAAO,CACX,CCzCO,SAASia,GAAkBnW,GAC9B,IAAMoW,EAAU,GACVC,EAAarW,EAAO1D,KACpBga,EAAOtW,EAGb,OAFAsW,EAAKha,KAAOia,GAAmBF,EAAYD,GAC3CE,EAAKE,YAAcJ,EAAQvX,OACpB,CAAEmB,OAAQsW,EAAMF,QAASA,EACpC,CACA,SAASG,GAAmBja,EAAM8Z,GAC9B,IAAK9Z,EACD,OAAOA,EACX,GAAIsT,GAAStT,GAAO,CAChB,IAAMma,EAAc,CAAEC,cAAc,EAAM9O,IAAKwO,EAAQvX,QAEvD,OADAuX,EAAQjU,KAAK7F,GACNma,CACV,CACI,GAAIzT,MAAMkT,QAAQ5Z,GAAO,CAE1B,IADA,IAAMqa,EAAU,IAAI3T,MAAM1G,EAAKuC,QACtBV,EAAI,EAAGA,EAAI7B,EAAKuC,OAAQV,IAC7BwY,EAAQxY,GAAKoY,GAAmBja,EAAK6B,GAAIiY,GAE7C,OAAOO,CACX,CACK,GAAoB,WAAhBrE,EAAOhW,MAAuBA,aAAgB2L,MAAO,CAC1D,IAAM0O,EAAU,CAAA,EAChB,IAAK,IAAMza,KAAOI,EACVT,OAAOY,UAAUoH,eAAelH,KAAKL,EAAMJ,KAC3Cya,EAAQza,GAAOqa,GAAmBja,EAAKJ,GAAMka,IAGrD,OAAOO,CACX,CACA,OAAOra,CACX,CASO,SAASsa,GAAkB5W,EAAQoW,GAGtC,OAFApW,EAAO1D,KAAOua,GAAmB7W,EAAO1D,KAAM8Z,UACvCpW,EAAOwW,YACPxW,CACX,CACA,SAAS6W,GAAmBva,EAAM8Z,GAC9B,IAAK9Z,EACD,OAAOA,EACX,GAAIA,IAA8B,IAAtBA,EAAKoa,aAAuB,CAIpC,GAHyC,iBAAbpa,EAAKsL,KAC7BtL,EAAKsL,KAAO,GACZtL,EAAKsL,IAAMwO,EAAQvX,OAEnB,OAAOuX,EAAQ9Z,EAAKsL,KAGpB,MAAM,IAAItC,MAAM,sBAEvB,CACI,GAAItC,MAAMkT,QAAQ5Z,GACnB,IAAK,IAAI6B,EAAI,EAAGA,EAAI7B,EAAKuC,OAAQV,IAC7B7B,EAAK6B,GAAK0Y,GAAmBva,EAAK6B,GAAIiY,QAGzC,GAAoB,WAAhB9D,EAAOhW,GACZ,IAAK,IAAMJ,KAAOI,EACVT,OAAOY,UAAUoH,eAAelH,KAAKL,EAAMJ,KAC3CI,EAAKJ,GAAO2a,GAAmBva,EAAKJ,GAAMka,IAItD,OAAO9Z,CACX,CC5EA,IAcWwa,GAdLC,GAAkB,CACpB,UACA,gBACA,aACA,gBACA,cACA,mBASJ,SAAWD,GACPA,EAAWA,EAAoB,QAAI,GAAK,UACxCA,EAAWA,EAAuB,WAAI,GAAK,aAC3CA,EAAWA,EAAkB,MAAI,GAAK,QACtCA,EAAWA,EAAgB,IAAI,GAAK,MACpCA,EAAWA,EAA0B,cAAI,GAAK,gBAC9CA,EAAWA,EAAyB,aAAI,GAAK,eAC7CA,EAAWA,EAAuB,WAAI,GAAK,YAC9C,CARD,CAQGA,KAAeA,GAAa,CAAE,IAIjC,IAAaE,GAAO,WAMhB,SAAAA,EAAYC,GAAU9R,OAAA6R,GAClB/U,KAAKgV,SAAWA,CACpB,CA2DC,OA1DD7R,EAAA4R,EAAA,CAAA,CAAA9a,IAAA,SAAA6J,MAMA,SAAOhJ,GACH,OAAIA,EAAIV,OAASya,GAAWI,OAASna,EAAIV,OAASya,GAAWK,MACrDnB,GAAUjZ,GAWX,CAACkF,KAAKmV,eAAera,IAVbkF,KAAKoV,eAAe,CACvBhb,KAAMU,EAAIV,OAASya,GAAWI,MACxBJ,GAAWQ,aACXR,GAAWS,WACjBC,IAAKza,EAAIya,IACTlb,KAAMS,EAAIT,KACVgX,GAAIvW,EAAIuW,IAKxB,GACA,CAAApX,IAAA,iBAAA6J,MAGA,SAAehJ,GAEX,IAAIwK,EAAM,GAAKxK,EAAIV,KAmBnB,OAjBIU,EAAIV,OAASya,GAAWQ,cACxBva,EAAIV,OAASya,GAAWS,aACxBhQ,GAAOxK,EAAIyZ,YAAc,KAIzBzZ,EAAIya,KAAO,MAAQza,EAAIya,MACvBjQ,GAAOxK,EAAIya,IAAM,KAGjB,MAAQza,EAAIuW,KACZ/L,GAAOxK,EAAIuW,IAGX,MAAQvW,EAAIT,OACZiL,GAAOwN,KAAK0C,UAAU1a,EAAIT,KAAM2F,KAAKgV,WAElC1P,CACX,GACA,CAAArL,IAAA,iBAAA6J,MAKA,SAAehJ,GACX,IAAM2a,EAAiBvB,GAAkBpZ,GACnCuZ,EAAOrU,KAAKmV,eAAeM,EAAe1X,QAC1CoW,EAAUsB,EAAetB,QAE/B,OADAA,EAAQuB,QAAQrB,GACTF,CACX,KAACY,CAAA,CAnEe,GAsEpB,SAASY,GAAS7R,GACd,MAAiD,oBAA1ClK,OAAOY,UAAUC,SAASC,KAAKoJ,EAC1C,CAMa8R,IAAAA,YAAOrS,GAAAZ,EAAAiT,EAAArS,GAAA,IAAAX,EAAAC,EAAA+S,GAMhB,SAAAA,EAAYC,GAAS,IAAA5S,EAEM,OAFNC,OAAA0S,IACjB3S,EAAAL,EAAAlI,KAAAsF,OACK6V,QAAUA,EAAQ5S,CAC3B,CA4IC,OA3IDE,EAAAyS,EAAA,CAAA,CAAA3b,IAAA,MAAA6J,MAKA,SAAIhJ,GACA,IAAIiD,EACJ,GAAmB,iBAARjD,EAAkB,CACzB,GAAIkF,KAAK8V,cACL,MAAM,IAAIzS,MAAM,mDAGpB,IAAM0S,GADNhY,EAASiC,KAAKgW,aAAalb,IACEV,OAASya,GAAWQ,aAC7CU,GAAiBhY,EAAO3D,OAASya,GAAWS,YAC5CvX,EAAO3D,KAAO2b,EAAgBlB,GAAWI,MAAQJ,GAAWK,IAE5DlV,KAAK8V,cAAgB,IAAIG,GAAoBlY,GAElB,IAAvBA,EAAOwW,aACPxQ,EAAAC,EAAA4R,EAAApb,WAAA,eAAAwF,MAAAtF,KAAAsF,KAAmB,UAAWjC,IAKlCgG,EAAAC,EAAA4R,EAAApb,WAAA,eAAAwF,MAAAtF,KAAAsF,KAAmB,UAAWjC,EAErC,KACI,KAAI4P,GAAS7S,KAAQA,EAAIgC,OAe1B,MAAM,IAAIuG,MAAM,iBAAmBvI,GAbnC,IAAKkF,KAAK8V,cACN,MAAM,IAAIzS,MAAM,qDAGhBtF,EAASiC,KAAK8V,cAAcI,eAAepb,MAGvCkF,KAAK8V,cAAgB,KACrB/R,EAAAC,EAAA4R,EAAApb,WAAA,eAAAwF,MAAAtF,KAAAsF,KAAmB,UAAWjC,GAM1C,CACJ,GACA,CAAA9D,IAAA,eAAA6J,MAMA,SAAawB,GACT,IAAIpJ,EAAI,EAEFmB,EAAI,CACNjD,KAAMgL,OAAOE,EAAI7I,OAAO,KAE5B,QAA2BkI,IAAvBkQ,GAAWxX,EAAEjD,MACb,MAAM,IAAIiJ,MAAM,uBAAyBhG,EAAEjD,MAG/C,GAAIiD,EAAEjD,OAASya,GAAWQ,cACtBhY,EAAEjD,OAASya,GAAWS,WAAY,CAElC,IADA,IAAMa,EAAQja,EAAI,EACS,MAApBoJ,EAAI7I,SAASP,IAAcA,GAAKoJ,EAAI1I,SAC3C,IAAMwZ,EAAM9Q,EAAI3I,UAAUwZ,EAAOja,GACjC,GAAIka,GAAOhR,OAAOgR,IAA0B,MAAlB9Q,EAAI7I,OAAOP,GACjC,MAAM,IAAImH,MAAM,uBAEpBhG,EAAEkX,YAAcnP,OAAOgR,EAC3B,CAEA,GAAI,MAAQ9Q,EAAI7I,OAAOP,EAAI,GAAI,CAE3B,IADA,IAAMia,EAAQja,EAAI,IACTA,GAAG,CAER,GAAI,MADMoJ,EAAI7I,OAAOP,GAEjB,MACJ,GAAIA,IAAMoJ,EAAI1I,OACV,KACR,CACAS,EAAEkY,IAAMjQ,EAAI3I,UAAUwZ,EAAOja,EACjC,MAEImB,EAAEkY,IAAM,IAGZ,IAAMc,EAAO/Q,EAAI7I,OAAOP,EAAI,GAC5B,GAAI,KAAOma,GAAQjR,OAAOiR,IAASA,EAAM,CAErC,IADA,IAAMF,EAAQja,EAAI,IACTA,GAAG,CACR,IAAMkX,EAAI9N,EAAI7I,OAAOP,GACrB,GAAI,MAAQkX,GAAKhO,OAAOgO,IAAMA,EAAG,GAC3BlX,EACF,KACJ,CACA,GAAIA,IAAMoJ,EAAI1I,OACV,KACR,CACAS,EAAEgU,GAAKjM,OAAOE,EAAI3I,UAAUwZ,EAAOja,EAAI,GAC3C,CAEA,GAAIoJ,EAAI7I,SAASP,GAAI,CACjB,IAAMoa,EAAUtW,KAAKuW,SAASjR,EAAIkR,OAAOta,IACzC,IAAI0Z,EAAQa,eAAepZ,EAAEjD,KAAMkc,GAI/B,MAAM,IAAIjT,MAAM,mBAHhBhG,EAAEhD,KAAOic,CAKjB,CACA,OAAOjZ,CACX,GAAC,CAAApD,IAAA,WAAA6J,MACD,SAASwB,GACL,IACI,OAAOwN,KAAK5D,MAAM5J,EAAKtF,KAAK6V,QAC/B,CACD,MAAOvP,GACH,OAAO,CACX,CACJ,GAAC,CAAArM,IAAA,UAAA6J,MAuBD,WACQ9D,KAAK8V,gBACL9V,KAAK8V,cAAcY,yBACnB1W,KAAK8V,cAAgB,KAE7B,IAAC,CAAA,CAAA7b,IAAA,iBAAA6J,MA3BD,SAAsB1J,EAAMkc,GACxB,OAAQlc,GACJ,KAAKya,GAAW8B,QACZ,OAAOhB,GAASW,GACpB,KAAKzB,GAAW+B,WACZ,YAAmBjS,IAAZ2R,EACX,KAAKzB,GAAWgC,cACZ,MAA0B,iBAAZP,GAAwBX,GAASW,GACnD,KAAKzB,GAAWI,MAChB,KAAKJ,GAAWQ,aACZ,OAAQtU,MAAMkT,QAAQqC,KACK,iBAAfA,EAAQ,IACW,iBAAfA,EAAQ,KAC6B,IAAzCxB,GAAgB7P,QAAQqR,EAAQ,KAChD,KAAKzB,GAAWK,IAChB,KAAKL,GAAWS,WACZ,OAAOvU,MAAMkT,QAAQqC,GAEjC,KAACV,CAAA,EArJwBlW,GAwKvBuW,GAAmB,WACrB,SAAAA,EAAYlY,GAAQmF,OAAA+S,GAChBjW,KAAKjC,OAASA,EACdiC,KAAKmU,QAAU,GACfnU,KAAK8W,UAAY/Y,CACrB,CAyBC,OAxBDoF,EAAA8S,EAAA,CAAA,CAAAhc,IAAA,iBAAA6J,MAQA,SAAeiT,GAEX,GADA/W,KAAKmU,QAAQjU,KAAK6W,GACd/W,KAAKmU,QAAQvX,SAAWoD,KAAK8W,UAAUvC,YAAa,CAEpD,IAAMxW,EAAS4W,GAAkB3U,KAAK8W,UAAW9W,KAAKmU,SAEtD,OADAnU,KAAK0W,yBACE3Y,CACX,CACA,OAAO,IACX,GACA,CAAA9D,IAAA,yBAAA6J,MAGA,WACI9D,KAAK8W,UAAY,KACjB9W,KAAKmU,QAAU,EACnB,KAAC8B,CAAA,CA9BoB,6CApQD,sDCnBjB,SAASrW,GAAG9E,EAAK2R,EAAI1M,GAExB,OADAjF,EAAI8E,GAAG6M,EAAI1M,GACJ,WACHjF,EAAIsF,IAAIqM,EAAI1M,GAEpB,CCEA,IAAM+U,GAAkBlb,OAAOod,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACb9W,eAAgB,IA0BP4P,YAAM5M,GAAAZ,EAAAwN,EAAA5M,GAAA,IAAAX,EAAAC,EAAAsN,GAIf,SAAAA,EAAYmH,EAAI/B,EAAKpT,GAAM,IAAAc,EAoDP,OApDOC,OAAAiN,IACvBlN,EAAAL,EAAAlI,KAAAsF,OAeKuX,WAAY,EAKjBtU,EAAKuU,WAAY,EAIjBvU,EAAKwU,cAAgB,GAIrBxU,EAAKyU,WAAa,GAOlBzU,EAAK0U,OAAS,GAKd1U,EAAK2U,UAAY,EACjB3U,EAAK4U,IAAM,EACX5U,EAAK6U,KAAO,GACZ7U,EAAK8U,MAAQ,GACb9U,EAAKqU,GAAKA,EACVrU,EAAKsS,IAAMA,EACPpT,GAAQA,EAAK6V,OACb/U,EAAK+U,KAAO7V,EAAK6V,MAErB/U,EAAKgV,MAAQpP,EAAc,CAAE,EAAE1G,GAC3Bc,EAAKqU,GAAGY,cACRjV,EAAK4G,OAAO5G,CACpB,CAmuBC,OAluBDE,EAAAgN,EAAA,CAAA,CAAAlW,IAAA,eAAAsN,IAcA,WACI,OAAQvH,KAAKuX,SACjB,GACA,CAAAtd,IAAA,YAAA6J,MAKA,WACI,IAAI9D,KAAKmY,KAAT,CAEA,IAAMb,EAAKtX,KAAKsX,GAChBtX,KAAKmY,KAAO,CACRvY,GAAG0X,EAAI,OAAQtX,KAAKkM,OAAO5J,KAAKtC,OAChCJ,GAAG0X,EAAI,SAAUtX,KAAKoY,SAAS9V,KAAKtC,OACpCJ,GAAG0X,EAAI,QAAStX,KAAK0M,QAAQpK,KAAKtC,OAClCJ,GAAG0X,EAAI,QAAStX,KAAKsM,QAAQhK,KAAKtC,OANlC,CAQR,GACA,CAAA/F,IAAA,SAAAsN,IAiBA,WACI,QAASvH,KAAKmY,IAClB,GACA,CAAAle,IAAA,UAAA6J,MAUA,WACI,OAAI9D,KAAKuX,YAETvX,KAAKqY,YACArY,KAAKsX,GAAkB,eACxBtX,KAAKsX,GAAGzN,OACR,SAAW7J,KAAKsX,GAAGgB,aACnBtY,KAAKkM,UALElM,IAOf,GACA,CAAA/F,IAAA,OAAA6J,MAGA,WACI,OAAO9D,KAAKiX,SAChB,GACA,CAAAhd,IAAA,OAAA6J,MAeA,WAAc,IAAA,IAAAtC,EAAAlB,UAAA1D,OAANkE,EAAIC,IAAAA,MAAAS,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJZ,EAAIY,GAAApB,UAAAoB,GAGR,OAFAZ,EAAK4U,QAAQ,WACb1V,KAAKa,KAAKR,MAAML,KAAMc,GACfd,IACX,GACA,CAAA/F,IAAA,OAAA6J,MAiBA,SAAK2I,GACD,GAAIqI,GAAgBlT,eAAe6K,GAC/B,MAAM,IAAIpJ,MAAM,IAAMoJ,EAAGhS,WAAa,8BACzC,IAAA8d,IAAAA,EAAAjY,UAAA1D,OAHOkE,MAAIC,MAAAwX,EAAAA,EAAAA,OAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJ1X,EAAI0X,EAAAlY,GAAAA,UAAAkY,GAKZ,GADA1X,EAAK4U,QAAQjJ,GACTzM,KAAKiY,MAAMQ,UAAYzY,KAAK+X,MAAMW,YAAc1Y,KAAK+X,eAErD,OADA/X,KAAK2Y,YAAY7X,GACVd,KAEX,IAAMjC,EAAS,CACX3D,KAAMya,GAAWI,MACjB5a,KAAMyG,EAEV/C,QAAiB,IAGjB,GAFAA,EAAOwV,QAAQC,UAAmC,IAAxBxT,KAAK+X,MAAMvE,SAEjC,mBAAsB1S,EAAKA,EAAKlE,OAAS,GAAI,CAC7C,IAAMyU,EAAKrR,KAAK6X,MACVe,EAAM9X,EAAK+X,MACjB7Y,KAAK8Y,qBAAqBzH,EAAIuH,GAC9B7a,EAAOsT,GAAKA,CAChB,CACA,IAAM0H,EAAsB/Y,KAAKsX,GAAG0B,QAChChZ,KAAKsX,GAAG0B,OAAOjM,WACf/M,KAAKsX,GAAG0B,OAAOjM,UAAUrJ,SAY7B,OAXsB1D,KAAK+X,MAAc,YAAMgB,IAAwB/Y,KAAKuX,aAGnEvX,KAAKuX,WACVvX,KAAKiZ,wBAAwBlb,GAC7BiC,KAAKjC,OAAOA,IAGZiC,KAAK0X,WAAWxX,KAAKnC,IAEzBiC,KAAK+X,MAAQ,GACN/X,IACX,GACA,CAAA/F,IAAA,uBAAA6J,MAGA,SAAqBuN,EAAIuH,GAAK,IACtBlP,EADsBjG,EAAAzD,KAEpBmK,EAAwC,QAA7BT,EAAK1J,KAAK+X,MAAM5N,eAA4B,IAAPT,EAAgBA,EAAK1J,KAAKiY,MAAMiB,WACtF,QAAgBvU,IAAZwF,EAAJ,CAKA,IAAMgP,EAAQnZ,KAAKsX,GAAGjV,cAAa,kBACxBoB,EAAKqU,KAAKzG,GACjB,IAAK,IAAInV,EAAI,EAAGA,EAAIuH,EAAKiU,WAAW9a,OAAQV,IACpCuH,EAAKiU,WAAWxb,GAAGmV,KAAOA,GAC1B5N,EAAKiU,WAAW9W,OAAO1E,EAAG,GAGlC0c,EAAIle,KAAK+I,EAAM,IAAIJ,MAAM,2BAC5B,GAAE8G,GACHnK,KAAK8X,KAAKzG,GAAM,WAEZ5N,EAAK6T,GAAG/U,eAAe4W,GAAO,IAAA,IAAAC,EAAA9Y,UAAA1D,OAFdkE,EAAIC,IAAAA,MAAAqY,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJvY,EAAIuY,GAAA/Y,UAAA+Y,GAGpBT,EAAIvY,MAAMoD,EAAI,CAAG,MAAI8C,OAAKzF,IAd9B,MAFId,KAAK8X,KAAKzG,GAAMuH,CAkBxB,GACA,CAAA3e,IAAA,cAAA6J,MAgBA,SAAY2I,GAAa,IAAA,IAAA7E,EAAA5H,KAAAsZ,EAAAhZ,UAAA1D,OAANkE,MAAIC,MAAAuY,EAAAA,EAAAA,OAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJzY,EAAIyY,EAAAjZ,GAAAA,UAAAiZ,GAEnB,IAAMC,OAAiC7U,IAAvB3E,KAAK+X,MAAM5N,cAAmDxF,IAA1B3E,KAAKiY,MAAMiB,WAC/D,OAAO,IAAI9N,SAAQ,SAACC,EAASoO,GACzB3Y,EAAKZ,MAAK,SAACwZ,EAAMC,GACb,OAAIH,EACOE,EAAOD,EAAOC,GAAQrO,EAAQsO,GAG9BtO,EAAQqO,EAEvB,IACA9R,EAAK/G,KAAIR,MAATuH,EAAU6E,CAAAA,GAAElG,OAAKzF,GACrB,GACJ,GACA,CAAA7G,IAAA,cAAA6J,MAKA,SAAYhD,GAAM,IACV8X,EADU1Q,EAAAlI,KAEuB,mBAA1Bc,EAAKA,EAAKlE,OAAS,KAC1Bgc,EAAM9X,EAAK+X,OAEf,IAAM9a,EAAS,CACXsT,GAAIrR,KAAK4X,YACTgC,SAAU,EACVC,SAAS,EACT/Y,KAAAA,EACAiX,MAAOlP,EAAc,CAAE6P,WAAW,GAAQ1Y,KAAK+X,QAEnDjX,EAAKZ,MAAK,SAACgG,GACP,GAAInI,IAAWmK,EAAKyP,OAAO,GAA3B,CAKA,GADyB,OAARzR,EAETnI,EAAO6b,SAAW1R,EAAK+P,MAAMQ,UAC7BvQ,EAAKyP,OAAOpY,QACRqZ,GACAA,EAAI1S,SAMZ,GADAgC,EAAKyP,OAAOpY,QACRqZ,EAAK,CAAA,IAAAkB,IAAAA,EAAAxZ,UAAA1D,OAhBEmd,MAAYhZ,MAAA+Y,EAAAA,EAAAA,OAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAZD,EAAYC,EAAA1Z,GAAAA,UAAA0Z,GAiBnBpB,EAAGvY,WAAC,EAAA,CAAA,MAAIkG,OAAKwT,GACjB,CAGJ,OADAhc,EAAO8b,SAAU,EACV3R,EAAK+R,aAjBZ,CAkBJ,IACAja,KAAK2X,OAAOzX,KAAKnC,GACjBiC,KAAKia,aACT,GACA,CAAAhgB,IAAA,cAAA6J,MAMA,WAA2B,IAAfoW,EAAK5Z,UAAA1D,OAAA,QAAA+H,IAAArE,UAAA,IAAAA,UAAA,GACb,GAAKN,KAAKuX,WAAoC,IAAvBvX,KAAK2X,OAAO/a,OAAnC,CAGA,IAAMmB,EAASiC,KAAK2X,OAAO,GACvB5Z,EAAO8b,UAAYK,IAGvBnc,EAAO8b,SAAU,EACjB9b,EAAO6b,WACP5Z,KAAK+X,MAAQha,EAAOga,MACpB/X,KAAKa,KAAKR,MAAML,KAAMjC,EAAO+C,MAR7B,CASJ,GACA,CAAA7G,IAAA,SAAA6J,MAMA,SAAO/F,GACHA,EAAOwX,IAAMvV,KAAKuV,IAClBvV,KAAKsX,GAAG6C,QAAQpc,EACpB,GACA,CAAA9D,IAAA,SAAA6J,MAKA,WAAS,IAAAsE,EAAApI,KACmB,mBAAbA,KAAKgY,KACZhY,KAAKgY,MAAK,SAAC3d,GACP+N,EAAKgS,mBAAmB/f,EAC5B,IAGA2F,KAAKoa,mBAAmBpa,KAAKgY,KAErC,GACA,CAAA/d,IAAA,qBAAA6J,MAMA,SAAmBzJ,GACf2F,KAAKjC,OAAO,CACR3D,KAAMya,GAAW8B,QACjBtc,KAAM2F,KAAKqa,KACLxR,EAAc,CAAEyR,IAAKta,KAAKqa,KAAME,OAAQva,KAAKwa,aAAengB,GAC5DA,GAEd,GACA,CAAAJ,IAAA,UAAA6J,MAMA,SAAQoC,GACClG,KAAKuX,WACNvX,KAAKgB,aAAa,gBAAiBkF,EAE3C,GACA,CAAAjM,IAAA,UAAA6J,MAOA,SAAQhB,EAAQC,GACZ/C,KAAKuX,WAAY,SACVvX,KAAKqR,GACZrR,KAAKgB,aAAa,aAAc8B,EAAQC,EAC5C,GACA,CAAA9I,IAAA,WAAA6J,MAMA,SAAS/F,GAEL,GADsBA,EAAOwX,MAAQvV,KAAKuV,IAG1C,OAAQxX,EAAO3D,MACX,KAAKya,GAAW8B,QACR5Y,EAAO1D,MAAQ0D,EAAO1D,KAAKqO,IAC3B1I,KAAKya,UAAU1c,EAAO1D,KAAKqO,IAAK3K,EAAO1D,KAAKigB,KAG5Cta,KAAKgB,aAAa,gBAAiB,IAAIqC,MAAM,8LAEjD,MACJ,KAAKwR,GAAWI,MAChB,KAAKJ,GAAWQ,aACZrV,KAAK0a,QAAQ3c,GACb,MACJ,KAAK8W,GAAWK,IAChB,KAAKL,GAAWS,WACZtV,KAAK2a,MAAM5c,GACX,MACJ,KAAK8W,GAAW+B,WACZ5W,KAAK4a,eACL,MACJ,KAAK/F,GAAWgC,cACZ7W,KAAK6a,UACL,IAAM3U,EAAM,IAAI7C,MAAMtF,EAAO1D,KAAKygB,SAElC5U,EAAI7L,KAAO0D,EAAO1D,KAAKA,KACvB2F,KAAKgB,aAAa,gBAAiBkF,GAG/C,GACA,CAAAjM,IAAA,UAAA6J,MAMA,SAAQ/F,GACJ,IAAM+C,EAAO/C,EAAO1D,MAAQ,GACxB,MAAQ0D,EAAOsT,IACfvQ,EAAKZ,KAAKF,KAAK4Y,IAAI7a,EAAOsT,KAE1BrR,KAAKuX,UACLvX,KAAK+a,UAAUja,GAGfd,KAAKyX,cAAcvX,KAAKtG,OAAOod,OAAOlW,GAE9C,GAAC,CAAA7G,IAAA,YAAA6J,MACD,SAAUhD,GACN,GAAId,KAAKgb,eAAiBhb,KAAKgb,cAAcpe,OAAQ,CACjD,IACgCqe,EADaC,EAAAC,EAA3Bnb,KAAKgb,cAAcvb,SACL,IAAhC,IAAAyb,EAAAE,MAAAH,EAAAC,EAAApN,KAAAc,MAAkC,CAAfqM,EAAAnX,MACNzD,MAAML,KAAMc,EACzB,CAAC,CAAA,MAAAoF,GAAAgV,EAAA5U,EAAAJ,EAAA,CAAA,QAAAgV,EAAAG,GAAA,CACL,CACAtX,EAAAC,EAAAmM,EAAA3V,WAAW6F,OAAAA,MAAAA,MAAML,KAAMc,GACnBd,KAAKqa,MAAQvZ,EAAKlE,QAA2C,iBAA1BkE,EAAKA,EAAKlE,OAAS,KACtDoD,KAAKwa,YAAc1Z,EAAKA,EAAKlE,OAAS,GAE9C,GACA,CAAA3C,IAAA,MAAA6J,MAKA,SAAIuN,GACA,IAAMjQ,EAAOpB,KACTsb,GAAO,EACX,OAAO,WAEH,IAAIA,EAAJ,CAEAA,GAAO,EAAK,IAAA,IAAAC,EAAAjb,UAAA1D,OAJIkE,EAAIC,IAAAA,MAAAwa,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJ1a,EAAI0a,GAAAlb,UAAAkb,GAKpBpa,EAAKrD,OAAO,CACR3D,KAAMya,GAAWK,IACjB7D,GAAIA,EACJhX,KAAMyG,GALN,EAQZ,GACA,CAAA7G,IAAA,QAAA6J,MAMA,SAAM/F,GACF,IAAM6a,EAAM5Y,KAAK8X,KAAK/Z,EAAOsT,IACzB,mBAAsBuH,IACtBA,EAAIvY,MAAML,KAAMjC,EAAO1D,aAChB2F,KAAK8X,KAAK/Z,EAAOsT,IAIhC,GACA,CAAApX,IAAA,YAAA6J,MAKA,SAAUuN,EAAIiJ,GACVta,KAAKqR,GAAKA,EACVrR,KAAKwX,UAAY8C,GAAOta,KAAKqa,OAASC,EACtCta,KAAKqa,KAAOC,EACZta,KAAKuX,WAAY,EACjBvX,KAAKyb,eACLzb,KAAKgB,aAAa,WAClBhB,KAAKia,aAAY,EACrB,GACA,CAAAhgB,IAAA,eAAA6J,MAKA,WAAe,IAAAkF,EAAAhJ,KACXA,KAAKyX,cAAczd,SAAQ,SAAC8G,GAAI,OAAKkI,EAAK+R,UAAUja,MACpDd,KAAKyX,cAAgB,GACrBzX,KAAK0X,WAAW1d,SAAQ,SAAC+D,GACrBiL,EAAKiQ,wBAAwBlb,GAC7BiL,EAAKjL,OAAOA,EAChB,IACAiC,KAAK0X,WAAa,EACtB,GACA,CAAAzd,IAAA,eAAA6J,MAKA,WACI9D,KAAK6a,UACL7a,KAAKsM,QAAQ,uBACjB,GACA,CAAArS,IAAA,UAAA6J,MAOA,WACQ9D,KAAKmY,OAELnY,KAAKmY,KAAKne,SAAQ,SAAC0hB,GAAU,OAAKA,OAClC1b,KAAKmY,UAAOxT,GAEhB3E,KAAKsX,GAAa,SAAEtX,KACxB,GACA,CAAA/F,IAAA,aAAA6J,MAgBA,WAUI,OATI9D,KAAKuX,WACLvX,KAAKjC,OAAO,CAAE3D,KAAMya,GAAW+B,aAGnC5W,KAAK6a,UACD7a,KAAKuX,WAELvX,KAAKsM,QAAQ,wBAEVtM,IACX,GACA,CAAA/F,IAAA,QAAA6J,MAKA,WACI,OAAO9D,KAAKmX,YAChB,GACA,CAAAld,IAAA,WAAA6J,MASA,SAAS0P,GAEL,OADAxT,KAAK+X,MAAMvE,SAAWA,EACfxT,IACX,GACA,CAAA/F,IAAA,WAAAsN,IASA,WAEI,OADAvH,KAAK+X,MAAc,UAAG,EACf/X,IACX,GACA,CAAA/F,IAAA,UAAA6J,MAaA,SAAQqG,GAEJ,OADAnK,KAAK+X,MAAM5N,QAAUA,EACdnK,IACX,GACA,CAAA/F,IAAA,QAAA6J,MAWA,SAAM6X,GAGF,OAFA3b,KAAKgb,cAAgBhb,KAAKgb,eAAiB,GAC3Chb,KAAKgb,cAAc9a,KAAKyb,GACjB3b,IACX,GACA,CAAA/F,IAAA,aAAA6J,MAWA,SAAW6X,GAGP,OAFA3b,KAAKgb,cAAgBhb,KAAKgb,eAAiB,GAC3Chb,KAAKgb,cAActF,QAAQiG,GACpB3b,IACX,GACA,CAAA/F,IAAA,SAAA6J,MAkBA,SAAO6X,GACH,IAAK3b,KAAKgb,cACN,OAAOhb,KAEX,GAAI2b,GAEA,IADA,IAAM1a,EAAYjB,KAAKgb,cACd9e,EAAI,EAAGA,EAAI+E,EAAUrE,OAAQV,IAClC,GAAIyf,IAAa1a,EAAU/E,GAEvB,OADA+E,EAAUL,OAAO1E,EAAG,GACb8D,UAKfA,KAAKgb,cAAgB,GAEzB,OAAOhb,IACX,GACA,CAAA/F,IAAA,eAAA6J,MAIA,WACI,OAAO9D,KAAKgb,eAAiB,EACjC,GACA,CAAA/gB,IAAA,gBAAA6J,MAaA,SAAc6X,GAGV,OAFA3b,KAAK4b,sBAAwB5b,KAAK4b,uBAAyB,GAC3D5b,KAAK4b,sBAAsB1b,KAAKyb,GACzB3b,IACX,GACA,CAAA/F,IAAA,qBAAA6J,MAaA,SAAmB6X,GAGf,OAFA3b,KAAK4b,sBAAwB5b,KAAK4b,uBAAyB,GAC3D5b,KAAK4b,sBAAsBlG,QAAQiG,GAC5B3b,IACX,GACA,CAAA/F,IAAA,iBAAA6J,MAkBA,SAAe6X,GACX,IAAK3b,KAAK4b,sBACN,OAAO5b,KAEX,GAAI2b,GAEA,IADA,IAAM1a,EAAYjB,KAAK4b,sBACd1f,EAAI,EAAGA,EAAI+E,EAAUrE,OAAQV,IAClC,GAAIyf,IAAa1a,EAAU/E,GAEvB,OADA+E,EAAUL,OAAO1E,EAAG,GACb8D,UAKfA,KAAK4b,sBAAwB,GAEjC,OAAO5b,IACX,GACA,CAAA/F,IAAA,uBAAA6J,MAIA,WACI,OAAO9D,KAAK4b,uBAAyB,EACzC,GACA,CAAA3hB,IAAA,0BAAA6J,MAOA,SAAwB/F,GACpB,GAAIiC,KAAK4b,uBAAyB5b,KAAK4b,sBAAsBhf,OAAQ,CACjE,IACgCif,EADqBC,EAAAX,EAAnCnb,KAAK4b,sBAAsBnc,SACb,IAAhC,IAAAqc,EAAAV,MAAAS,EAAAC,EAAAhO,KAAAc,MAAkC,CAAfiN,EAAA/X,MACNzD,MAAML,KAAMjC,EAAO1D,KAChC,CAAC,CAAA,MAAA6L,GAAA4V,EAAAxV,EAAAJ,EAAA,CAAA,QAAA4V,EAAAT,GAAA,CACL,CACJ,KAAClL,CAAA,EA5xBuBzQ,GC7BrB,SAASqc,GAAQ5Z,GACpBA,EAAOA,GAAQ,GACfnC,KAAKgc,GAAK7Z,EAAK8Z,KAAO,IACtBjc,KAAKkc,IAAM/Z,EAAK+Z,KAAO,IACvBlc,KAAKmc,OAASha,EAAKga,QAAU,EAC7Bnc,KAAKoc,OAASja,EAAKia,OAAS,GAAKja,EAAKia,QAAU,EAAIja,EAAKia,OAAS,EAClEpc,KAAKqc,SAAW,CACpB,CAOAN,GAAQvhB,UAAU8hB,SAAW,WACzB,IAAIN,EAAKhc,KAAKgc,GAAKpW,KAAKoI,IAAIhO,KAAKmc,OAAQnc,KAAKqc,YAC9C,GAAIrc,KAAKoc,OAAQ,CACb,IAAIG,EAAO3W,KAAK4W,SACZC,EAAY7W,KAAKC,MAAM0W,EAAOvc,KAAKoc,OAASJ,GAChDA,EAAoC,IAAN,EAAxBpW,KAAKC,MAAa,GAAP0W,IAAuBP,EAAKS,EAAYT,EAAKS,CAClE,CACA,OAAgC,EAAzB7W,KAAKqW,IAAID,EAAIhc,KAAKkc,IAC7B,EAMAH,GAAQvhB,UAAUkiB,MAAQ,WACtB1c,KAAKqc,SAAW,CACpB,EAMAN,GAAQvhB,UAAUmiB,OAAS,SAAUV,GACjCjc,KAAKgc,GAAKC,CACd,EAMAF,GAAQvhB,UAAUoiB,OAAS,SAAUV,GACjClc,KAAKkc,IAAMA,CACf,EAMAH,GAAQvhB,UAAUqiB,UAAY,SAAUT,GACpCpc,KAAKoc,OAASA,CAClB,EC3DaU,IAAAA,YAAOvZ,GAAAZ,EAAAma,EAAAvZ,GAAA,IAAAX,EAAAC,EAAAia,GAChB,SAAAA,EAAY/T,EAAK5G,GAAM,IAAAc,EACfyG,EADexG,OAAA4Z,IAEnB7Z,EAAAL,EAAAlI,KAAAsF,OACK+c,KAAO,GACZ9Z,EAAKkV,KAAO,GACRpP,GAAO,WAAQsH,EAAYtH,KAC3B5G,EAAO4G,EACPA,OAAMpE,IAEVxC,EAAOA,GAAQ,IACV2C,KAAO3C,EAAK2C,MAAQ,aACzB7B,EAAKd,KAAOA,EACZD,EAAqByB,EAAAV,GAAOd,GAC5Bc,EAAK+Z,cAAmC,IAAtB7a,EAAK6a,cACvB/Z,EAAKga,qBAAqB9a,EAAK8a,sBAAwBC,KACvDja,EAAKka,kBAAkBhb,EAAKgb,mBAAqB,KACjDla,EAAKma,qBAAqBjb,EAAKib,sBAAwB,KACvDna,EAAKoa,oBAAwD,QAAnC3T,EAAKvH,EAAKkb,2BAAwC,IAAP3T,EAAgBA,EAAK,IAC1FzG,EAAKqa,QAAU,IAAIvB,GAAQ,CACvBE,IAAKhZ,EAAKka,oBACVjB,IAAKjZ,EAAKma,uBACVhB,OAAQnZ,EAAKoa,wBAEjBpa,EAAKkH,QAAQ,MAAQhI,EAAKgI,QAAU,IAAQhI,EAAKgI,SACjDlH,EAAKqV,YAAc,SACnBrV,EAAK8F,IAAMA,EACX,IAAMwU,EAAUpb,EAAKqb,QAAUA,GAKf,OAJhBva,EAAKwa,QAAU,IAAIF,EAAQxI,QAC3B9R,EAAKya,QAAU,IAAIH,EAAQ3H,QAC3B3S,EAAKiV,cAAoC,IAArB/V,EAAKwb,YACrB1a,EAAKiV,cACLjV,EAAK4G,OAAO5G,CACpB,CA6TC,OA7TAE,EAAA2Z,EAAA,CAAA,CAAA7iB,IAAA,eAAA6J,MACD,SAAa8Z,GACT,OAAKtd,UAAU1D,QAEfoD,KAAK6d,gBAAkBD,EAChB5d,MAFIA,KAAK6d,aAGpB,GAAC,CAAA5jB,IAAA,uBAAA6J,MACD,SAAqB8Z,GACjB,YAAUjZ,IAANiZ,EACO5d,KAAK8d,uBAChB9d,KAAK8d,sBAAwBF,EACtB5d,KACX,GAAC,CAAA/F,IAAA,oBAAA6J,MACD,SAAkB8Z,GACd,IAAIlU,EACJ,YAAU/E,IAANiZ,EACO5d,KAAK+d,oBAChB/d,KAAK+d,mBAAqBH,EACF,QAAvBlU,EAAK1J,KAAKsd,eAA4B,IAAP5T,GAAyBA,EAAGiT,OAAOiB,GAC5D5d,KACX,GAAC,CAAA/F,IAAA,sBAAA6J,MACD,SAAoB8Z,GAChB,IAAIlU,EACJ,YAAU/E,IAANiZ,EACO5d,KAAKge,sBAChBhe,KAAKge,qBAAuBJ,EACJ,QAAvBlU,EAAK1J,KAAKsd,eAA4B,IAAP5T,GAAyBA,EAAGmT,UAAUe,GAC/D5d,KACX,GAAC,CAAA/F,IAAA,uBAAA6J,MACD,SAAqB8Z,GACjB,IAAIlU,EACJ,YAAU/E,IAANiZ,EACO5d,KAAKie,uBAChBje,KAAKie,sBAAwBL,EACL,QAAvBlU,EAAK1J,KAAKsd,eAA4B,IAAP5T,GAAyBA,EAAGkT,OAAOgB,GAC5D5d,KACX,GAAC,CAAA/F,IAAA,UAAA6J,MACD,SAAQ8Z,GACJ,OAAKtd,UAAU1D,QAEfoD,KAAKke,SAAWN,EACT5d,MAFIA,KAAKke,QAGpB,GACA,CAAAjkB,IAAA,uBAAA6J,MAMA,YAES9D,KAAKme,eACNne,KAAK6d,eACqB,IAA1B7d,KAAKsd,QAAQjB,UAEbrc,KAAKoe,WAEb,GACA,CAAAnkB,IAAA,OAAA6J,MAOA,SAAK/D,GAAI,IAAA0D,EAAAzD,KACL,IAAKA,KAAKsY,YAAYrT,QAAQ,QAC1B,OAAOjF,KACXA,KAAKgZ,OAAS,IAAIqF,GAAOre,KAAK+I,IAAK/I,KAAKmC,MACxC,IAAM0B,EAAS7D,KAAKgZ,OACd5X,EAAOpB,KACbA,KAAKsY,YAAc,UACnBtY,KAAKse,eAAgB,EAErB,IAAMC,EAAiB3e,GAAGiE,EAAQ,QAAQ,WACtCzC,EAAK8K,SACLnM,GAAMA,GACV,IACMsJ,EAAU,SAACnD,GACbzC,EAAKoH,UACLpH,EAAK6U,YAAc,SACnB7U,EAAKzC,aAAa,QAASkF,GACvBnG,EACAA,EAAGmG,GAIHzC,EAAK+a,wBAIPC,EAAW7e,GAAGiE,EAAQ,QAASwF,GACrC,IAAI,IAAUrJ,KAAKke,SAAU,CACzB,IAAM/T,EAAUnK,KAAKke,SAEf/E,EAAQnZ,KAAKqC,cAAa,WAC5Bkc,IACAlV,EAAQ,IAAIhG,MAAM,YAClBQ,EAAOsE,OACV,GAAEgC,GACCnK,KAAKmC,KAAKgK,WACVgN,EAAM9M,QAEVrM,KAAKmY,KAAKjY,MAAK,WACXuD,EAAKlB,eAAe4W,EACxB,GACJ,CAGA,OAFAnZ,KAAKmY,KAAKjY,KAAKqe,GACfve,KAAKmY,KAAKjY,KAAKue,GACRze,IACX,GACA,CAAA/F,IAAA,UAAA6J,MAMA,SAAQ/D,GACJ,OAAOC,KAAK6J,KAAK9J,EACrB,GACA,CAAA9F,IAAA,SAAA6J,MAKA,WAEI9D,KAAK6K,UAEL7K,KAAKsY,YAAc,OACnBtY,KAAKgB,aAAa,QAElB,IAAM6C,EAAS7D,KAAKgZ,OACpBhZ,KAAKmY,KAAKjY,KAAKN,GAAGiE,EAAQ,OAAQ7D,KAAK0e,OAAOpc,KAAKtC,OAAQJ,GAAGiE,EAAQ,OAAQ7D,KAAK2e,OAAOrc,KAAKtC,OAAQJ,GAAGiE,EAAQ,QAAS7D,KAAK0M,QAAQpK,KAAKtC,OAAQJ,GAAGiE,EAAQ,QAAS7D,KAAKsM,QAAQhK,KAAKtC,OAAQJ,GAAGI,KAAK0d,QAAS,UAAW1d,KAAK4e,UAAUtc,KAAKtC,OACvP,GACA,CAAA/F,IAAA,SAAA6J,MAKA,WACI9D,KAAKgB,aAAa,OACtB,GACA,CAAA/G,IAAA,SAAA6J,MAKA,SAAOzJ,GACH,IACI2F,KAAK0d,QAAQmB,IAAIxkB,EACpB,CACD,MAAOiM,GACHtG,KAAKsM,QAAQ,cAAehG,EAChC,CACJ,GACA,CAAArM,IAAA,YAAA6J,MAKA,SAAU/F,GAAQ,IAAA6J,EAAA5H,KAEdmL,IAAS,WACLvD,EAAK5G,aAAa,SAAUjD,EAChC,GAAGiC,KAAKqC,aACZ,GACA,CAAApI,IAAA,UAAA6J,MAKA,SAAQoC,GACJlG,KAAKgB,aAAa,QAASkF,EAC/B,GACA,CAAAjM,IAAA,SAAA6J,MAMA,SAAOyR,EAAKpT,GACR,IAAI0B,EAAS7D,KAAK+c,KAAKxH,GAQvB,OAPK1R,EAII7D,KAAKkY,eAAiBrU,EAAOib,QAClCjb,EAAOoT,WAJPpT,EAAS,IAAIsM,GAAOnQ,KAAMuV,EAAKpT,GAC/BnC,KAAK+c,KAAKxH,GAAO1R,GAKdA,CACX,GACA,CAAA5J,IAAA,WAAA6J,MAMA,SAASD,GAEL,IADA,IACAkb,EAAA,EAAAC,EADaplB,OAAOG,KAAKiG,KAAK+c,MACRgC,EAAAC,EAAApiB,OAAAmiB,IAAE,CAAnB,IAAMxJ,EAAGyJ,EAAAD,GAEV,GADe/e,KAAK+c,KAAKxH,GACduJ,OACP,MAER,CACA9e,KAAKif,QACT,GACA,CAAAhlB,IAAA,UAAA6J,MAMA,SAAQ/F,GAEJ,IADA,IAAM+J,EAAiB9H,KAAKyd,QAAQpf,OAAON,GAClC7B,EAAI,EAAGA,EAAI4L,EAAelL,OAAQV,IACvC8D,KAAKgZ,OAAO1U,MAAMwD,EAAe5L,GAAI6B,EAAOwV,QAEpD,GACA,CAAAtZ,IAAA,UAAA6J,MAKA,WACI9D,KAAKmY,KAAKne,SAAQ,SAAC0hB,GAAU,OAAKA,OAClC1b,KAAKmY,KAAKvb,OAAS,EACnBoD,KAAK0d,QAAQ7C,SACjB,GACA,CAAA5gB,IAAA,SAAA6J,MAKA,WACI9D,KAAKse,eAAgB,EACrBte,KAAKme,eAAgB,EACrBne,KAAKsM,QAAQ,gBACTtM,KAAKgZ,QACLhZ,KAAKgZ,OAAO7Q,OACpB,GACA,CAAAlO,IAAA,aAAA6J,MAKA,WACI,OAAO9D,KAAKif,QAChB,GACA,CAAAhlB,IAAA,UAAA6J,MAKA,SAAQhB,EAAQC,GACZ/C,KAAK6K,UACL7K,KAAKsd,QAAQZ,QACb1c,KAAKsY,YAAc,SACnBtY,KAAKgB,aAAa,QAAS8B,EAAQC,GAC/B/C,KAAK6d,gBAAkB7d,KAAKse,eAC5Bte,KAAKoe,WAEb,GACA,CAAAnkB,IAAA,YAAA6J,MAKA,WAAY,IAAAoE,EAAAlI,KACR,GAAIA,KAAKme,eAAiBne,KAAKse,cAC3B,OAAOte,KACX,IAAMoB,EAAOpB,KACb,GAAIA,KAAKsd,QAAQjB,UAAYrc,KAAK8d,sBAC9B9d,KAAKsd,QAAQZ,QACb1c,KAAKgB,aAAa,oBAClBhB,KAAKme,eAAgB,MAEpB,CACD,IAAMe,EAAQlf,KAAKsd,QAAQhB,WAC3Btc,KAAKme,eAAgB,EACrB,IAAMhF,EAAQnZ,KAAKqC,cAAa,WACxBjB,EAAKkd,gBAETpW,EAAKlH,aAAa,oBAAqBI,EAAKkc,QAAQjB,UAEhDjb,EAAKkd,eAETld,EAAKyI,MAAK,SAAC3D,GACHA,GACA9E,EAAK+c,eAAgB,EACrB/c,EAAKgd,YACLlW,EAAKlH,aAAa,kBAAmBkF,IAGrC9E,EAAK+d,aAEb,IACH,GAAED,GACClf,KAAKmC,KAAKgK,WACVgN,EAAM9M,QAEVrM,KAAKmY,KAAKjY,MAAK,WACXgI,EAAK3F,eAAe4W,EACxB,GACJ,CACJ,GACA,CAAAlf,IAAA,cAAA6J,MAKA,WACI,IAAMsb,EAAUpf,KAAKsd,QAAQjB,SAC7Brc,KAAKme,eAAgB,EACrBne,KAAKsd,QAAQZ,QACb1c,KAAKgB,aAAa,YAAaoe,EACnC,KAACtC,CAAA,EA9VwBpd,GCAvB2f,GAAQ,CAAA,EACd,SAASpjB,GAAO8M,EAAK5G,GACE,WAAfkO,EAAOtH,KACP5G,EAAO4G,EACPA,OAAMpE,GAGV,IASI2S,EATEgI,ECHH,SAAavW,GAAqB,IAAhBjE,EAAIxE,UAAA1D,OAAA,QAAA+H,IAAArE,UAAA,GAAAA,UAAA,GAAG,GAAIif,EAAGjf,UAAA1D,OAAA0D,EAAAA,kBAAAqE,EAC/B7J,EAAMiO,EAEVwW,EAAMA,GAA4B,oBAAbxY,UAA4BA,SAC7C,MAAQgC,IACRA,EAAMwW,EAAItY,SAAW,KAAOsY,EAAI9P,MAEjB,iBAAR1G,IACH,MAAQA,EAAItM,OAAO,KAEfsM,EADA,MAAQA,EAAItM,OAAO,GACb8iB,EAAItY,SAAW8B,EAGfwW,EAAI9P,KAAO1G,GAGpB,sBAAsByW,KAAKzW,KAExBA,OADA,IAAuBwW,EACjBA,EAAItY,SAAW,KAAO8B,EAGtB,WAAaA,GAI3BjO,EAAMoU,GAAMnG,IAGXjO,EAAIoK,OACD,cAAcsa,KAAK1kB,EAAImM,UACvBnM,EAAIoK,KAAO,KAEN,eAAesa,KAAK1kB,EAAImM,YAC7BnM,EAAIoK,KAAO,QAGnBpK,EAAIgK,KAAOhK,EAAIgK,MAAQ,IACvB,IACM2K,GADkC,IAA3B3U,EAAI2U,KAAKxK,QAAQ,KACV,IAAMnK,EAAI2U,KAAO,IAAM3U,EAAI2U,KAS/C,OAPA3U,EAAIuW,GAAKvW,EAAImM,SAAW,MAAQwI,EAAO,IAAM3U,EAAIoK,KAAOJ,EAExDhK,EAAI2kB,KACA3kB,EAAImM,SACA,MACAwI,GACC8P,GAAOA,EAAIra,OAASpK,EAAIoK,KAAO,GAAK,IAAMpK,EAAIoK,MAChDpK,CACX,CD7CmB4kB,CAAI3W,GADnB5G,EAAOA,GAAQ,IACc2C,MAAQ,cAC/B0K,EAAS8P,EAAO9P,OAChB6B,EAAKiO,EAAOjO,GACZvM,EAAOwa,EAAOxa,KACd6a,EAAgBN,GAAMhO,IAAOvM,KAAQua,GAAMhO,GAAU,KAkB3D,OAjBsBlP,EAAKyd,UACvBzd,EAAK,0BACL,IAAUA,EAAK0d,WACfF,EAGArI,EAAK,IAAIwF,GAAQtN,EAAQrN,IAGpBkd,GAAMhO,KACPgO,GAAMhO,GAAM,IAAIyL,GAAQtN,EAAQrN,IAEpCmV,EAAK+H,GAAMhO,IAEXiO,EAAO1b,QAAUzB,EAAKyB,QACtBzB,EAAKyB,MAAQ0b,EAAOvP,UAEjBuH,EAAGzT,OAAOyb,EAAOxa,KAAM3C,EAClC,QAGA0G,EAAc5M,GAAQ,CAClB6gB,QAAAA,GACA3M,OAAAA,GACAmH,GAAIrb,GACJgb,QAAShb"}