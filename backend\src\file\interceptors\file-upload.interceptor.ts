import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import * as path from 'path';
import * as fs from 'fs';
import { RequestWithUser } from 'src/common/interfaces/request-with-user.interface';
import { BadRequestException } from '@nestjs/common';
import * as sharp from 'sharp';

// Allowed file types
export const allowedMimeTypes = [
  'image/jpeg',
  'image/png'
];

/**
 * Validates image aspect ratio to be 1:1
 * 
 * @param buffer Buffer gambar yang akan divalidasi
 * @returns Promise yang resolve jika valid, reject jika tidak valid
 */
export const validateImageAspectRatio = async (buffer: Buffer): Promise<void> => {
  try {
    const metadata = await sharp(buffer).metadata();
    
    if (metadata.width && metadata.height) {
      const aspectRatio = metadata.width / metadata.height;
      if (Math.abs(aspectRatio - 1) > 0.1) {
        throw new BadRequestException('Image must have 1:1 aspect ratio (square)');
      }
    }
  } catch (error) {
    if (error instanceof BadRequestException) {
      throw error;
    }
    throw new BadRequestException(`Error validating image: ${error.message}`);
  }
};

/**
 * Creates a configured FileInterceptor for handling single file uploads
 * 
 * @param fieldName Nama field untuk file upload
 * @returns FileInterceptor yang dikonfigurasi
 */
export const uploadImageInterceptor = (fieldName: string = 'file') => {
  return FileInterceptor(fieldName, {
    storage: diskStorage({
      destination: (req, file, cb) => {
        const userId = (req as RequestWithUser).user?.userId || 'temp';
        const tempDir = path.join(process.cwd(), 'storage', userId);
        
        if (!fs.existsSync(tempDir)) {
          fs.mkdirSync(tempDir, { recursive: true });
        }
        
        cb(null, tempDir);
      },
      filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
        const extension = 'png'; // Selalu konversi ke PNG
        cb(null, `${uniqueSuffix}.${extension}`);
      },
    }),
    limits: {
      fileSize: 1 * 1024 * 1024, // 1MB limit
    },
    fileFilter: (req, file, cb) => {
      if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
        return cb(new BadRequestException('Only image files are allowed!'), false);
      }
      cb(null, true);
    },
  });
};

/**
 * Creates an interceptor for handling multiple square image uploads
 * 
 * @param fieldName The form field name for the files (default: 'files')
 * @param maxCount Maximum number of files allowed (default: 5)
 * @returns Configured FilesInterceptor
 */
export const bulkUploadImageInterceptor = (fieldName: string = 'files', maxCount: number = 5) => {
  return FilesInterceptor(fieldName, maxCount, {
    storage: diskStorage({
      destination: (req, file, cb) => {
        const userId = (req as RequestWithUser).user?.userId || 'temp';
        const tempDir = path.join(process.cwd(), 'storage', userId, 'bulk');
        
        if (!fs.existsSync(tempDir)) {
          fs.mkdirSync(tempDir, { recursive: true });
        }
        
        cb(null, tempDir);
      },
      filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
        const extension = 'png'; // Always convert to PNG
        cb(null, `${uniqueSuffix}.${extension}`);
      },
    }),
    limits: {
      fileSize: 1 * 1024 * 1024, // 1MB limit per file
      files: maxCount, // Maximum number of files
    },
    fileFilter: (req, file, cb) => {
      // Check file type
      if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
        return cb(new BadRequestException('Only image files are allowed!'), false);
      }
      
      // Check MIME type
      if (!allowedMimeTypes.includes(file.mimetype)) {
        return cb(new BadRequestException(`File type not allowed: ${file.mimetype}`), false);
      }
      
      cb(null, true);
    },
  });
};
