import { Module } from '@nestjs/common';
import { QueueService } from './queue.service';
import { BullModule } from '@nestjs/bullmq';
import { ConfigService } from '@nestjs/config';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import * as expressBasicAuth from 'express-basic-auth';
import { UserModule } from 'src/user/user.module';
import { MatchModule } from 'src/match/match.module';
import { PersonalityModule } from 'src/personality/personality.module';
import { MatchProcessor } from './processors/match.processor';
import { ImageProcessor } from './processors/image.processor';
import { QUEUE_IMAGE_NAME, QUEUE_MATCH_NAME } from 'src/constants/queue';

@Module({
  imports: [
    BullModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        connection: {
          host: configService.get<string>('app.redis.host'),
          port: configService.get<number>('app.redis.port'),
          username: configService.get<string>('app.redis.username'),
          password: configService.get<string>('app.redis.password'),
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: QUEUE_MATCH_NAME,
      /*
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 60000
        }
      }
      */
    }, {
      name: QUEUE_IMAGE_NAME,
    }),
    BullBoardModule.forRoot({
      route: '/admin/queues',
      adapter: ExpressAdapter,
      middleware: expressBasicAuth({
        challenge: true,
        users: { [process.env.BULL_BOARD_USER || 'admin']: process.env.BULL_BOARD_PASSWORD || '12345678' },
      }),
    }),
    BullBoardModule.forFeature({
      name: QUEUE_MATCH_NAME,
      adapter: BullMQAdapter
    }),
    BullBoardModule.forFeature({
      name: QUEUE_IMAGE_NAME,
      adapter: BullMQAdapter
    }),
    UserModule,
    PersonalityModule,
    MatchModule
  ],
  providers: [
    MatchProcessor,
    ImageProcessor,
    QueueService
  ]
})
export class QueueModule {}
