"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Heart,HelpCircle,LayoutDashboard,LogOut,MessageSquare,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Heart,HelpCircle,LayoutDashboard,LogOut,MessageSquare,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Heart,HelpCircle,LayoutDashboard,LogOut,MessageSquare,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Heart,HelpCircle,LayoutDashboard,LogOut,MessageSquare,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Heart,HelpCircle,LayoutDashboard,LogOut,MessageSquare,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Heart,HelpCircle,LayoutDashboard,LogOut,MessageSquare,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,Heart,HelpCircle,LayoutDashboard,LogOut,MessageSquare,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_nav_main__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/nav-main */ \"(app-pages-browser)/./src/components/nav-main.tsx\");\n/* harmony import */ var _components_nav_secondary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/nav-secondary */ \"(app-pages-browser)/./src/components/nav-secondary.tsx\");\n/* harmony import */ var _components_nav_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/nav-user */ \"(app-pages-browser)/./src/components/nav-user.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst navMainData = [\n    {\n        title: \"Dasbor\",\n        url: \"/dashboard\",\n        icon: _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        isActive: true,\n        items: []\n    },\n    {\n        title: \"Pasangan\",\n        url: \"/dashboard/matches\",\n        icon: _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        items: []\n    },\n    {\n        title: \"Obrolan\",\n        url: \"/dashboard/chat\",\n        icon: _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        items: []\n    },\n    {\n        title: \"Profil Saya\",\n        url: \"/dashboard/profile\",\n        icon: _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        items: []\n    },\n    {\n        title: \"Langganan\",\n        url: \"/dashboard/subscription\",\n        icon: _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        items: []\n    }\n];\nconst navSecondaryData = [\n    {\n        title: \"Help\",\n        url: \"#\",\n        icon: _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        title: \"Logout\",\n        url: \"/logout\",\n        icon: _barrel_optimize_names_CreditCard_Heart_HelpCircle_LayoutDashboard_LogOut_MessageSquare_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    }\n];\nfunction AppSidebar(param) {\n    let { ...props } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"Loading...\",\n        email: \"<EMAIL>\",\n        avatar: null\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppSidebar.useEffect\": ()=>{\n            fetchUserProfile();\n        }\n    }[\"AppSidebar.useEffect\"], []);\n    const fetchUserProfile = async ()=>{\n        try {\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) {\n                router.replace('/login');\n                return;\n            }\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/auth/profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                if (response.status === 401) {\n                    localStorage.removeItem('pairsona_token');\n                    router.replace('/login');\n                    return;\n                }\n                throw new Error('Failed to fetch profile');\n            }\n            const profile = await response.json();\n            setUser({\n                name: profile.name || 'User',\n                email: profile.email || '<EMAIL>',\n                avatar: profile.image || null\n            });\n        } catch (err) {\n            console.error('Failed to fetch user profile:', err);\n        // Keep default values if fetch fails\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.Sidebar, {\n        variant: \"inset\",\n        ...props,\n        className: \"bg-[#D0544D] border-r border-[#B8453F] shadow-sm text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenu, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuItem, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarMenuButton, {\n                            size: \"lg\",\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/dashboard\",\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logowhite.png\",\n                                    alt: \"Pairsona Logo\",\n                                    className: \"h-12 w-auto object-contain\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_main__WEBPACK_IMPORTED_MODULE_3__.NavMain, {\n                        items: navMainData\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_secondary__WEBPACK_IMPORTED_MODULE_4__.NavSecondary, {\n                        items: navSecondaryData,\n                        className: \"mt-auto\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_user__WEBPACK_IMPORTED_MODULE_5__.NavUser, {\n                    user: user\n                }, void 0, false, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"I0XtUPu7Y8AlLqgOXzOcMtim8sI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});