import { Modu<PERSON> } from '@nestjs/common';
import { ChatGateway } from './chat.gateway';
import { ChatService } from './chat.service';
import { MongooseModule } from '@nestjs/mongoose';
import { UserOnline, UserOnlineSchema } from './schemas/user-online.schema';
import { Chat, ChatSchema } from './schemas/chat.schema';
import { ChatMessage, ChatMessageSchema } from './schemas/chat-message.schema';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt'
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ChatController } from './chat.controller';
import { UserModule } from 'src/user/user.module';
import { UserOnlineService } from './user-online.service';
import { Match, MatchSchema } from 'src/match/schemas/match.schema';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('app.jwt.secret'),
        signOptions: {
          expiresIn: configService.get<string>('app.jwt.expiresIn'),
        },
      }),
    }),
    MongooseModule.forFeature([
      { name: UserOnline.name, schema: UserOnlineSchema },
      { name: Chat.name, schema: ChatSchema },
      { name: ChatMessage.name, schema: ChatMessageSchema },
      { name: Match.name, schema: MatchSchema },
    ]),
    UserModule
  ],
  providers: [ChatGateway, ChatService, UserOnlineService],
  controllers: [ChatController]
})
export class ChatModule {}
