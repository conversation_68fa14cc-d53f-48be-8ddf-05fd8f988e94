// BFI-2-S Questions (30 items, 6 per dimension)
export const BFI_QUESTIONS = [
  // Extraversion (E)
  { id: 'BFI_E1', dimension: 'extraversion', text: '<PERSON>a ramah.' },
  { id: 'BFI_E2', dimension: 'extraversion', text: '<PERSON>a be<PERSON> tegas.' },
  { id: 'BFI_E3', dimension: 'extraversion', text: 'Saya penuh energi.' },
  { id: 'BFI_E4', dimension: 'extraversion', text: 'Saya bukan orang yang menyendiri.' },
  { id: 'BFI_E5', dimension: 'extraversion', text: 'Saya suka menjadi pusat perhatian.' },
  { id: 'BFI_E6', dimension: 'extraversion', text: 'Saya merasa nyaman saat berbicara di depan orang banyak.' },

  // Agreeableness (A)
  { id: 'BFI_A1', dimension: 'agreeableness', text: 'Saya peduli terhadap perasaan orang lain.' },
  { id: 'BFI_A2', dimension: 'agreeableness', text: '<PERSON>a men<PERSON> pandangan orang lain.' },
  { id: 'BFI_A3', dimension: 'agreeableness', text: 'Saya mempercayai orang lain.' },
  { id: 'BFI_A4', dimension: 'agreeableness', text: 'Saya mudah berempati.' },
  { id: 'BFI_A5', dimension: 'agreeableness', text: 'Saya jarang merasa sinis terhadap orang.' },
  { id: 'BFI_A6', dimension: 'agreeableness', text: 'Saya suka bekerja sama.' },

  // Conscientiousness (C)
  { id: 'BFI_C1', dimension: 'conscientiousness', text: 'Saya teratur dan rapi.' },
  { id: 'BFI_C2', dimension: 'conscientiousness', text: 'Saya bekerja keras mencapai target.' },
  { id: 'BFI_C3', dimension: 'conscientiousness', text: 'Saya bertanggung jawab.' },
  { id: 'BFI_C4', dimension: 'conscientiousness', text: 'Saya selalu mempersiapkan diri.' },
  { id: 'BFI_C5', dimension: 'conscientiousness', text: 'Saya disiplin.' },
  { id: 'BFI_C6', dimension: 'conscientiousness', text: 'Saya jarang menunda-nunda.' },

  // Negative Emotionality (N)
  { id: 'BFI_N1', dimension: 'negativeEmotionality', text: 'Saya mudah merasa cemas.' },
  { id: 'BFI_N2', dimension: 'negativeEmotionality', text: 'Saya kadang merasa sedih.' },
  { id: 'BFI_N3', dimension: 'negativeEmotionality', text: 'Saya cepat marah/emosi.' },
  { id: 'BFI_N4', dimension: 'negativeEmotionality', text: 'Saya sering merasa khawatir.' },
  { id: 'BFI_N5', dimension: 'negativeEmotionality', text: 'Saya mudah stres.' },
  { id: 'BFI_N6', dimension: 'negativeEmotionality', text: 'Saya rentan emosi naik-turun.' },

  // Open-Mindedness (O)
  { id: 'BFI_O1', dimension: 'openMindedness', text: 'Saya suka belajar hal baru.' },
  { id: 'BFI_O2', dimension: 'openMindedness', text: 'Saya memiliki imajinasi kreatif.' },
  { id: 'BFI_O3', dimension: 'openMindedness', text: 'Saya sensitif terhadap keindahan.' },
  { id: 'BFI_O4', dimension: 'openMindedness', text: 'Saya menikmati ide-ide abstrak.' },
  { id: 'BFI_O5', dimension: 'openMindedness', text: 'Saya penasaran tentang dunia.' },
  { id: 'BFI_O6', dimension: 'openMindedness', text: 'Saya berpikiran terbuka.' },
] as const;

// Self-disclosure Questions (20 items)
export const SELF_DISCLOSURE_QUESTIONS = [
  // Keyakinan & Nilai
  { id: 'SD_KN1', category: 'keyakinan_nilai', text: 'Saya sering menyampaikan pandangan religius/pribadi.' },
  { id: 'SD_KN2', category: 'keyakinan_nilai', text: 'Saya membicarakan pendapat politik.' },
  { id: 'SD_KN3', category: 'keyakinan_nilai', text: 'Saya menyampaikan nilai-nilai moral.' },
  { id: 'SD_KN4', category: 'keyakinan_nilai', text: 'Saya berbagi gagasan idealis.' },

  // Hubungan Interpersonal
  { id: 'SD_HI1', category: 'hubungan_interpersonal', text: 'Saya berbagi pengalaman keluarga.' },
  { id: 'SD_HI2', category: 'hubungan_interpersonal', text: 'Saya membicarakan dinamika hubungan pribadi.' },
  { id: 'SD_HI3', category: 'hubungan_interpersonal', text: 'Saya bercerita tentang persahabatan penting.' },
  { id: 'SD_HI4', category: 'hubungan_interpersonal', text: 'Saya berbagi masalah sosial.' },

  // Urusan Pribadi
  { id: 'SD_UP1', category: 'urusan_pribadi', text: 'Saya mengungkap pengalaman traumatis.' },
  { id: 'SD_UP2', category: 'urusan_pribadi', text: 'Saya bercerita tentang penyakit/masalah kesehatan.' },
  { id: 'SD_UP3', category: 'urusan_pribadi', text: 'Saya membuka kerentanan pribadi.' },
  { id: 'SD_UP4', category: 'urusan_pribadi', text: 'Saya berbagi kesalahan/penyesalan.' },

  // Ketertarikan & Minat
  { id: 'SD_KM1', category: 'ketertarikan_minat', text: 'Saya membicarakan hobi besar saya.' },
  { id: 'SD_KM2', category: 'ketertarikan_minat', text: 'Saya berbagi tentang musik/film favorit.' },
  { id: 'SD_KM3', category: 'ketertarikan_minat', text: 'Saya bercerita tentang kegiatan kreatif.' },
  { id: 'SD_KM4', category: 'ketertarikan_minat', text: 'Saya berbagi rencana liburan impian.' },

  // Perasaan Intim
  { id: 'SD_PI1', category: 'perasaan_intim', text: 'Saya mengungkapkan ketakutan terdalam.' },
  { id: 'SD_PI2', category: 'perasaan_intim', text: 'Saya menyatakan harapan/beban emosional.' },
  { id: 'SD_PI3', category: 'perasaan_intim', text: 'Saya mengatakan perasaan cinta/rasa sayang.' },
  { id: 'SD_PI4', category: 'perasaan_intim', text: 'Saya membahas konflik emosional terdalam.' },
] as const;

// Interface untuk dimensi BFI
export interface BfiDimensionInfo {
  id: BfiDimension;
  name: string;
  description: string;
}

// Deskripsi untuk setiap dimensi BFI
export const BFI_DIMENSIONS: BfiDimensionInfo[] = [
  {
    id: 'extraversion',
    name: 'Ekstraversi',
    description: 'Menggambarkan tingkat kenyamanan seseorang dalam berinteraksi dengan orang lain dan kecenderungan untuk mencari rangsangan dari lingkungan sosial.'
  },
  {
    id: 'agreeableness',
    name: 'Kesetujuan',
    description: 'Menggambarkan kecenderungan untuk bersikap kooperatif dan peduli terhadap orang lain, dibandingkan dengan sikap yang lebih menantang atau tidak kooperatif.'
  },
  {
    id: 'conscientiousness',
    name: 'Keteraturan',
    description: 'Menggambarkan tingkat disiplin diri, ketelitian, dan dorongan untuk mencapai tujuan yang telah ditetapkan.'
  },
  {
    id: 'negativeEmotionality',
    name: 'Stabilitas Emosi',
    description: 'Menggambarkan kecenderungan untuk mengalami emosi negatif seperti kecemasan, kemarahan, atau depresi.'
  },
  {
    id: 'openMindedness',
    name: 'Keterbukaan terhadap Pengalaman',
    description: 'Menggambarkan apresiasi terhadap seni, emosi, petualangan, ide-ide yang tidak biasa, imajinasi, rasa ingin tahu, dan berbagai pengalaman.'
  }
];

// Interface untuk kategori Self-Disclosure
export interface SelfDisclosureCategoryInfo {
  id: SelfDisclosureCategory;
  name: string;
  description: string;
}

// Deskripsi untuk setiap kategori Self-Disclosure
export const SELF_DISCLOSURE_CATEGORIES: SelfDisclosureCategoryInfo[] = [
  {
    id: 'keyakinan_nilai',
    name: 'Keyakinan & Nilai',
    description: 'Mengungkapkan pandangan pribadi tentang agama, politik, moral, dan ide-ide idealis.'
  },
  {
    id: 'hubungan_interpersonal',
    name: 'Hubungan Interpersonal',
    description: 'Berbagi informasi tentang hubungan dengan keluarga, teman, dan interaksi sosial lainnya.'
  },
  {
    id: 'urusan_pribadi',
    name: 'Urusan Pribadi',
    description: 'Mengungkapkan pengalaman pribadi yang sensitif, masalah kesehatan, atau kerentanan diri.'
  },
  {
    id: 'ketertarikan_minat',
    name: 'Ketertarikan & Minat',
    description: 'Berbagi tentang hobi, preferensi hiburan, dan rencana pribadi.'
  },
  {
    id: 'perasaan_intim',
    name: 'Perasaan Intim',
    description: 'Mengungkapkan perasaan terdalam, ketakutan, harapan, dan konflik emosional.'
  }
];

// Export types for better type safety
export type BfiQuestion = typeof BFI_QUESTIONS[number];
export type BfiDimension = BfiQuestion['dimension'];

export type SelfDisclosureQuestion = typeof SELF_DISCLOSURE_QUESTIONS[number];
export type SelfDisclosureCategory = SelfDisclosureQuestion['category'];

// Helper function to get questions by dimension/category
export const getQuestionsByDimension = (dimension: BfiDimension) => 
  BFI_QUESTIONS.filter(q => q.dimension === dimension);

export const getQuestionsByCategory = (category: SelfDisclosureCategory) =>
  SELF_DISCLOSURE_QUESTIONS.filter(q => q.category === category);
