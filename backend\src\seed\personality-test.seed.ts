import { Injectable, Logger } from '@nestjs/common';
import { UserDocument } from '../user/schemas/user.schema';
import { PersonalityService } from 'src/personality/services/personality.service';
import { QuestionService } from 'src/personality/services/question.service';
import { UserService } from 'src/user/user.service';

@Injectable()
export class PersonalityTestSeeder {
  private readonly logger = new Logger(PersonalityTestSeeder.name);

  constructor(
    private readonly personalityService: PersonalityService,
    private readonly questionService: QuestionService,
    private readonly usersService: UserService
    
  ) {}

  async seed() {
    try {
      // Check if tests already exist
      const testCount = await this.personalityService.getAllPersonalityTests();
      if (testCount.length > 0) {
        this.logger.log('Personality tests already seeded');
        return;
      }

      // Get all users who have completed the test
      const users = await this.usersService.findUsersWithCompletedPersonalityTest();
      
      if (users.length === 0) {
        this.logger.log('No users with completed tests found');
        return;
      }

      // Generate test results for each user
      for (const user of users) {
        await this.generateTestForUser(user);
      }

      this.logger.log(`Seeded personality tests for ${users.length} users`);
    } catch (error) {
      this.logger.error('Error seeding personality tests:', error);
      throw error;
    }
  }

  private async generateTestForUser(user: UserDocument) {
    // Generate answers for BFI questions
    const bfiAnswers = await this.generateBfiAnswers();
    
    // Generate answers for Self-Disclosure questions
    const selfDisclosureAnswers = await this.generateSelfDisclosureAnswers();

    // Create test document
    const test = await this.personalityService.submitTest(user.id, {
      bfiAnswers,
      selfDisclosureAnswers
    });

    await test.save();
  }

  private async generateBfiAnswers(): Promise<Record<string, number>> {
    const bfiQuestions = await this.questionService.findAllBfiQuestions();
    const answers: Record<string, number> = {};

    bfiQuestions.forEach((question) => {
      const value = Math.floor(Math.random() * 5) + 1;
      answers[question.questionId] = value;
      this.logger.log(`Generated answer for question ${question.questionId}: ${value}`);
    });
    
    return answers;
  }

  private async generateSelfDisclosureAnswers(): Promise<Record<string, number>> {
    const bfiQuestions = await this.questionService.findAllSelfDisclosureQuestions();
    const answers: Record<string, number> = {};

    bfiQuestions.forEach((question) => {
      // Generate random number between 1 and 5
      const value = Math.floor(Math.random() * 5) + 1;
      answers[question.questionId] = value;
      this.logger.log(`Generated answer for question ${question.questionId}: ${value}`);
    });
    
    return answers;
  }
}
