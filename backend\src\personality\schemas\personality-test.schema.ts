import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type PersonalityTestDocument = PersonalityTest & Document;

@Schema({ _id: false })
export class BfiAnswer {
  @Prop({ required: true })
  questionId: string;

  @Prop({ required: true, min: 0, max: 5 })
  value: number;

  @Prop({ required: true })
  dimension: string;
}

@Schema({ _id: false })
export class SelfDisclosureAnswer {
  @Prop({ required: true })
  questionId: string;

  @Prop({ required: true, min: 0, max: 5 })
  value: number;

  @Prop({ required: true })
  category: string;
}

@Schema({ _id: false })
export class BFI {
  @Prop({ required: true, min: 0, max: 1 })
  extraversion: number;

  @Prop({ required: true, min: 0, max: 1 })
  agreeableness: number;

  @Prop({ required: true, min: 0, max: 1 })
  conscientiousness: number;

  @Prop({ required: true, min: 0, max: 1 })
  negativeEmotionality: number;

  @Prop({ required: true, min: 0, max: 1 })
  openMindedness: number;
}

@Schema({ timestamps: true, collection: 'personality-tests' })
export class PersonalityTest {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true, unique: true })
  userId: Types.ObjectId;

  @Prop({ type: BFI, default: {} })
  bfi: BFI;

  @Prop({ type: [BfiAnswer], required: true })
  bfiAnswers: BfiAnswer[];

  @Prop({ min: 0, max: 1, default: 0 })
  selfDisclosure: number;

  @Prop({ type: [SelfDisclosureAnswer], required: true })
  selfDisclosureAnswers: SelfDisclosureAnswer[];

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const PersonalityTestSchema = SchemaFactory.createForClass(PersonalityTest);

// Create a unique index on userId to ensure one test result per user
PersonalityTestSchema.index({ userId: 1 }, { unique: true });