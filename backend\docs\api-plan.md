# Subscription & Billing API Documentation

This document provides detailed information about the Subscription, Billing, and Plan API endpoints, request/response formats, and examples.

## Table of Contents
- [Plans](#plans)
  - [Get All Plans](#get-all-plans)
- [Subscriptions](#subscriptions)
  - [Get My Subscription](#get-my-subscription)
  - [Get My Resources](#get-my-resources)
  - [Enroll in a Plan](#enroll-in-a-plan)
- [Add-ons](#add-ons)
  - [Get Available Add-ons](#get-available-add-ons)
  - [Purchase Add-ons](#purchase-add-ons)
- [Invoices](#invoices)
  - [Get My Invoices](#get-my-invoices)
  - [Get Invoice by ID](#get-invoice-by-id)
  - [Pay Invoice](#pay-invoice)
- [Data Types](#data-types)
  - [Plan Response](#plan-response)
  - [Subscription Response](#subscription-response)
  - [Subscription Resources](#subscription-resources)
  - [Invoice Response](#invoice-response)
  - [Add-on Response](#add-on-response)

## Base URL
```
{{base_url}}/plans
```

## Authentication
All endpoints require JWT authentication unless otherwise specified. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

# Plans

## Get All Plans

Retrieve a list of all active subscription plans.

### Endpoint
```
GET /
```

### Response
```typescript
{
  "id": string;
  "code": string;
  "name": string;
  "description": string;
  "price": number;
  "interval": "monthly" | "yearly";
  "psychologistConsultations": number;
  "chats": number;
  "features": Record<string, boolean>;
  "status": "active" | "inactive" | "deprecated";
  "sortOrder": number;
  "isPopular": boolean;
  "isDefault": boolean;
  "isHidden": boolean;
}[]
```

### Example Response
```json
[
  {
    "id": "60d21b4667d0d8992e610c85",
    "code": "premium",
    "name": "Premium Plan",
    "description": "Unlimited access to all features",
    "price": 299000,
    "interval": "monthly",
    "psychologistConsultations": 4,
    "chats": 10,
    "features": {
      "unlimitedChat": true,
      "prioritySupport": true,
      "discounts": true
    },
    "status": "active",
    "sortOrder": 1,
    "isPopular": true,
    "isDefault": false,
    "isHidden": false
  }
]
```

# Subscriptions

## Get My Subscription

Get the current user's active subscription details.

### Endpoint
```
GET /subscription
```

### Response
```typescript
{
  "id": string;
  "userId": string;
  "plan": {
    "id": string;
    "name": string;
    "description": string;
    "psychologistConsultations": number;
    "chats": number;
  };
  "startDate": string;  // ISO date string
  "endDate": string;    // ISO date string
  "status": "active" | "expired" | "cancelled";
  "autoRenew": boolean;
  "resources": {
    "psychologistConsultations": number;
    "chats": number;
  };
  "usage": {
    "psychologistConsultationsCount": number;
    "chatsCount": number;
  };
  "addOnResources": {
    "psychologistConsultations": number;
    "chats": number;
  };
  "addOnUsage": {
    "psychologistConsultationsCount": number;
    "chatsCount": number;
  };
}
```

## Get My Resources

Get the current user's available resources and usage.

### Endpoint
```
GET /subscription/resources
```

### Response
```typescript
{
  "psychologistConsultations": {
    "plan": {
      "available": number;
      "total": number;
    };
    "addon": {
      "available": number;
      "total": number;
    };
  };
  "chats": {
    "plan": {
      "available": number;
      "total": number;
    };
    "addon": {
      "available": number;
      "total": number;
    };
  };
}
```

## Enroll in a Plan

Enroll the current user in a subscription plan.

### Endpoint
```
POST /subscription/enroll
```

### Request Body
```typescript
{
  "planId": string;     // ID of the plan to enroll in
  "isTrial"?: boolean;  // Optional: Whether to start a trial
}
```

### Response
Returns the created invoice for the subscription.

# Add-ons

## Get Available Add-ons

Get a list of available add-ons.

### Endpoint
```
GET /addon
```

### Response
```typescript
{
  "id": string;
  "name": string;
  "description": string;
  "price": number;
  "type": "consultation" | "chat";
  "quantity": number;
  "isActive": boolean;
  "sortOrder": number;
}[]
```

## Purchase Add-ons

Purchase additional resources as add-ons.

### Endpoint
```
POST /addon
```

### Request Body
```typescript
{
  "items": Array<{
    "addonId": string;  // ID of the add-on to purchase
    "quantity": number; // Number of units to purchase
  }>;
}
```

### Response
Returns the created invoice for the add-on purchase.

# Invoices

## Get My Invoices

Get a paginated list of the current user's invoices.

### Endpoint
```
GET /invoices?page=1&limit=10
```

### Query Parameters
| Parameter | Type    | Required | Default | Description          |
|-----------|---------|----------|---------|----------------------|
| page      | number  | No       | 1       | Page number          |
| limit     | number  | No       | 10      | Items per page       |
| status    | string  | No       | -       | Filter by status     |

## Get Invoice by ID

Get details of a specific invoice.

### Endpoint
```
GET /invoices/:id
```

### Path Parameters
| Parameter | Type   | Required | Description          |
|-----------|--------|----------|----------------------|
| id        | string | Yes      | The ID of the invoice|

## Pay Invoice

Initiate payment for an invoice.

### Endpoint
```
POST /invoices/pay
```

### Request Body
```typescript
{
  "invoiceId": string;      // ID of the invoice to pay
  "paymentMethod": string;  // Payment method (e.g., 'credit_card', 'bank_transfer')
  "paymentDetails"?: any;   // Additional payment details
}
```

# Plans

## Get Plan by ID

Get details of a specific plan.

### Endpoint
```
GET /plans/:id
```

### Path Parameters
| Parameter | Type   | Required | Description          |
|-----------|--------|----------|----------------------|
| id        | string | Yes      | The ID of the plan   |

### Response
```typescript
{
  "id": string;                        // Unique identifier
  "code": string;                      // Plan code
  "name": string;                      // Plan name
  "description": string;               // Optional description
  "price": number;                     // Price in IDR
  "interval": "monthly" | "yearly";    // Billing interval
  "psychologistConsultations": number; // Number of included consultations
  "chats": number;                     // Number of included chat sessions
  "features": Record<string, any>;     // Additional features
  "status": "active" | "inactive" | "deprecated";
  "sortOrder": number;                 // Display order
  "isPopular": boolean;                // Whether plan is marked as popular
  "isDefault": boolean;                // Whether plan is default
  "isHidden": boolean;                 // Whether plan is hidden from public
  "createdAt": string;                 // ISO date string
  "updatedAt": string;                 // ISO date string
}
```
