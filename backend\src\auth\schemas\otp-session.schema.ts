import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { AuthProvider } from '../dto/signin.dto';

@Schema({ collection: 'otp-sessions', timestamps: true })
export class OtpSession {
  @Prop({ required: true })
  otpId: string;

  @Prop({ required: true })
  provider: AuthProvider;

  @Prop({ required: true })
  to: string;
}

export type OtpSessionDocument = OtpSession & Document;
export const OtpSessionSchema = SchemaFactory.createForClass(OtpSession);
