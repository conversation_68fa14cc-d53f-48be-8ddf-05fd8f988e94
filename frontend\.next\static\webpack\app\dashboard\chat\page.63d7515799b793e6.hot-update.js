"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/chat/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/chat/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/dashboard/chat/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatPage() {\n    _s();\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [selectedChat, setSelectedChat] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [messagesLoading, setMessagesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [currentUserId, setCurrentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            fetchChats();\n            getCurrentUserId();\n            return ({\n                \"ChatPage.useEffect\": ()=>{\n                    if (socket) {\n                        console.log('Cleaning up socket connection');\n                        socket.disconnect();\n                    }\n                }\n            })[\"ChatPage.useEffect\"];\n        }\n    }[\"ChatPage.useEffect\"], []);\n    // Auto-select first chat when chats are loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            if (chats.length > 0 && !selectedChat) {\n                console.log('Auto-selecting first chat:', chats[0]);\n                handleChatSelect(chats[0]);\n            }\n        }\n    }[\"ChatPage.useEffect\"], [\n        chats,\n        selectedChat\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatPage.useEffect\"], [\n        messages\n    ]);\n    const getCurrentUserId = async ()=>{\n        try {\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) return;\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/auth/profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const profile = await response.json();\n                setCurrentUserId(profile.id);\n            }\n        } catch (err) {\n            console.error('Failed to get current user ID:', err);\n        }\n    };\n    const fetchChats = async ()=>{\n        try {\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) {\n                router.replace('/login');\n                return;\n            }\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/chats\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                if (response.status === 401) {\n                    localStorage.removeItem('pairsona_token');\n                    router.replace('/login');\n                    return;\n                }\n                throw new Error('Failed to fetch chats');\n            }\n            const chatsData = await response.json();\n            console.log('Fetched chats:', chatsData);\n            setChats(chatsData);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load chats');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchMessages = async (chatId)=>{\n        try {\n            setMessagesLoading(true);\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) return;\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/chats/\").concat(chatId, \"/messages?page=1&limit=50\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch messages');\n            }\n            const messagesData = await response.json();\n            setMessages(messagesData.data.reverse()); // Reverse to show oldest first\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load messages');\n        } finally{\n            setMessagesLoading(false);\n        }\n    };\n    const connectToChat = (chatId)=>{\n        const token = localStorage.getItem('pairsona_token');\n        if (!token) return;\n        // Disconnect existing socket\n        if (socket) {\n            socket.disconnect();\n        }\n        console.log('Connecting to chat with chatId:', chatId);\n        console.log('Using Socket URL:', \"https://api.pairsona.id\");\n        console.log('Using token:', token ? 'Token available' : 'No token');\n        // Try multiple approaches for authorization\n        const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_7__.io)(\"\".concat(\"https://api.pairsona.id\", \"/chat\"), {\n            transports: [\n                'websocket'\n            ],\n            auth: {\n                token: \"Bearer \".concat(token)\n            },\n            extraHeaders: {\n                'Authorization': \"Bearer \".concat(token),\n                'authorization': \"Bearer \".concat(token) // lowercase version\n            },\n            query: {\n                chatId: chatId,\n                token: \"Bearer \".concat(token) // Also try in query\n            },\n            forceNew: true,\n            reconnection: true,\n            reconnectionAttempts: 3,\n            reconnectionDelay: 1000\n        });\n        newSocket.on('connect', ()=>{\n            console.log('✅ Connected to chat server, chatId:', chatId);\n            console.log('Socket ID:', newSocket.id);\n        });\n        newSocket.on('message', (message)=>{\n            console.log('📨 New message received:', message);\n            setMessages((prev)=>[\n                    ...prev,\n                    message\n                ]);\n            // Update last message in chats list\n            setChats((prev)=>prev.map((chat)=>chat.id === message.chatId ? {\n                        ...chat,\n                        lastMessage: {\n                            id: message.id,\n                            senderId: message.senderId,\n                            message: message.message,\n                            sentAt: message.sentAt\n                        }\n                    } : chat));\n        });\n        newSocket.on('disconnect', (reason)=>{\n            console.log('❌ Disconnected from chat server, reason:', reason);\n        });\n        newSocket.on('connect_error', (error)=>{\n            console.error('🚫 Socket connection error:', error);\n            console.error('Error details:', error.message, error.description, error.context);\n            setError('Failed to connect to chat server. Please try again.');\n        });\n        newSocket.on('error', (error)=>{\n            console.error('⚠️ Socket error:', error);\n            setError('Connection error. Please try again.');\n        });\n        // Additional debugging events\n        newSocket.on('connect_timeout', ()=>{\n            console.error('⏰ Connection timeout');\n        });\n        newSocket.on('reconnect', (attemptNumber)=>{\n            console.log('🔄 Reconnected after', attemptNumber, 'attempts');\n        });\n        newSocket.on('reconnect_error', (error)=>{\n            console.error('🔄❌ Reconnection error:', error);\n        });\n        setSocket(newSocket);\n    };\n    const handleChatSelect = (chat)=>{\n        console.log('Selecting chat:', chat);\n        setSelectedChat(chat);\n        setMessages([]);\n        fetchMessages(chat.id);\n        connectToChat(chat.id);\n    };\n    const sendMessage = async ()=>{\n        if (!newMessage.trim() || !selectedChat || !socket || sending) return;\n        console.log('Sending message:', newMessage.trim());\n        console.log('Socket connected:', socket.connected);\n        console.log('Selected chat:', selectedChat.id);\n        try {\n            setSending(true);\n            if (!socket.connected) {\n                console.error('Socket not connected, attempting to reconnect...');\n                connectToChat(selectedChat.id);\n                setError('Connection lost. Please try again.');\n                return;\n            }\n            socket.emit('sendMessage', {\n                message: newMessage.trim()\n            });\n            console.log('Message emitted successfully');\n            setNewMessage('');\n        } catch (err) {\n            console.error('Error sending message:', err);\n            setError('Failed to send message');\n        } finally{\n            setSending(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const formatTime = (dateString)=>{\n        return new Date(dateString).toLocaleTimeString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    const formatLastMessageTime = (dateString)=>{\n        const now = new Date();\n        const messageDate = new Date(dateString);\n        const diffInHours = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return 'Now';\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n        return messageDate.toLocaleDateString();\n    };\n    const getInitials = (name)=>{\n        return name.split(' ').map((n)=>n[0]).join('').toUpperCase();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Chat\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Loading your conversations...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"md:col-span-1 h-[calc(100vh-200px)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2 text-[#D0544D]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Conversations\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: Array.from({\n                                            length: 3\n                                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 rounded-full bg-gray-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, i, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"md:col-span-2 h-[calc(100vh-200px)]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-8 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-8 h-8 animate-spin text-[#D0544D]\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n            lineNumber: 332,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Chat\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Connect with your matches.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 mb-4\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchChats,\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n            lineNumber: 373,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Chat\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Connect with your matches.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"md:col-span-1 h-[calc(100vh-200px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-[#D0544D]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Conversations\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-0\",\n                                children: chats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No conversations yet\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Start connecting with your matches to begin chatting!\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divide-y\",\n                                    children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 cursor-pointer hover:bg-[#F2E7DB]/50 transition-colors \".concat((selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id) === chat.id ? 'bg-[#F2E7DB]/30' : ''),\n                                            onClick: ()=>handleChatSelect(chat),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                                className: \"w-10 h-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                                        src: chat.user.image || undefined,\n                                                                        alt: chat.user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                        className: \"bg-[#D0544D]/20 text-[#D0544D]\",\n                                                                        children: getInitials(chat.user.name)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            chat.user.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium truncate\",\n                                                                children: chat.user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground truncate\",\n                                                                children: chat.lastMessage ? chat.lastMessage.message : 'No messages yet'\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: chat.lastMessage ? formatLastMessageTime(chat.lastMessage.sentAt) : ''\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, chat.id, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"md:col-span-2 h-[calc(100vh-200px)] flex flex-col\",\n                        children: selectedChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                    className: \"border-b\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mr-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                            className: \"w-8 h-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                                    src: selectedChat.user.image || undefined,\n                                                                    alt: selectedChat.user.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                    className: \"bg-[#D0544D]/20 text-[#D0544D]\",\n                                                                    children: getInitials(selectedChat.user.name)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedChat.user.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: selectedChat.user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: selectedChat.user.isOnline ? 'Online' : 'Offline'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"flex-1 overflow-auto p-4\",\n                                    children: messagesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 animate-spin text-[#D0544D]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                        children: \"No messages yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"Start the conversation by sending a message!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 23\n                                            }, this) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(message.senderId === currentUserId ? 'justify-end' : 'justify-start'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg p-3 max-w-[80%] \".concat(message.senderId === currentUserId ? 'bg-[#D0544D] text-white' : 'bg-[#F2E7DB]'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: message.message\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1 \".concat(message.senderId === currentUserId ? 'text-white/70' : 'text-muted-foreground'),\n                                                                children: formatTime(message.sentAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, message.id, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 25\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: messagesEndRef\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                type: \"text\",\n                                                placeholder: \"Type a message...\",\n                                                value: newMessage,\n                                                onChange: (e)=>setNewMessage(e.target.value),\n                                                onKeyPress: handleKeyPress,\n                                                disabled: sending,\n                                                className: \"flex-1 focus:ring-[#D0544D] focus:border-[#D0544D]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: sendMessage,\n                                                disabled: !newMessage.trim() || sending,\n                                                className: \"bg-[#D0544D] hover:bg-[#D0544D]/90 text-white\",\n                                                children: sending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"flex-1 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"mx-auto h-16 w-16 text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-medium text-gray-900 mb-2\",\n                                        children: \"Select a conversation\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Choose a conversation from the sidebar to start chatting\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 549,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n        lineNumber: 390,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"S6ZjTg6MVYCshZyj22E3f9HdYt8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/chat/page.tsx\n"));

/***/ })

});