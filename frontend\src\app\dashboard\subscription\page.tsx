"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, CreditCard, Calendar, Users, MessageCircle, Star, Loader2, AlertCircle } from "lucide-react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

// Declare global snap for TypeScript
declare global {
  interface Window {
    snap: any;
  }
}

interface Plan {
  id: string;
  code: string;
  name: string;
  description: string;
  price: number;
  interval: "monthly" | "yearly";
  psychologistConsultations: number;
  chats: number;
  features: Record<string, boolean>;
  status: "active" | "inactive" | "deprecated";
  sortOrder: number;
  isPopular: boolean;
  isDefault: boolean;
  isHidden: boolean;
}

interface Subscription {
  id: string;
  userId: string;
  plan: {
    id: string;
    name: string;
    description: string;
    psychologistConsultations: number;
    chats: number;
  };
  startDate: string;
  endDate: string;
  status: "active" | "expired" | "cancelled";
  autoRenew: boolean;
  resources: {
    psychologistConsultations: number;
    chats: number;
  };
  usage: {
    psychologistConsultationsCount: number;
    chatsCount: number;
  };
  addOnResources: {
    psychologistConsultations: number;
    chats: number;
  };
  addOnUsage: {
    psychologistConsultationsCount: number;
    chatsCount: number;
  };
}

interface Invoice {
  id: string;
  userId: string;
  planId: string;
  items: Array<{
    id: string;
    description: string;
    quantity: number;
    price: number;
  }>;
  totals: Array<{
    type: string;
    description: string;
    amount: number;
  }>;
  paidDate: string | null;
  metadata: any;
  status: string;
  invoiceCode: string;
  invoiceLink: string | null;
  paymentLink: string | null;
  paymentToken: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function SubscriptionPage() {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [enrolling, setEnrolling] = useState<string | null>(null);
  const [payingInvoice, setPayingInvoice] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      };

      // Fetch plans, subscription, and invoices in parallel
      const [plansRes, subscriptionRes, invoicesRes] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/plans`, { headers }),
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/plans/subscription`, { headers }),
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/plans/invoices?page=1&limit=10`, { headers })
      ]);

      if (plansRes.ok) {
        const plansData = await plansRes.json();
        setPlans(plansData.filter((plan: Plan) => !plan.isHidden));
      }

      if (subscriptionRes.ok) {
        const subscriptionData = await subscriptionRes.json();
        setSubscription(subscriptionData);
      }

      if (invoicesRes.ok) {
        const invoicesData = await invoicesRes.json();
        setInvoices(invoicesData.data || invoicesData);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load subscription data');
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = async (planId: string) => {
    try {
      setEnrolling(planId);
      const token = localStorage.getItem('pairsona_token');
      if (!token) return;

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/plans/subscription/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId }),
      });

      if (!response.ok) {
        throw new Error('Failed to enroll in plan');
      }

      const invoice = await response.json();
      console.log('Invoice created:', invoice);

      // Open Midtrans Snap payment
      if (invoice.paymentToken && window.snap) {
        window.snap.pay(invoice.paymentToken, {
          onSuccess: async (result: any) => {
            console.log('Payment success:', result);
            await handlePaymentValidation(invoice.id, result.transaction_id, result.order_id);
          },
          onPending: (result: any) => {
            console.log('Payment pending:', result);
            setError('Payment is pending. Please complete your payment.');
          },
          onError: (result: any) => {
            console.log('Payment error:', result);
            setError('Payment failed. Please try again.');
          },
          onClose: () => {
            console.log('Payment popup closed');
            setError('Payment was cancelled.');
          }
        });
      } else {
        throw new Error('Payment token not available');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to enroll in plan');
    } finally {
      setEnrolling(null);
    }
  };

  const handlePaymentValidation = async (invoiceId: string, transactionId: string, orderId: string) => {
    try {
      const token = localStorage.getItem('pairsona_token');
      if (!token) return;

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/plans/invoices/validate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          invoiceId,
          transactionId,
          orderId
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to validate payment');
      }

      const result = await response.json();
      console.log('Payment validation result:', result);

      // Refresh data after successful payment
      await fetchData();
      setError(null);

      // Show success message
      alert('Payment successful! Your subscription has been activated.');

    } catch (err) {
      console.error('Payment validation error:', err);
      setError(err instanceof Error ? err.message : 'Failed to validate payment');
    }
  };

  const handlePayInvoice = (invoice: Invoice) => {
    if (invoice.paymentToken && window.snap) {
      setPayingInvoice(invoice.id);
      window.snap.pay(invoice.paymentToken, {
        onSuccess: async (result: any) => {
          console.log('Payment success:', result);
          await handlePaymentValidation(invoice.id, result.transaction_id, result.order_id);
          setPayingInvoice(null);
        },
        onPending: (result: any) => {
          console.log('Payment pending:', result);
          setError('Payment is pending. Please complete your payment.');
          setPayingInvoice(null);
        },
        onError: (result: any) => {
          console.log('Payment error:', result);
          setError('Payment failed. Please try again.');
          setPayingInvoice(null);
        },
        onClose: () => {
          console.log('Payment popup closed');
          setPayingInvoice(null);
          // Don't show error on close, user might just want to cancel
        }
      });
    } else {
      setError('Payment token not available');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Langganan</h1>
          <p className="text-muted-foreground">Memuat data langganan...</p>
        </div>
        <div className="grid gap-6 md:grid-cols-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Langganan</h1>
          <p className="text-muted-foreground">Kelola paket langganan dan penagihan Anda.</p>
        </div>
        <Card className="p-6">
          <div className="text-center">
            <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchData}>Try Again</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Langganan</h1>
        <p className="text-muted-foreground">Kelola paket langganan dan penagihan Anda.</p>
      </div>

      {/* Current Subscription */}
      {subscription ? (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Langganan Saat Ini</CardTitle>
            <CardDescription>Detail langganan aktif Anda</CardDescription>
          </CardHeader>
          <CardContent className="w-full">
            <div className="bg-[#F2E7DB]/50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-bold text-[#D0544D]">{subscription.plan.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    Aktif hingga {formatDate(subscription.endDate)}
                  </p>
                </div>
                <Badge
                  variant={subscription.status === 'active' ? 'default' : 'secondary'}
                  className={subscription.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                >
                  {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                </Badge>
              </div>

              {/* Resources Usage */}
              <div className="mt-4 border-t border-gray-200 pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium flex items-center">
                        <Users className="w-4 h-4 mr-2" />
                        Psychologist Consultations
                      </span>
                      <span className="text-sm">
                        {subscription.resources.psychologistConsultations - subscription.usage.psychologistConsultationsCount} / {subscription.resources.psychologistConsultations}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-[#D0544D] h-2 rounded-full"
                        style={{
                          width: `${(subscription.usage.psychologistConsultationsCount / subscription.resources.psychologistConsultations) * 100}%`
                        }}
                      ></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium flex items-center">
                        <MessageCircle className="w-4 h-4 mr-2" />
                        Chat Sessions
                      </span>
                      <span className="text-sm">
                        {subscription.resources.chats - subscription.usage.chatsCount} / {subscription.resources.chats}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-[#D0544D] h-2 rounded-full"
                        style={{
                          width: `${(subscription.usage.chatsCount / subscription.resources.chats) * 100}%`
                        }}
                      ></div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 flex gap-2">
                  <Button variant="outline" size="sm">
                    <CreditCard className="mr-2 h-4 w-4" />
                    Update Payment Method
                  </Button>
                  <Button variant="outline" size="sm">
                    <Calendar className="mr-2 h-4 w-4" />
                    {subscription.autoRenew ? 'Nonaktifkan' : 'Aktifkan'} Perpanjangan Otomatis
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="w-full">
          <CardHeader>
            <CardTitle>Tidak Ada Langganan Aktif</CardTitle>
            <CardDescription>Pilih paket untuk memulai</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">You don't have an active subscription. Choose from our plans below to unlock premium features.</p>
          </CardContent>
        </Card>
      )}

      {/* Available Plans */}
      <div className="w-full">
        <h2 className="text-2xl font-bold mb-4">Available Plans</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
          {plans
            .sort((a, b) => a.sortOrder - b.sortOrder)
            .map((plan) => {
              const isCurrentPlan = subscription?.plan.id === plan.id;
              const isEnrolling = enrolling === plan.id;

              return (
                <Card
                  key={plan.id}
                  className={`relative ${plan.isPopular ? 'border-[#D0544D] shadow-lg' : ''}`}
                >
                  {plan.isPopular && (
                    <div className="bg-[#D0544D] text-white text-center py-1 text-sm font-medium">
                      <Star className="inline w-4 h-4 mr-1" />
                      POPULAR
                    </div>
                  )}

                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      {plan.name}
                      {isCurrentPlan && (
                        <Badge className="bg-green-100 text-green-800">Current</Badge>
                      )}
                    </CardTitle>
                    <CardDescription>{plan.description}</CardDescription>
                    <div className="mt-2">
                      <span className="text-3xl font-bold">
                        {plan.price === 0 ? 'Free' : formatPrice(plan.price)}
                      </span>
                      {plan.price > 0 && (
                        <span className="text-muted-foreground">/{plan.interval}</span>
                      )}
                    </div>
                  </CardHeader>

                  <CardContent>
                    <ul className="space-y-2 mb-6">
                      <li className="flex items-center">
                        <Users className="h-4 w-4 mr-2 text-[#D0544D]" />
                        <span className="text-sm">
                          {plan.psychologistConsultations} Psychologist Consultations
                        </span>
                      </li>
                      <li className="flex items-center">
                        <MessageCircle className="h-4 w-4 mr-2 text-[#D0544D]" />
                        <span className="text-sm">
                          {plan.chats} Chat Sessions
                        </span>
                      </li>
                      {Object.entries(plan.features).map(([feature, enabled]) =>
                        enabled && (
                          <li key={feature} className="flex items-center">
                            <Check className="h-4 w-4 mr-2 text-green-500" />
                            <span className="text-sm capitalize">
                              {feature.replace(/([A-Z])/g, ' $1').toLowerCase()}
                            </span>
                          </li>
                        )
                      )}
                    </ul>

                    <Button
                      className={`w-full ${
                        isCurrentPlan
                          ? 'bg-gray-200 text-gray-800 hover:bg-gray-300'
                          : plan.isPopular
                            ? 'bg-[#D0544D] hover:bg-[#D0544D]/90'
                            : 'bg-gray-800 hover:bg-gray-700'
                      }`}
                      onClick={() => !isCurrentPlan && handleEnroll(plan.id)}
                      disabled={isCurrentPlan || isEnrolling}
                    >
                      {isEnrolling ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Enrolling...
                        </>
                      ) : isCurrentPlan ? (
                        'Current Plan'
                      ) : (
                        `Choose ${plan.name}`
                      )}
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
        </div>
      </div>

      {/* Billing History */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>Your recent invoices and transactions</CardDescription>
        </CardHeader>
        <CardContent className="w-full">
          {invoices.length === 0 ? (
            <div className="text-center py-8">
              <CreditCard className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No invoices yet</h3>
              <p className="text-gray-500">Your billing history will appear here once you make a purchase.</p>
            </div>
          ) : (
            <div className="overflow-x-auto w-full">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4">Date</th>
                    <th className="text-left py-3 px-4">Description</th>
                    <th className="text-left py-3 px-4">Amount</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-left py-3 px-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {invoices.map((invoice) => {
                    // Get the total amount from totals array
                    const totalAmount = invoice.totals.find(total => total.type === 'total')?.amount || 0;
                    // Get description from first item
                    const description = invoice.items.length > 0 ? invoice.items[0].description : 'Subscription';

                    return (
                      <tr key={invoice.id} className="border-b">
                        <td className="py-3 px-4">
                          {formatDate(invoice.createdAt)}
                        </td>
                        <td className="py-3 px-4">
                          {description}
                        </td>
                        <td className="py-3 px-4">
                          {formatPrice(totalAmount)}
                        </td>
                        <td className="py-3 px-4">
                          <Badge
                            variant={
                              invoice.status === 'paid' ? 'default' :
                              invoice.status === 'pending' ? 'secondary' :
                              'destructive'
                            }
                            className={
                              invoice.status === 'paid' ? 'bg-green-100 text-green-800' :
                              invoice.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }
                          >
                            {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          {invoice.status === 'pending' && invoice.paymentToken && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handlePayInvoice(invoice)}
                              disabled={payingInvoice === invoice.id}
                            >
                              {payingInvoice === invoice.id ? (
                                <>
                                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                                  Processing...
                                </>
                              ) : (
                                'Pay Now'
                              )}
                            </Button>
                          )}
                          {invoice.status === 'paid' && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                // Handle download receipt - you might want to implement this
                                console.log('Download receipt:', invoice.id);
                              }}
                            >
                              Receipt
                            </Button>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
