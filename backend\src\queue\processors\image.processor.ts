import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Logger } from "@nestjs/common";
import { Job } from "bullmq";
import { MatchService } from "src/match/match.service";
import { UserService } from "src/user/user.service"
import { QUEUE_IMAGE_NAME } from "src/constants/queue";

export enum ImageJobName {
  RESIZE = 'resize'
}

@Processor(QUEUE_IMAGE_NAME)
export class ImageProcessor extends WorkerHost {
  private readonly logger = new Logger(ImageProcessor.name);

  constructor(
    private readonly matchService: MatchService,
    private readonly userService: UserService
  ) {
    super()
  }

  async process(job: Job<any, any, string>): Promise<any> {
    try {
      return { success: true, message: 'Matchmaking processed successfully' };
    } catch (error) {
      this.logger.error(`Failed to process matchmaking job: ${error.message}`, error.stack);
      throw error;
    }
  }
}
