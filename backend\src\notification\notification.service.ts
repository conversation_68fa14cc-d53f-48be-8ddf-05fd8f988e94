import { forwardRef, Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EmailTemplate, MailService } from './mail.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UserService } from 'src/user/user.service';
import * as moment from 'moment';

export enum NotificationEvent {
  USER_SIGNUP = 'user.signup',
  SUBSCRIPTION_REMINDER = 'subscription.reminder',
  SUBSCRIPTION_CANCELLED = 'subscription.cancelled',
}

@Injectable()
export class NotificationService implements OnModuleInit {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    private readonly mailService: MailService,
    private readonly eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => UserService)) private readonly userService: UserService
  ) {}

  onModuleInit() {
    this.logger.log('Notification service initialized');

    this.eventEmitter.on(NotificationEvent.USER_SIGNUP, (data) => {
      this.logger.log(`User signup: ${JSON.stringify(data)}`);
      this.mailService.sendMail(data.email, 'Selamat Datang di Serius.ID!', EmailTemplate.USER_SIGNUP, {
        username: data.name
      });
    });

    this.eventEmitter.on(NotificationEvent.SUBSCRIPTION_REMINDER, (data) => {
      this.logger.log(`Subscription reminded: ${JSON.stringify(data)}`);
      this.sendSubscriptionRemindedNotification(data.userId, data);
    });

    this.eventEmitter.on(NotificationEvent.SUBSCRIPTION_CANCELLED, (data) => {
      this.logger.log(`Subscription cancelled: ${JSON.stringify(data)}`);
      this.sendSubscriptionCancelledNotification(data.userId, data);
    });
  }

  async sendSubscriptionRemindedNotification(userId: string, data: any) {
    try {
      const user = await this.userService.findOne(userId);
      this.mailService.sendMail(user.email, 'Subscription Reminder', EmailTemplate.SUBSCRIPTION_REMINDER, {
        username: user.name,
        end_date: moment(data.subscription.endDate).format('DD MMM YYYY')
      });
    } catch (error) {
      this.logger.error(`Failed to send subscription reminded notification to user ${userId}: ${error}`);
    }
  }

  async sendSubscriptionCancelledNotification(userId: string, data: any) {
    try {
      const user = await this.userService.findOne(userId);
      this.mailService.sendMail(user.email, 'Subscription Cancelled', EmailTemplate.SUBSCRIPTION_CANCELLED, {
        username: user.name,
        end_date: moment(data.subscription.endDate).format('DD MMM YYYY')
      });
    } catch (error) {
      this.logger.error(`Failed to send subscription cancelled notification to user ${userId}: ${error}`);
    }
  }
}
