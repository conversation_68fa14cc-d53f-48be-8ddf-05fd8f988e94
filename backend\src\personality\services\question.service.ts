import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  QuestionType,
  PersonalityQuestion,
  PersonalityQuestionDocument
} from '../schemas/question.schema';

@Injectable()
export class QuestionService {
  constructor(@InjectModel(PersonalityQuestion.name) private personalityQuestionModel: Model<PersonalityQuestionDocument>) {}

  async seedBfiQuestions(questions: Array<{
    questionId: string;
    text: string;
    dimension: string;
    order: number;
  }>) {
    await this.personalityQuestionModel.deleteMany({ questionType: QuestionType.BFI });
    
    const created = await this.personalityQuestionModel.insertMany(
      questions.map(q => ({
        questionId: q.questionId,
        questionType: QuestionType.BFI,
        text: q.text,
        dimension: q.dimension,
        order: q.order,
        isActive: true,
      }))
    );

    return created;
  }

  async seedSelfDisclosureQuestions(questions: Array<{
    questionId: string;
    text: string;
    category: string;
    order: number;
  }>) {
    await this.personalityQuestionModel.deleteMany({ questionType: QuestionType.SELF_DISCLOSURE });
    
    const created = await this.personalityQuestionModel.insertMany(
      questions.map(q => ({
        questionId: q.questionId,
        questionType: QuestionType.SELF_DISCLOSURE,
        text: q.text,
        category: q.category,
        order: q.order,
        isActive: true,
      }))
    );

    return created;
  }

  async findAllBfiQuestions() {
    return this.personalityQuestionModel.find({ questionType: QuestionType.BFI }).sort('order').exec();
  }

  async findAllSelfDisclosureQuestions() {
    return this.personalityQuestionModel.find({ questionType: QuestionType.SELF_DISCLOSURE }).sort('order').exec();
  }

  async findAllQuestions() {
    const [bfi, sd] = await Promise.all([
      this.findAllBfiQuestions(),
      this.findAllSelfDisclosureQuestions(),
    ]);
    
    return { bfi, selfDisclosure: sd };
  }
}
