import { Controller, Get, Param, Query, Request } from '@nestjs/common';
import { ChatService } from './chat.service';
import { RequestWithUser } from 'src/common/interfaces/request-with-user.interface';
import { ChatResponseDto } from './dto/chat-response.dto';
import { ChatMessageResponseDto } from './dto/chat-message-response.dto';
import { PaginatedResponseDto, PaginationDto } from 'src/common/dto/pagination.dto';

@Controller('chats')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  /**
   * Gets the user chats.
   * 
   * @param req The request object.
   * @returns The user chats.
   */
  @Get()
  async getChats(@Request() req: RequestWithUser): Promise<ChatResponseDto[]> {
    const chats = await this.chatService.getChatsByUserId(req.user.userId);
    
    return chats.map(chat => {
      let user: any;
      
      if (chat.user1Id.id === req.user.userId) {
        user = chat.user2Id;
      } else {
        user = chat.user1Id;
      }

      const lastMessage = chat.lastMessage as any || null;

      return {
        id: chat.id,
        matchId: chat.matchId,
        user: {
          id: user.id,
          name: user.name,
          image: user.image?.url || '',
          isOnline: false
        },
        lastMessage: lastMessage ? {
          id: lastMessage.id,
          senderId: lastMessage.senderId,
          message: lastMessage.message,
          sentAt: lastMessage.createdAt
        } : null
      };
    });
  }

  /**
   * Gets the chat messages.
   * 
   * @param req The request object.
   * @param chatId The chat id or match id.
   * @param query The pagination query.
   * @returns The chat messages.
   */
  @Get('/:chatId/messages')
  async getChatMessages(@Request() req: RequestWithUser, @Param('chatId') chatId: string, @Query() query: PaginationDto): Promise<PaginatedResponseDto<ChatMessageResponseDto>> {
    return await this.chatService.getChatMessages(chatId, query);
  }
}
