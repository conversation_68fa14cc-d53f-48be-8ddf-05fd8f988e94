"use client";

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Heart,
  MessageSquare,
  User,
  CreditCard,
  TrendingUp,
  Calendar,
  Users,
  CheckCircle,
  AlertCircle,
  Loader2,
  ArrowRight
} from "lucide-react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface DashboardStats {
  totalMatches: number;
  incomingInvitations: number;
  outgoingInvitations: number;
  connectedMatches: number;
  profileCompletion: number;
}

interface RecentMatch {
  id: string;
  user: {
    id: string;
    name: string;
    image: string | null;
    age: number;
    occupation: string;
  };
  matchScore: number;
  status: string;
}

interface Subscription {
  plan: {
    name: string;
  };
  endDate: string;
  status: string;
  resources: {
    psychologistConsultations: number;
    chats: number;
  };
  usage: {
    psychologistConsultationsCount: number;
    chatsCount: number;
  };
}

interface Profile {
  name: string;
  image?: string;
  psychTestCompleted: boolean;
  // Add other profile fields as needed
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalMatches: 0,
    incomingInvitations: 0,
    outgoingInvitations: 0,
    connectedMatches: 0,
    profileCompletion: 0,
  });
  const [recentMatches, setRecentMatches] = useState<RecentMatch[]>([]);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      };

      // Fetch all data in parallel
      const [
        allMatchesRes,
        incomingRes,
        outgoingRes,
        connectedRes,
        profileRes,
        subscriptionRes
      ] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/match`, { headers }),
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/match?type=incoming`, { headers }),
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/match?type=outgoing`, { headers }),
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/match?type=connected`, { headers }),
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile`, { headers }),
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/plans/subscription`, { headers })
      ]);

      // Process matches data
      if (allMatchesRes.ok) {
        const allMatches = await allMatchesRes.json();
        const matchesData = allMatches.data || allMatches;
        setRecentMatches(matchesData.slice(0, 5)); // Get first 5 for recent matches
      }

      if (incomingRes.ok) {
        const incoming = await incomingRes.json();
        const incomingData = incoming.data || incoming;
        setStats(prev => ({ ...prev, incomingInvitations: incomingData.length }));
      }

      if (outgoingRes.ok) {
        const outgoing = await outgoingRes.json();
        const outgoingData = outgoing.data || outgoing;
        setStats(prev => ({ ...prev, outgoingInvitations: outgoingData.length }));
      }

      if (connectedRes.ok) {
        const connected = await connectedRes.json();
        const connectedData = connected.data || connected;
        setStats(prev => ({ ...prev, connectedMatches: connectedData.length }));
      }

      // Process profile data
      if (profileRes.ok) {
        const profileData = await profileRes.json();
        setProfile(profileData);

        // Calculate profile completion
        const completion = calculateProfileCompletion(profileData);
        setStats(prev => ({ ...prev, profileCompletion: completion }));
      }

      // Process subscription data
      if (subscriptionRes.ok) {
        const subscriptionData = await subscriptionRes.json();
        setSubscription(subscriptionData);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const calculateProfileCompletion = (profile: any): number => {
    const fields = [
      'name', 'email', 'image', 'dateOfBirth', 'gender',
      'religion', 'occupation', 'about', 'psychTestCompleted'
    ];

    let completed = 0;
    fields.forEach(field => {
      if (profile[field] && profile[field] !== '') {
        completed++;
      }
    });

    return Math.round((completed / fields.length) * 100);
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dasbor</h1>
            <p className="text-muted-foreground">Memuat dasbor Anda...</p>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-20"></div>
                <div className="h-4 w-4 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-24"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Dasbor</h1>
        </div>
        <Card className="p-6">
          <div className="text-center">
            <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchDashboardData}>Coba Lagi</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Selamat datang kembali{profile?.name ? `, ${profile.name}` : ''}!
          </h1>
          <p className="text-muted-foreground">Berikut yang terjadi dengan pasangan Anda hari ini.</p>
        </div>
        <Link href="/dashboard/matches">
          <Button className="bg-[#D0544D] hover:bg-[#D0544D]/90 text-white">
            <Heart className="w-4 h-4 mr-2" />
            Cari Pasangan
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Incoming Invitations</CardTitle>
            <Heart className="h-4 w-4 text-[#D0544D]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.incomingInvitations}</div>
            <p className="text-xs text-muted-foreground">
              {stats.incomingInvitations > 0 ? 'Waiting for your response' : 'No new invitations'}
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Koneksi</CardTitle>
            <Users className="h-4 w-4 text-[#D0544D]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.connectedMatches}</div>
            <p className="text-xs text-muted-foreground">
              {stats.connectedMatches > 0 ? 'Koneksi aktif' : 'Belum ada koneksi'}
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Profil</CardTitle>
            <User className="h-4 w-4 text-[#D0544D]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.profileCompletion}%</div>
            <p className="text-xs text-muted-foreground">Kelengkapan profil</p>
            <Progress value={stats.profileCompletion} className="mt-2 h-2" />
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Langganan</CardTitle>
            <CreditCard className="h-4 w-4 text-[#D0544D]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {subscription?.plan?.name || 'Gratis'}
            </div>
            <p className="text-xs text-muted-foreground">
              {subscription?.endDate
                ? `Aktif hingga ${formatDate(subscription.endDate)}`
                : 'Tidak ada langganan aktif'
              }
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Subscription Resources (if user has subscription) */}
      {subscription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Sumber Daya Langganan</span>
              <Badge variant="outline">{subscription.plan.name}</Badge>
            </CardTitle>
            <CardDescription>Lacak penggunaan bulanan dan sumber daya yang tersisa</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Konsultasi Psikolog</span>
                  <span className="text-sm text-muted-foreground">
                    {subscription.resources.psychologistConsultations - subscription.usage.psychologistConsultationsCount} / {subscription.resources.psychologistConsultations}
                  </span>
                </div>
                <Progress
                  value={(subscription.usage.psychologistConsultationsCount / subscription.resources.psychologistConsultations) * 100}
                  className="h-2"
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Sesi Chat</span>
                  <span className="text-sm text-muted-foreground">
                    {subscription.resources.chats - subscription.usage.chatsCount} / {subscription.resources.chats}
                  </span>
                </div>
                <Progress
                  value={(subscription.usage.chatsCount / subscription.resources.chats) * 100}
                  className="h-2"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4 md:grid-cols-2">
        {/* Recent Matches */}
        <Card className="col-span-1">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Pasangan Terbaru</CardTitle>
              <CardDescription>Calon pasangan terbaru Anda</CardDescription>
            </div>
            <Link href="/dashboard/matches">
              <Button variant="ghost" size="sm">
                Lihat Semua
                <ArrowRight className="w-4 h-4 ml-1" />
              </Button>
            </Link>
          </CardHeader>
          <CardContent>
            {recentMatches.length === 0 ? (
              <div className="rounded-md bg-[#F2E7DB]/50 p-8 text-center">
                <Heart className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm text-muted-foreground">Belum ada pasangan</p>
                <p className="text-xs text-muted-foreground">Lengkapi profil Anda untuk menemukan pasangan!</p>
              </div>
            ) : (
              <div className="space-y-3">
                {recentMatches.map((match) => (
                  <div key={match.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-[#F2E7DB]/30 transition-colors">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={match.user.image || undefined} alt={match.user.name} />
                      <AvatarFallback className="bg-[#D0544D]/20 text-[#D0544D]">
                        {getInitials(match.user.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{match.user.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {match.user.age} tahun • {match.user.occupation}
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge variant="secondary" className="text-xs">
                        {Math.round(match.matchScore)}%
                      </Badge>
                      {match.status && (
                        <p className="text-xs text-muted-foreground mt-1 capitalize">
                          {match.status}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Aksi Cepat</CardTitle>
            <CardDescription>Tugas umum dan pintasan</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Link href="/dashboard/matches?type=incoming">
                <Button variant="outline" className="w-full justify-start">
                  <Heart className="w-4 h-4 mr-2" />
                  Tinjau Undangan Masuk
                  {stats.incomingInvitations > 0 && (
                    <Badge className="ml-auto bg-[#D0544D]">
                      {stats.incomingInvitations}
                    </Badge>
                  )}
                </Button>
              </Link>

              <Link href="/dashboard/profile">
                <Button variant="outline" className="w-full justify-start">
                  <User className="w-4 h-4 mr-2" />
                  Lengkapi Profil
                  {stats.profileCompletion < 100 && (
                    <Badge variant="secondary" className="ml-auto">
                      {stats.profileCompletion}%
                    </Badge>
                  )}
                </Button>
              </Link>

              <Link href="/dashboard/matches?type=connected">
                <Button variant="outline" className="w-full justify-start">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Mulai Percakapan
                  {stats.connectedMatches > 0 && (
                    <Badge variant="secondary" className="ml-auto">
                      {stats.connectedMatches}
                    </Badge>
                  )}
                </Button>
              </Link>

              {!subscription && (
                <Link href="/dashboard/subscription">
                  <Button variant="outline" className="w-full justify-start">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Upgrade ke Premium
                    <Badge variant="secondary" className="ml-auto">
                      Baru
                    </Badge>
                  </Button>
                </Link>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Profile Completion Alert */}
      {stats.profileCompletion < 80 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="text-sm font-medium text-yellow-800">
                  Lengkapi profil Anda untuk mendapatkan pasangan yang lebih baik
                </h3>
                <p className="text-sm text-yellow-700 mt-1">
                  Profil Anda {stats.profileCompletion}% lengkap. Tambahkan lebih banyak informasi untuk meningkatkan kualitas pasangan.
                </p>
                <Link href="/dashboard/profile">
                  <Button size="sm" className="mt-3 bg-yellow-600 hover:bg-yellow-700">
                    Lengkapi Profil
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
