import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Match, MatchDocument, MatchStatus } from './schemas/match.schema';
import { Model, Types } from 'mongoose';
import { PersonalityTestDocument } from 'src/personality/schemas/personality-test.schema';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PersonalityService } from 'src/personality/services/personality.service';
import { UserService } from 'src/user/user.service';
import { GetMatchesRequestDto, InvitationType } from './dto/match-request.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EVENT_MATCH_ACCEPTED } from 'src/constants/event-emitter';

@Injectable()
export class MatchService {
  private readonly logger = new Logger(MatchService.name);
  
  constructor(
    @InjectModel(Match.name) private readonly matchModel: Model<Match>,
    private readonly personalityService: PersonalityService,
    private readonly userService: UserService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  // @Cron(CronExpression.EVERY_10_SECONDS)
  async scheduleMatches(): Promise<void> {
    // this.logger.log('Scheduling matches...');
    // await this.makeMatches();
  }

  /**
   * Make matches between users
   * 
   * @returns void
   */
  async makeMatches() {
    const personalityTests = await this.personalityService.getAllPersonalityTests();

    if (personalityTests.length < 2) {
      this.logger.log('No personality tests found');
      return;
    }

    for (let i = 0; i < personalityTests.length - 1; i++) {
      for (let j = i + 1; j < personalityTests.length; j++) {
        const user1Test = personalityTests[i];
        const user2Test = personalityTests[j];
        
        if (user1Test.userId._id.toString() === user2Test.userId._id.toString() || user1Test.userId.gender === user2Test.userId.gender) continue;

        const existingMatch = await this.matchModel.findOne({
          $or: [
            { user1Id: user1Test.userId, user2Id: user2Test.userId },
            { user1Id: user2Test.userId, user2Id: user1Test.userId }
          ]
        });

        if (!existingMatch) {
          const matchScore = this.calculateMatchScore(user1Test, user2Test);
          const newMatch = new this.matchModel({
            user1Id: user1Test.userId,
            user2Id: user2Test.userId,
            matchScore: matchScore.matchScore,
            matchDetails: matchScore.matchDetails
          });
          
          await newMatch.save();
          this.logger.log(`Created match between ${user1Test.userId.name} and ${user2Test.userId.name}`);
        }
      }
    }
  }

  /**
   * Create a match between two users
   * 
   * @param userId - User ID
   * @returns void
   */
  async createMatch(userId: string): Promise<void> {
    const userAtest = await this.personalityService.getPersonalityTestByUserId(userId);

    if (!userAtest) {
      throw new BadRequestException(`User with ID ${userId} not found`);
    }
    
    const users = await this.userService.findUsersWithCompletedPersonalityTest();
    
    for (const user of users) {
      if (user.id.toString() === userId) continue;
      if (user.gender === (userAtest.userId as any).gender) continue;
      
      const userBtest = await this.personalityService.getPersonalityTestByUserId(user.id.toString());
      
      if (userBtest) {
        const matchScore = this.calculateMatchScore(userAtest, userBtest);
        
        const userIdObj = Types.ObjectId.createFromHexString(userId);
        const otherUserIdObj = Types.ObjectId.createFromHexString(user.id);
        
        await this.matchModel.findOneAndUpdate({
          $or: [
            { user1Id: userIdObj, user2Id: otherUserIdObj },
            { user1Id: otherUserIdObj, user2Id: userIdObj }
          ]
        }, {
          user1Id: userIdObj,
          user2Id: otherUserIdObj,
          matchScore: matchScore.matchScore,
          matchDetails: matchScore.matchDetails
        }, { upsert: true, new: true });
      } else {
        this.logger.log(`Matches already exists`);
      }
    }
  }

  /**
   * Find match by user ID
   * 
   * @param userId - User ID
   * @returns Match document
   */
  async findMatchByUserId(userId: string): Promise<MatchDocument | null> {
    return this.matchModel.findOne({
      $or: [
        { user1Id: userId },
        { user2Id: userId }
      ]
    })
    .populate('user1Id')
    .populate('user2Id')
    .populate('invitedBy')
    .populate('acceptedBy')
    .populate('rejectedBy')
    .exec();
  }

  /**
   * Find matches by user IDs
   * 
   * @param user1Id - User ID 1
   * @param user2Id - User ID 2
   * @returns Match document
   */ 
  async findMatchesByUserIds(user1Id: string, user2Id: string): Promise<MatchDocument | null> {
    const user1IdObj = Types.ObjectId.createFromHexString(user1Id);
    const user2IdObj = Types.ObjectId.createFromHexString(user2Id);

    return await this.matchModel.findOne({
      $or: [
        { user1Id: user1IdObj, user2Id: user2IdObj },
        { user1Id: user2IdObj, user2Id: user1IdObj }
      ]
    })
    .populate('user1Id')
    .populate('user2Id')
    .populate('invitedBy')
    .populate('acceptedBy')
    .populate('rejectedBy')
    .exec();
  }

  /**
   * Find matches by user ID
   * 
   * @param userId - User ID
   * @param query - Query object
   * @returns Match documents
   */
  async findMatchesByUserId(
    userId: string,
    query: GetMatchesRequestDto
  ) {
    const { page = 1, limit = 10, type } = query;
    const skip = (page - 1) * limit;

    // Base query to find matches for the user
    const baseQuery = {
      $or: [
        { user1Id: Types.ObjectId.createFromHexString(userId) },
        { user2Id: Types.ObjectId.createFromHexString(userId) }
      ]
    };

    // Add filters based on type
    if (type === InvitationType.INCOMING) {
      baseQuery['invitedBy'] = { 
        $ne: null,
        $nin: [null, new Types.ObjectId(userId)]
      };
      baseQuery['status'] = MatchStatus.INVITED;
    } else if (type === InvitationType.OUTGOING) {
      baseQuery['invitedBy'] = new Types.ObjectId(userId);
      baseQuery['status'] = MatchStatus.INVITED;
    } else if (type === InvitationType.CONNECTED) {
      baseQuery['status'] = MatchStatus.ACCEPTED;
    } else {
      baseQuery['status'] = { $in: [MatchStatus.PENDING, MatchStatus.REJECTED] };
    }

    const [matches, total] = await Promise.all([
      this.matchModel
        .find(baseQuery)
        .populate('user1Id')
        .populate('user2Id')
        .populate('invitedBy')
        .populate('acceptedBy')
        .populate('rejectedBy')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.matchModel.countDocuments(baseQuery).exec()
    ]);

    return {
      data: matches,
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page
    };
  }
  /**
   * Calculates similarity score between two values (0-1 scale)
   * Formula: 1 - |A - B|
   * 
   * @param valueA - Value A
   * @param valueB - Value B
   * @returns Similarity score
   */
  calculateSimilarity(valueA: number, valueB: number): number {
    return 1 - Math.abs(valueA - valueB);
  }

  /**
   * Calculates complementary score between two values (0-1 scale)
   * Formula: 1 - |A - (1 - B)|
   * 
   * @param valueA - Value A
   * @param valueB - Value B
   * @returns Complementary score
   */
  calculateComplementary(valueA: number, valueB: number): number {
    return 1 - Math.abs(valueA - (1 - valueB));
  }

  /**
   * Calculate match score between two users based on their psychological test results
   * 
   * @param testA - Personality test of user A
   * @param testB - Personality test of user B
   * @returns An object with overall score and individual domain scores
   */
  calculateMatchScore(testA: PersonalityTestDocument, testB: PersonalityTestDocument) {
    // Calculate individual domain scores
    const extraversionScore = this.calculateComplementary(testA.bfi.extraversion, testB.bfi.extraversion);
    const agreeablenessScore = this.calculateSimilarity(testA.bfi.agreeableness, testB.bfi.agreeableness);
    const conscientiousnessScore = this.calculateSimilarity(testA.bfi.conscientiousness, testB.bfi.conscientiousness);
    const negativeEmotionalityScore = this.calculateComplementary(testA.bfi.negativeEmotionality, testB.bfi.negativeEmotionality);
    const openMindednessScore = this.calculateSimilarity(testA.bfi.openMindedness, testB.bfi.openMindedness);
    const selfDisclosureScore = this.calculateSimilarity(testA.selfDisclosure, testB.selfDisclosure);

    // Calculate overall score (average of all domains)
    const domainScores = [
      extraversionScore,
      agreeablenessScore,
      conscientiousnessScore,
      negativeEmotionalityScore,
      openMindednessScore,
      selfDisclosureScore
    ];

    const overallScore = domainScores.reduce((sum, score) => sum + score, 0) / domainScores.length;

    return {
      matchScore: overallScore,
      matchDetails: {
        extraversion: extraversionScore,
        agreeableness: agreeablenessScore,
        conscientiousness: conscientiousnessScore,
        negativeEmotionality: negativeEmotionalityScore,
        openMindedness: openMindednessScore,
        selfDisclosure: selfDisclosureScore
      }
    };
  }

  /**
   * Get match interpretation based on score
   * 
   * @param score - Match score
   * @returns Match interpretation
   */
  getMatchInterpretation(score: number): string {
    if (score >= 0.8) {
      return '🌟 Saling melengkapi kuat';
    } else if (score >= 0.6) {
      return '👍 Potensial bagus';
    } else if (score >= 0.4) {
      return '🤝 Butuh usaha & eksplorasi';
    } else {
      return '🔍 Kecocokan rendah';
    }
  }

  /**
   * Find a match by ID
   * 
   * @param id - Match ID
   * @returns Match document
   */
  async find(id: string): Promise<Match | null> {
    return this.matchModel
    .findById(id)
    .populate('user1Id')
    .populate('user2Id')
    .populate('invitedBy')
    .populate('acceptedBy')
    .populate('rejectedBy')
    .exec();
  }

  /**
   * Invite a user to a match
   * 
   * @param matchId - Match ID
   * @param userId - User ID
   * @returns void
   */
  async invite(matchId: string, userId: string) {
    try {
      const match = await this.find(matchId);

      // Check if match exists
      if (!match) {
        throw new NotFoundException(`Match with ID ${matchId} not found`);
      }

      // Check if user is member of match
      if (match.user1Id._id.toString() !== userId && match.user2Id._id.toString() !== userId) {
        throw new BadRequestException(`You are not member to this match`);
      }

      // Check if match has been invited
      if (match.status === MatchStatus.INVITED && match.invitedBy)  {
        throw new BadRequestException(`This match has been invited by ${(match.invitedBy as any).name}`);
      }

      // Check if match has been rejected
      if (match.status === MatchStatus.REJECTED && match.rejectedBy && match.rejectedBy?._id.toString() !== userId) {
        throw new BadRequestException(`This match has been rejected by ${(match.rejectedBy as any).name}`);
      }

      await this.matchModel.findByIdAndUpdate(
        matchId, {
          $set: {
            invitedBy: Types.ObjectId.createFromHexString(userId),
            invitedAt: new Date(),
            status: MatchStatus.INVITED,
            rejectedBy: null,
            rejectedAt: null
          }
        }
      );
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Accept a match invitation
   * 
   * @param matchId - Match ID
   * @param userId - User ID
   * @returns void
   */
  async accept(matchId: string, userId: string) {
    try {
      const match = await this.find(matchId);

      // Check if match exists
      if (!match) {
        throw new NotFoundException(`Match with ID ${matchId} not found`);
      }

      // Check if user is member of match
      if (match.user1Id._id.toString() !== userId && match.user2Id._id.toString() !== userId) {
        throw new BadRequestException(`You are not member to this match`);
      }

      // Check if match has been invited
      if (match.status !== MatchStatus.INVITED || !match.invitedBy) {
        throw new BadRequestException(`This match has not been invited`);
      }

      // Check if user is the one who invited the match
      if (match.invitedBy._id.toString() === userId) {
        throw new BadRequestException(`You are the one who invited this match`);
      }

      await this.matchModel.findByIdAndUpdate(
        matchId, {
          $set: {
            acceptedBy: Types.ObjectId.createFromHexString(userId),
            acceptedAt: new Date(),
            status: MatchStatus.ACCEPTED,
            rejectedBy: null,
            rejectedAt: null
          }
        }
      );

      this.eventEmitter.emit(EVENT_MATCH_ACCEPTED, {
        user1Id: match.user1Id._id.toString(),
        user2Id: match.user2Id._id.toString(),
        matchId: matchId
      });
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Reject a match invitation
   * 
   * @param matchId - Match ID
   * @param userId - User ID
   * @returns void
   */
  async reject(matchId: string, userId: string) {
    try {
      const match = await this.find(matchId);

      // Check if match exists
      if (!match) {
        throw new NotFoundException(`Match with ID ${matchId} not found`);
      }

      // Check if user is member of match
      if (match.user1Id._id.toString() !== userId && match.user2Id._id.toString() !== userId) {
        throw new BadRequestException(`You are not member to this match`);
      }

      // Check if match has been invited
      if (match.status !== MatchStatus.INVITED || !match.invitedBy) {
        throw new BadRequestException(`This match has not been invited`);
      }

      // Check if user is the one who invited the match
      if (match.invitedBy._id.toString() === userId) {
        throw new BadRequestException(`You are the one who invited this match`);
      }

      // Check if match has been accepted or rejected
      if ([MatchStatus.ACCEPTED, MatchStatus.REJECTED].includes(match.status as MatchStatus)) {
        throw new BadRequestException(`This match has been ${match.status}`);
      }

      await this.matchModel.findByIdAndUpdate(
        matchId, {
          $set: {
            rejectedBy: Types.ObjectId.createFromHexString(userId),
            rejectedAt: new Date(),
            status: MatchStatus.REJECTED,
            invitedBy: null,
            invitedAt: null,
            acceptedBy: null,
            acceptedAt: null
          }
        }
      );
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
