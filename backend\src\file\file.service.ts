import { Injectable, Logger, BadRequestException, OnModuleInit } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import * as mime from 'mime-types';
import * as AWS from 'aws-sdk';
import * as sharp from 'sharp';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { Types } from 'mongoose';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EVENT_IMAGE_DELETED, EVENT_PHOTO_DELETED } from 'src/constants/event-emitter';

@Injectable()
export class FileService implements OnModuleInit {
  private readonly logger = new Logger(FileService.name);
  private readonly storagePath: string;
  private readonly s3Client: AWS.S3;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2
  ) {
    this.storagePath = path.join(process.cwd(), 'storage');

    // Ensure files directory exists
    if (!fs.existsSync(this.storagePath)) {
      fs.mkdirSync(this.storagePath, { recursive: true });
    }

    this.s3Client = new AWS.S3({
      accessKeyId: this.configService.get<string>('app.s3.accessKeyId'),
      secretAccessKey: this.configService.get<string>('app.s3.secretAccessKey'),
      region: this.configService.get<string>('app.s3.region'),
      endpoint: this.configService.get<string>('app.s3.endpoint'),
    });
  }

  onModuleInit() {
    this.eventEmitter.on(EVENT_PHOTO_DELETED, (data: { userId: string, key: string }) => {
      this.logger.log(`Photo deleted for user ${data.userId}, key: ${data.key}`);
      this.removeImageFromS3(data.key);
    });

    this.eventEmitter.on(EVENT_IMAGE_DELETED, (data: { userId: string, key: string }) => {
      this.logger.log(`Image deleted for user ${data.userId}, key: ${data.key}`);
      this.removeImageFromS3(data.key);
    });
  }

  /**
   * Upload file from URL
   * @param url URL of the file to upload
   * @returns Uploaded file object
   */
  async uploadFromUrl(userId: string, url: string): Promise<void> {
    try {
      this.logger.log(`Uploading file from URL: ${url}`);
      
      // Create a temporary directory for the user 'system'
      const tempDir = path.join(this.storagePath, userId);
      
      // Ensure temp directory exists
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      // Extract filename from URL
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop() || `file_${Date.now()}`;
      
      // Create a temporary file path
      const tempFilePath = path.join(tempDir, filename);
      
      // Download the file
      const response = await axios({
        method: 'GET',
        url: url,
        responseType: 'stream',
      });
      
      // Get content type
      const contentType = response.headers['content-type'] || 'application/octet-stream';
      
      // get mime type from content type
      const mimeType = contentType.split(';')[0];
      
      // Create write stream
      const writer = fs.createWriteStream(tempFilePath);
      
      // Pipe the response to the file
      response.data.pipe(writer);
      
      // Wait for the download to complete
      await new Promise<void>((resolve, reject) => {
        writer.on('finish', () => resolve());
        writer.on('error', (err) => reject(err));
      });
      
      this.logger.log(`File downloaded to: ${tempFilePath}`);
      
      // Get file stats
      const stats = fs.statSync(tempFilePath);
      
      // Upload file to S3

      // Clean up temp file
      fs.unlinkSync(tempFilePath);
    } catch (error) {
      this.logger.error(`Error uploading file from URL: ${error.message}`, error.stack);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Get file info from path
   * 
   * @param filePath File path
   * @returns File info
   */
  async getFileInfoFromPath(filePath: string) {
    const stats = await fs.promises.stat(filePath);
    const filename = path.basename(filePath);
    const ext = path.extname(filePath);
    const mimetype = mime.lookup(ext) || 'application/octet-stream';
  
    return {
      path: filePath,
      filename,
      originalname: filename,
      size: stats.size,
      mimetype,
    };
  }

  /**
   * Upload image to S3
   * 
   * @param directory Directory to upload to
   * @param file File to upload
   * @returns Upload result
   */
  async uploadImageToS3(directory: string, file: Express.Multer.File): Promise<{ url: string, key: string }> {
    try {
      const fileName = new Types.ObjectId().toString() + '-' + Math.round(Math.random() * 1e9);
      const key = `images/${directory}/${fileName}.png`;
      const fileBuffer = fs.readFileSync(file.path);
      const uploadResult = await this.s3Client.upload({
        Bucket: this.configService.get<string>('app.s3.bucket') || 'heylo-assets',
        Key: key,
        Body: fileBuffer,
        ContentType: file.mimetype,
        ACL: 'public-read'
      }).promise();
      
      fs.unlinkSync(file.path);
      
      return {
        url: uploadResult.Location,
        key: uploadResult.Key
      };
    } catch (error) {
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw error;
    }
  }

  /**
   * Resize image
   * 
   * @param buffer Buffer image
   * @param width Width of the image
   * @param height Height of the image
   * @returns Buffer resized image
   */
  async resizeImage(buffer: Buffer, width: number, height: number): Promise<Buffer> {
    try {
      return await sharp(buffer)
        .resize(width, height, {
          fit: 'cover',
          position: 'center'
        })
        .png()
        .toBuffer();
    } catch (error) {
      throw new BadRequestException(`Error resizing image: ${error.message}`);
    }
  }

  /**
   * Remove an image from S3
   * 
   * @param key Key of the image
   * @returns 
   */
  async removeImageFromS3(key: string): Promise<void> {
    try {
      await this.s3Client.deleteObject({
        Bucket: this.configService.get<string>('app.s3.bucket') || 'heylo-assets',
        Key: key
      }).promise();

      this.logger.log(`Image removed from S3: ${key}`);
    } catch (error) {
      throw new BadRequestException(`Error removing image from S3: ${error.message}`);
    }
  }
}
