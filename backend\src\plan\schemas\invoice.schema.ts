import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export enum InvoiceStatus {
  PENDING = 'pending',
  PAID = 'paid',
  CANCELLED = 'cancelled'
}

export type InvoiceItem = {
  id: string;
  description: string;
  quantity: number;
  price: number;
}

export type InvoiceTotal = {
  type: string;
  description: string;
  amount: number;
}

export type InvoiceDocument = Invoice & Document;

@Schema({ timestamps: true })
export class Invoice {
  @Prop({ default: null })
  invoiceCode: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ default: null })
  planId: string;

  @Prop({ default: null })
  purchaseId: string;

  @Prop({ type: [Object], required: true })
  items: InvoiceItem[];

  @Prop({ type: [Object], default: [] })
  totals: InvoiceTotal[];

  @Prop({ default: null })
  paidDate: Date;

  @Prop({ type: Object, default: null })
  metadata: Record<string, any>;

  @Prop({ 
    type: String, 
    enum: InvoiceStatus, 
    default: InvoiceStatus.PENDING 
  })
  status: InvoiceStatus;

  @Prop({ default: null })
  invoiceLink: string;

  @Prop({ default: null })
  paymentLink: string;

  @Prop({ default: null })
  paymentToken: string;

  createdAt: Date;
  updatedAt: Date;
}

export const InvoiceSchema = SchemaFactory.createForClass(Invoice);
