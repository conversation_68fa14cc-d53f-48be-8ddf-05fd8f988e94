import { PlanInterval } from '../../plan/schemas/plan.schema';

export const planSeeds = [
  {
    name: 'Silver',
    code: 'silver',
    description: 'Basic plan for individuals and small businesses',
    price: 199000,
    currency: 'IDR',
    interval: PlanInterval.MONTHLY,
    isPopular: false,
    isDefault: false,
    isHidden: false,
    sortOrder: 1,
    psychologistConsultations: 3,
    chats: 100,
    features: {}
  },
  {
    name: 'Gold',
    code: 'gold',
    description: 'Advanced plan for growing businesses',
    price: 499000,
    currency: 'IDR',
    interval: PlanInterval.MONTHLY,
    isPopular: true,
    isDefault: true,
    isHidden: false,
    sortOrder: 2,
    psychologistConsultations: 10,
    chats: 500,
    features: {}
  },
  {
    name: 'Platinum',
    code: 'platinum',
    description: 'Enterprise-grade solution for large organizations',
    price: 1000000,
    currency: 'IDR',
    interval: PlanInterval.MONTHLY,
    isPopular: false,
    isDefault: false,
    isHidden: false,
    sortOrder: 3,
    psychologistConsultations: 50,
    chats: 5000,
    features: {}
  }
];
