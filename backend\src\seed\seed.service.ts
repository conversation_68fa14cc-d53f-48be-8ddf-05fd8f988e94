import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { PlanSeeder } from './plan.seed';
import { QuestionSeeder } from './question.seed';
import { UserSeeder } from './user.seed';
import { PersonalityTestSeeder } from './personality-test.seed';

@Injectable()
export class SeedService implements OnModuleInit {
  private readonly logger = new Logger(SeedService.name);

  constructor(
    private readonly planSeeder: PlanSeeder,
    private readonly questionSeeder: QuestionSeeder,
    private readonly userSeeder: UserSeeder,
    private readonly personalityTestSeeder: PersonalityTestSeeder,
  ) {}

  async onModuleInit() {
    await this.seed();
  }

  async seed() {
    this.logger.log('Starting database seeding...');
    
    try {
      // Seed users first since other seeders might depend on users
      this.logger.log('Seeding users...');
      await this.userSeeder.seed();
      
      // Seed questions
      this.logger.log('Seeding questions...');
      await this.questionSeeder.seed();
      
      // Seed personality tests for users
      this.logger.log('Seeding personality tests...');
      await this.personalityTestSeeder.seed();
      
      // Seed plans
      await this.planSeeder.seed();
      
      this.logger.log('Database seeding completed successfully!');
    } catch (error) {
      this.logger.error('Error during database seeding', error.stack);
    }
  }
}
