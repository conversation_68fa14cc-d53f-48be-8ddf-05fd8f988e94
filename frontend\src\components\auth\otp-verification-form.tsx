"use client";

"use client";

import { useState, useEffect } from "react";
import Cookies from 'js-cookie';
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const otpSchema = z.object({
  sessionId: z.string(),
  code: z.string().min(4, {
    message: "Kode OTP harus minimal 4 karakter.",
  }),
});

type OtpFormValues = z.infer<typeof otpSchema>;

type AuthSession = {
  sessionId: string;
  contactMethod: "email";
  contactValue: string;
  isRegistration?: boolean;
}

export function OtpVerificationForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [authSession, setAuthSession] = useState<AuthSession | null>(null);
  const [contactInfo, setContactInfo] = useState<string>("");
  const [contactType, setContactType] = useState<string>("");

  useEffect(() => {
    // Get session data from sessionStorage
    const sessionData = sessionStorage.getItem('authSession');
    if (sessionData) {
      try {
        const parsedSession = JSON.parse(sessionData) as AuthSession;
        setAuthSession(parsedSession);
        setContactInfo(parsedSession.contactValue);
        setContactType(parsedSession.contactMethod);
      } catch (error) {
        console.error("Error parsing session data:", error);
        router.push('/login'); // Redirect back to login if session data is invalid
      }
    } else {
      router.push('/login'); // Redirect back to login if no session data
    }
  }, [router]);

  const form = useForm<OtpFormValues>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      sessionId: "",
      code: "",
    },
  });

  // Update form values when authSession changes
  useEffect(() => {
    if (authSession) {
      form.setValue("sessionId", authSession.sessionId);
    }
  }, [authSession, form]);

  async function onSubmit(data: OtpFormValues) {
    if (!authSession) return;
    
    setIsLoading(true);

    try {
      // Cek apakah sessionId dibuat secara manual (dimulai dengan login_ atau register_)
      const isManualSession = authSession.sessionId.startsWith('login_') || authSession.sessionId.startsWith('register_');
      
      // Jika sessionId dibuat manual, kita perlu mengirim email juga ke API
      const requestBody = isManualSession ? {
        code: data.code,
        email: authSession.contactValue,
        provider: "email"
      } : {
        sessionId: authSession.sessionId,
        code: data.code
      };
      
      console.log("OTP verification request:", requestBody);
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/verify-otp`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      const result = await response.json();
      console.log("OTP verification response:", result);

      if (!response.ok) {
        throw new Error(result.message || "Failed to verify OTP");
      }

      // Store the token in localStorage and cookies
      localStorage.setItem("pairsona_token", result.accessToken);
      localStorage.setItem("pairsona_token_expiry", result.expiredAt);
      Cookies.set('pairsona_token', result.accessToken, { 
        expires: new Date(result.expiredAt),
        path: '/',
        // secure: process.env.NODE_ENV === 'production', // Add this for production
        // sameSite: 'strict' 
      });
      
      // Clear session storage
      sessionStorage.removeItem('authSession');

      // Redirect to dashboard
      router.push("/auth/setup");
    } catch (error) {
      console.error("OTP verification error:", error);
      // Handle error state
    } finally {
      setIsLoading(false);
    }
  }

  if (!authSession) {
    return <div>Loading...</div>;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="mb-4">
          <p className="text-sm text-muted-foreground">
            Verification code sent to email: {contactInfo}
          </p>
        </div>

        <FormField
          control={form.control}
          name="code"
          render={({ field }) => (
            <FormItem>
              <FormLabel>OTP Code</FormLabel>
              <FormControl>
                <Input placeholder="Enter OTP code" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full bg-accent-red hover:bg-accent-red/90 text-white" disabled={isLoading}>
          {isLoading ? "Verifying..." : "Verify OTP"}
        </Button>
      </form>
    </Form>
  );
}
