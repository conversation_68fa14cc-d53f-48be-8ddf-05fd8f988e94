{"version": 3, "file": "engine.io.esm.min.js", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../build/esm/globalThis.browser.js", "../build/esm/util.js", "../build/esm/transport.js", "../build/esm/contrib/parseqs.js", "../build/esm/contrib/yeast.js", "../build/esm/contrib/has-cors.js", "../build/esm/transports/xmlhttprequest.browser.js", "../build/esm/transports/polling.js", "../build/esm/transports/websocket-constructor.browser.js", "../build/esm/transports/websocket.js", "../build/esm/transports/index.js", "../build/esm/transports/webtransport.js", "../build/esm/contrib/parseuri.js", "../build/esm/socket.js", "../build/esm/index.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data\n            .arrayBuffer()\n            .then(toArray)\n            .then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, encoded => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, encodedPacket => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        }\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else if (state === 2 /* READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        }\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\nexport function createCookieJar() { }\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { createCookieJar, XHR as XMLHttpRequest, } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n        if (this.opts.withCredentials) {\n            this.cookieJar = createCookieJar();\n        }\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, cookieJar: this.cookieJar }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        var _a;\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, true);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(xhr);\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: Polling,\n};\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"./websocket-constructor.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        // @ts-ignore\n        if (typeof WebTransport !== \"function\") {\n            return;\n        }\n        // @ts-ignore\n        this.transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        this.transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this.transport.ready.then(() => {\n            this.transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this.writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this.writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this.writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this.transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 2000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { defaultBinaryType } from \"./transports/websocket-constructor.js\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\n            \"polling\",\n            \"websocket\",\n            \"webtransport\",\n        ];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this.upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            this.resetPingTimeout();\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "chars", "lookup", "i", "length", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "createPacketEncoderStream", "TransformStream", "transform", "packet", "controller", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "encodePacketToBinary", "payloadLength", "header", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "TEXT_DECODER", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "attr", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "TransportError", "Error", "constructor", "reason", "description", "context", "super", "Transport", "writable", "query", "socket", "onError", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "onPacket", "details", "pause", "onPause", "createUri", "schema", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "str", "encodeURIComponent", "alphabet", "map", "prev", "seed", "num", "Math", "floor", "yeast", "now", "Date", "value", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Request", "uri", "method", "undefined", "_a", "xd", "xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "cookieJar", "addCookies", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "status", "onLoad", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "WebSocket", "MozWebSocket", "isReactNative", "navigator", "product", "toLowerCase", "transports", "websocket", "forceBase64", "name", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "timestampRequests", "timestampParam", "b64", "webtransport", "WebTransport", "transport", "transportOptions", "closed", "catch", "ready", "createBidirectionalStream", "stream", "decoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "pow", "createPacketDecoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "writer", "getWriter", "read", "done", "sid", "polling", "location", "isSSL", "protocol", "createCookieJar", "poll", "total", "doPoll", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "count", "encodePayload", "doWrite", "request", "assign", "req", "xhrStatus", "pollXhr", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "writeBuffer", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "offlineEventListener", "createTransport", "EIO", "priorWebsocketSuccess", "setTransport", "onDrain", "probe", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "resetPingTimeout", "onHandshake", "JSON", "sendPacket", "code", "filterUpgrades", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades"], "mappings": ";;;;;AAAA,MAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,MAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQC,IAC9BH,EAAqBH,EAAaM,IAAQA,CAAG,IAEjD,MAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAE/BC,EAASC,GAC0B,mBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,GAAOA,EAAIC,kBAAkBH,YAEjCI,EAAe,EAAGZ,OAAMC,QAAQY,EAAgBC,IAC9CZ,GAAkBD,aAAgBE,KAC9BU,EACOC,EAASb,GAGTc,EAAmBd,EAAMa,GAG/BP,IACJN,aAAgBO,aAAeC,EAAOR,IACnCY,EACOC,EAASb,GAGTc,EAAmB,IAAIZ,KAAK,CAACF,IAAQa,GAI7CA,EAAStB,EAAaQ,IAASC,GAAQ,KAE5Cc,EAAqB,CAACd,EAAMa,KAC9B,MAAME,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,MAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CP,EAAS,KAAOK,GAAW,MAExBH,EAAWM,cAAcrB,EAAK,EAEzC,SAASsB,EAAQtB,GACb,OAAIA,aAAgBuB,WACTvB,EAEFA,aAAgBO,YACd,IAAIgB,WAAWvB,GAGf,IAAIuB,WAAWvB,EAAKU,OAAQV,EAAKwB,WAAYxB,EAAKyB,WAEjE,CACA,IAAIC,EClDJ,MAAMC,EAAQ,mEAERC,EAA+B,oBAAfL,WAA6B,GAAK,IAAIA,WAAW,KACvE,IAAK,IAAIM,EAAI,EAAGA,EAAIF,EAAMG,OAAQD,IAC9BD,EAAOD,EAAMI,WAAWF,IAAMA,EAkB3B,MCrBDvB,EAA+C,mBAAhBC,YACxByB,EAAe,CAACC,EAAeC,KACxC,GAA6B,iBAAlBD,EACP,MAAO,CACHlC,KAAM,UACNC,KAAMmC,EAAUF,EAAeC,IAGvC,MAAMnC,EAAOkC,EAAcG,OAAO,GAClC,GAAa,MAATrC,EACA,MAAO,CACHA,KAAM,UACNC,KAAMqC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAI7D,OADmBxC,EAAqBK,GAIjCkC,EAAcH,OAAS,EACxB,CACE/B,KAAML,EAAqBK,GAC3BC,KAAMiC,EAAcK,UAAU,IAEhC,CACEvC,KAAML,EAAqBK,IARxBD,CASN,EAEHuC,EAAqB,CAACrC,EAAMkC,KAC9B,GAAI5B,EAAuB,CACvB,MAAMiC,EDTQ,CAACC,IACnB,IAA8DX,EAAUY,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOV,OAAegB,EAAMN,EAAOV,OAAWiB,EAAI,EACnC,MAA9BP,EAAOA,EAAOV,OAAS,KACvBe,IACkC,MAA9BL,EAAOA,EAAOV,OAAS,IACvBe,KAGR,MAAMG,EAAc,IAAIzC,YAAYsC,GAAeI,EAAQ,IAAI1B,WAAWyB,GAC1E,IAAKnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWb,EAAOY,EAAOT,WAAWF,IACpCa,EAAWd,EAAOY,EAAOT,WAAWF,EAAI,IACxCc,EAAWf,EAAOY,EAAOT,WAAWF,EAAI,IACxCe,EAAWhB,EAAOY,EAAOT,WAAWF,EAAI,IACxCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CAAW,ECTEE,CAAOlD,GACvB,OAAOmC,EAAUI,EAASL,GAG1B,MAAO,CAAEM,QAAQ,EAAMxC,SAGzBmC,EAAY,CAACnC,EAAMkC,IAEZ,SADDA,EAEIlC,aAAgBE,KAETF,EAIA,IAAIE,KAAK,CAACF,IAIjBA,aAAgBO,YAETP,EAIAA,EAAKU,OCvDtByC,EAAYC,OAAOC,aAAa,IA4B/B,SAASC,IACZ,OAAO,IAAIC,gBAAgB,CACvBC,UAAUC,EAAQC,IHmBnB,SAA8BD,EAAQ5C,GACrCZ,GAAkBwD,EAAOzD,gBAAgBE,KAClCuD,EAAOzD,KACT2D,cACAC,KAAKtC,GACLsC,KAAK/C,GAELP,IACJmD,EAAOzD,gBAAgBO,aAAeC,EAAOiD,EAAOzD,OAC9Ca,EAASS,EAAQmC,EAAOzD,OAEnCW,EAAa8C,GAAQ,GAAOI,IACnBnC,IACDA,EAAe,IAAIoC,aAEvBjD,EAASa,EAAaqC,OAAOF,GAAS,GAE9C,CGnCYG,CAAqBP,GAAQxB,IACzB,MAAMgC,EAAgBhC,EAAcH,OACpC,IAAIoC,EAEJ,GAAID,EAAgB,IAChBC,EAAS,IAAI3C,WAAW,GACxB,IAAI4C,SAASD,EAAOxD,QAAQ0D,SAAS,EAAGH,QAEvC,GAAIA,EAAgB,MAAO,CAC5BC,EAAS,IAAI3C,WAAW,GACxB,MAAM8C,EAAO,IAAIF,SAASD,EAAOxD,QACjC2D,EAAKD,SAAS,EAAG,KACjBC,EAAKC,UAAU,EAAGL,OAEjB,CACDC,EAAS,IAAI3C,WAAW,GACxB,MAAM8C,EAAO,IAAIF,SAASD,EAAOxD,QACjC2D,EAAKD,SAAS,EAAG,KACjBC,EAAKE,aAAa,EAAGC,OAAOP,IAG5BR,EAAOzD,MAA+B,iBAAhByD,EAAOzD,OAC7BkE,EAAO,IAAM,KAEjBR,EAAWe,QAAQP,GACnBR,EAAWe,QAAQxC,EAAc,MAIjD,CACA,IAAIyC,EACJ,SAASC,EAAYC,GACjB,OAAOA,EAAOC,QAAO,CAACC,EAAKC,IAAUD,EAAMC,EAAMjD,QAAQ,EAC7D,CACA,SAASkD,EAAaJ,EAAQK,GAC1B,GAAIL,EAAO,GAAG9C,SAAWmD,EACrB,OAAOL,EAAOM,QAElB,MAAMxE,EAAS,IAAIa,WAAW0D,GAC9B,IAAIE,EAAI,EACR,IAAK,IAAItD,EAAI,EAAGA,EAAIoD,EAAMpD,IACtBnB,EAAOmB,GAAK+C,EAAO,GAAGO,KAClBA,IAAMP,EAAO,GAAG9C,SAChB8C,EAAOM,QACPC,EAAI,GAMZ,OAHIP,EAAO9C,QAAUqD,EAAIP,EAAO,GAAG9C,SAC/B8C,EAAO,GAAKA,EAAO,GAAGQ,MAAMD,IAEzBzE,CACX,CC/EO,SAAS2E,EAAQ5E,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIZ,KAAOwF,EAAQlF,UACtBM,EAAIZ,GAAOwF,EAAQlF,UAAUN,GAE/B,OAAOY,CACT,CAhBkB6E,CAAM7E,EACxB,CA0BA4E,EAAQlF,UAAUoF,GAClBF,EAAQlF,UAAUqF,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,IACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACT,EAYAN,EAAQlF,UAAU2F,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,WAKjB,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACT,EAYAN,EAAQlF,UAAU4F,IAClBV,EAAQlF,UAAU+F,eAClBb,EAAQlF,UAAUgG,mBAClBd,EAAQlF,UAAUiG,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,GAGjC,GAAKK,UAAUnE,OAEjB,OADA6D,KAAKC,WAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,WAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAUnE,OAEjB,cADO6D,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI9D,EAAI,EAAGA,EAAIyE,EAAUxE,OAAQD,IAEpC,IADAwE,EAAKC,EAAUzE,MACJ6D,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO1E,EAAG,GACpB,MAUJ,OAJyB,IAArByE,EAAUxE,eACL6D,KAAKC,WAAW,IAAMH,GAGxBE,IACT,EAUAN,EAAQlF,UAAUqG,KAAO,SAASf,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,GAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAUnE,OAAS,GACpCwE,EAAYX,KAAKC,WAAW,IAAMH,GAE7B5D,EAAI,EAAGA,EAAIoE,UAAUnE,OAAQD,IACpC4E,EAAK5E,EAAI,GAAKoE,UAAUpE,GAG1B,GAAIyE,EAEG,CAAIzE,EAAI,EAAb,IAAK,IAAWiB,GADhBwD,EAAYA,EAAUlB,MAAM,IACItD,OAAQD,EAAIiB,IAAOjB,EACjDyE,EAAUzE,GAAGmE,MAAML,KAAMc,EADK3E,CAKlC,OAAO6D,IACT,EAGAN,EAAQlF,UAAUwG,aAAetB,EAAQlF,UAAUqG,KAUnDnB,EAAQlF,UAAUyG,UAAY,SAASnB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,GAC9BD,KAAKC,WAAW,IAAMH,IAAU,EACzC,EAUAJ,EAAQlF,UAAU0G,aAAe,SAASpB,GACxC,QAAUE,KAAKiB,UAAUnB,GAAO3D,MAClC,ECxKO,MAAMgF,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKzG,KAAQ0G,GACzB,OAAOA,EAAKtC,QAAO,CAACC,EAAKsC,KACjB3G,EAAI4G,eAAeD,KACnBtC,EAAIsC,GAAK3G,EAAI2G,IAEVtC,IACR,GACP,CAEA,MAAMwC,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsBlH,EAAKmH,GACnCA,EAAKC,iBACLpH,EAAIqH,aAAeR,EAAmBS,KAAKR,GAC3C9G,EAAIuH,eAAiBP,EAAqBM,KAAKR,KAG/C9G,EAAIqH,aAAeP,EAAWC,WAAWO,KAAKR,GAC9C9G,EAAIuH,eAAiBT,EAAWG,aAAaK,KAAKR,GAE1D,CCjBO,MAAMU,UAAuBC,MAChCC,YAAYC,EAAQC,EAAaC,GAC7BC,MAAMH,GACNzC,KAAK0C,YAAcA,EACnB1C,KAAK2C,QAAUA,EACf3C,KAAK5F,KAAO,kBAGb,MAAMyI,UAAkBnD,EAO3B8C,YAAYP,GACRW,QACA5C,KAAK8C,UAAW,EAChBd,EAAsBhC,KAAMiC,GAC5BjC,KAAKiC,KAAOA,EACZjC,KAAK+C,MAAQd,EAAKc,MAClB/C,KAAKgD,OAASf,EAAKe,OAWvBC,QAAQR,EAAQC,EAAaC,GAEzB,OADAC,MAAM5B,aAAa,QAAS,IAAIsB,EAAeG,EAAQC,EAAaC,IAC7D3C,KAKXkD,OAGI,OAFAlD,KAAKmD,WAAa,UAClBnD,KAAKoD,SACEpD,KAKXqD,QAKI,MAJwB,YAApBrD,KAAKmD,YAAgD,SAApBnD,KAAKmD,aACtCnD,KAAKsD,UACLtD,KAAKuD,WAEFvD,KAOXwD,KAAKC,GACuB,SAApBzD,KAAKmD,YACLnD,KAAK0D,MAAMD,GAWnBE,SACI3D,KAAKmD,WAAa,OAClBnD,KAAK8C,UAAW,EAChBF,MAAM5B,aAAa,QAQvB4C,OAAOvJ,GACH,MAAMyD,EAASzB,EAAahC,EAAM2F,KAAKgD,OAAOzG,YAC9CyD,KAAK6D,SAAS/F,GAOlB+F,SAAS/F,GACL8E,MAAM5B,aAAa,SAAUlD,GAOjCyF,QAAQO,GACJ9D,KAAKmD,WAAa,SAClBP,MAAM5B,aAAa,QAAS8C,GAOhCC,MAAMC,IACNC,UAAUC,EAAQnB,EAAQ,IACtB,OAAQmB,EACJ,MACAlE,KAAKmE,YACLnE,KAAKoE,QACLpE,KAAKiC,KAAKoC,KACVrE,KAAKsE,OAAOvB,GAEpBoB,YACI,MAAMI,EAAWvE,KAAKiC,KAAKsC,SAC3B,OAAkC,IAA3BA,EAASC,QAAQ,KAAcD,EAAW,IAAMA,EAAW,IAEtEH,QACI,OAAIpE,KAAKiC,KAAKwC,OACRzE,KAAKiC,KAAKyC,QAAUC,OAA0B,MAAnB3E,KAAKiC,KAAKwC,QACjCzE,KAAKiC,KAAKyC,QAAqC,KAA3BC,OAAO3E,KAAKiC,KAAKwC,OACpC,IAAMzE,KAAKiC,KAAKwC,KAGhB,GAGfH,OAAOvB,GACH,MAAM6B,ECjIP,SAAgB9J,GACnB,IAAI+J,EAAM,GACV,IAAK,IAAI3I,KAAKpB,EACNA,EAAI4G,eAAexF,KACf2I,EAAI1I,SACJ0I,GAAO,KACXA,GAAOC,mBAAmB5I,GAAK,IAAM4I,mBAAmBhK,EAAIoB,KAGpE,OAAO2I,CACX,CDuH6BzG,CAAO2E,GAC5B,OAAO6B,EAAazI,OAAS,IAAMyI,EAAe,IExI1D,MAAMG,EAAW,mEAAmEtJ,MAAM,IAAkBuJ,EAAM,GAClH,IAAqBC,EAAjBC,EAAO,EAAGhJ,EAAI,EAQX,SAASkC,EAAO+G,GACnB,IAAIjH,EAAU,GACd,GACIA,EAAU6G,EAASI,EAZ6E,IAY7DjH,EACnCiH,EAAMC,KAAKC,MAAMF,EAb+E,UAc3FA,EAAM,GACf,OAAOjH,CACX,CAqBO,SAASoH,IACZ,MAAMC,EAAMnH,GAAQ,IAAIoH,MACxB,OAAID,IAAQN,GACDC,EAAO,EAAGD,EAAOM,GACrBA,EAAM,IAAMnH,EAAO8G,IAC9B,CAIA,KAAOhJ,EA9CiG,GA8CrFA,IACf8I,EAAID,EAAS7I,IAAMA,EChDvB,IAAIuJ,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,cAKjC,CAHA,MAAOC,GAGP,CACO,MAAMC,EAAUH,ECPhB,SAASI,EAAI5D,GAChB,MAAM6D,EAAU7D,EAAK6D,QAErB,IACI,GAAI,oBAAuBJ,kBAAoBI,GAAWF,GACtD,OAAO,IAAIF,eAGnB,MAAOK,IACP,IAAKD,EACD,IACI,OAAO,IAAIlE,EAAW,CAAC,UAAUoE,OAAO,UAAUC,KAAK,OAAM,qBAEjE,MAAOF,IAEf,CCXA,SAASG,KACT,MAAMC,EAIK,MAHK,IAAIT,EAAe,CAC3BI,SAAS,IAEMM,aAkNhB,MAAMC,UAAgB3G,EAOzB8C,YAAY8D,EAAKrE,GACbW,QACAZ,EAAsBhC,KAAMiC,GAC5BjC,KAAKiC,KAAOA,EACZjC,KAAKuG,OAAStE,EAAKsE,QAAU,MAC7BvG,KAAKsG,IAAMA,EACXtG,KAAK3F,UAAOmM,IAAcvE,EAAK5H,KAAO4H,EAAK5H,KAAO,KAClD2F,KAAKlG,SAOTA,SACI,IAAI2M,EACJ,MAAMxE,EAAOV,EAAKvB,KAAKiC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAK6D,UAAY9F,KAAKiC,KAAKyE,GAC3B,MAAMC,EAAO3G,KAAK2G,IAAM,IAAIjB,EAAezD,GAC3C,IACI0E,EAAIzD,KAAKlD,KAAKuG,OAAQvG,KAAKsG,KAAK,GAChC,IACI,GAAItG,KAAKiC,KAAK2E,aAAc,CACxBD,EAAIE,uBAAyBF,EAAIE,uBAAsB,GACvD,IAAK,IAAI3K,KAAK8D,KAAKiC,KAAK2E,aAChB5G,KAAKiC,KAAK2E,aAAalF,eAAexF,IACtCyK,EAAIG,iBAAiB5K,EAAG8D,KAAKiC,KAAK2E,aAAa1K,KAK/D,MAAO6J,IACP,GAAI,SAAW/F,KAAKuG,OAChB,IACII,EAAIG,iBAAiB,eAAgB,4BAEzC,MAAOf,IAEX,IACIY,EAAIG,iBAAiB,SAAU,OAEnC,MAAOf,IACwB,QAA9BU,EAAKzG,KAAKiC,KAAK8E,iBAA8B,IAAPN,GAAyBA,EAAGO,WAAWL,GAE1E,oBAAqBA,IACrBA,EAAIM,gBAAkBjH,KAAKiC,KAAKgF,iBAEhCjH,KAAKiC,KAAKiF,iBACVP,EAAIQ,QAAUnH,KAAKiC,KAAKiF,gBAE5BP,EAAIS,mBAAqB,KACrB,IAAIX,EACmB,IAAnBE,EAAIxD,aAC2B,QAA9BsD,EAAKzG,KAAKiC,KAAK8E,iBAA8B,IAAPN,GAAyBA,EAAGY,aAAaV,IAEhF,IAAMA,EAAIxD,aAEV,MAAQwD,EAAIW,QAAU,OAASX,EAAIW,OACnCtH,KAAKuH,SAKLvH,KAAKmC,cAAa,KACdnC,KAAKiD,QAA8B,iBAAf0D,EAAIW,OAAsBX,EAAIW,OAAS,EAAE,GAC9D,KAGXX,EAAInD,KAAKxD,KAAK3F,MAElB,MAAO0L,GAOH,YAHA/F,KAAKmC,cAAa,KACdnC,KAAKiD,QAAQ8C,EAAE,GAChB,GAGiB,oBAAbyB,WACPxH,KAAKyH,MAAQpB,EAAQqB,gBACrBrB,EAAQsB,SAAS3H,KAAKyH,OAASzH,MAQvCiD,QAAQ0C,GACJ3F,KAAKgB,aAAa,QAAS2E,EAAK3F,KAAK2G,KACrC3G,KAAK4H,SAAQ,GAOjBA,QAAQC,GACJ,QAAI,IAAuB7H,KAAK2G,KAAO,OAAS3G,KAAK2G,IAArD,CAIA,GADA3G,KAAK2G,IAAIS,mBAAqBlB,EAC1B2B,EACA,IACI7H,KAAK2G,IAAImB,QAEb,MAAO/B,IAEa,oBAAbyB,iBACAnB,EAAQsB,SAAS3H,KAAKyH,OAEjCzH,KAAK2G,IAAM,MAOfY,SACI,MAAMlN,EAAO2F,KAAK2G,IAAIoB,aACT,OAAT1N,IACA2F,KAAKgB,aAAa,OAAQ3G,GAC1B2F,KAAKgB,aAAa,WAClBhB,KAAK4H,WAQbE,QACI9H,KAAK4H,WAUb,GAPAvB,EAAQqB,cAAgB,EACxBrB,EAAQsB,SAAW,GAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,QAEvB,GAAgC,mBAArBpI,iBAAiC,CAE7CA,iBADyB,eAAgB+B,EAAa,WAAa,SAChCqG,GAAe,GAG1D,SAASA,IACL,IAAK,IAAI/L,KAAKmK,EAAQsB,SACdtB,EAAQsB,SAASjG,eAAexF,IAChCmK,EAAQsB,SAASzL,GAAG4L,OAGhC,CCpYY,MAACI,EACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAE/D1H,GAAOyH,QAAQC,UAAUnK,KAAKyC,GAG/B,CAACA,EAAIyB,IAAiBA,EAAazB,EAAI,GAGzC2H,EAAYzG,EAAWyG,WAAazG,EAAW0G,aCJtDC,EAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cCLV,MAACC,EAAa,CACtBC,UDKG,cAAiB/F,EAOpBL,YAAYP,GACRW,MAAMX,GACNjC,KAAK/E,gBAAkBgH,EAAK4G,YAE5BC,WACA,MAAO,YAEX1F,SACI,IAAKpD,KAAK+I,QAEN,OAEJ,MAAMzC,EAAMtG,KAAKsG,MACX0C,EAAYhJ,KAAKiC,KAAK+G,UAEtB/G,EAAOsG,EACP,GACAhH,EAAKvB,KAAKiC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMjC,KAAKiC,KAAK2E,eACV3E,EAAKgH,QAAUjJ,KAAKiC,KAAK2E,cAE7B,IACI5G,KAAKkJ,GACyBX,EAIpB,IAAIF,EAAU/B,EAAK0C,EAAW/G,GAH9B+G,EACI,IAAIX,EAAU/B,EAAK0C,GACnB,IAAIX,EAAU/B,GAGhC,MAAOX,GACH,OAAO3F,KAAKgB,aAAa,QAAS2E,GAEtC3F,KAAKkJ,GAAG3M,WAAayD,KAAKgD,OAAOzG,WACjCyD,KAAKmJ,oBAOTA,oBACInJ,KAAKkJ,GAAGE,OAAS,KACTpJ,KAAKiC,KAAKoH,WACVrJ,KAAKkJ,GAAGI,QAAQC,QAEpBvJ,KAAK2D,QAAQ,EAEjB3D,KAAKkJ,GAAGM,QAAWC,GAAezJ,KAAKuD,QAAQ,CAC3Cb,YAAa,8BACbC,QAAS8G,IAEbzJ,KAAKkJ,GAAGQ,UAAaC,GAAO3J,KAAK4D,OAAO+F,EAAGtP,MAC3C2F,KAAKkJ,GAAGU,QAAW7D,GAAM/F,KAAKiD,QAAQ,kBAAmB8C,GAE7DrC,MAAMD,GACFzD,KAAK8C,UAAW,EAGhB,IAAK,IAAI5G,EAAI,EAAGA,EAAIuH,EAAQtH,OAAQD,IAAK,CACrC,MAAM4B,EAAS2F,EAAQvH,GACjB2N,EAAa3N,IAAMuH,EAAQtH,OAAS,EAC1CnB,EAAa8C,EAAQkC,KAAK/E,gBAAiBZ,IAmBvC,IAGQ2F,KAAKkJ,GAAG1F,KAAKnJ,GAMrB,MAAO0L,IAEH8D,GAGA3B,GAAS,KACLlI,KAAK8C,UAAW,EAChB9C,KAAKgB,aAAa,QAAQ,GAC3BhB,KAAKmC,kBAKxBmB,eAC2B,IAAZtD,KAAKkJ,KACZlJ,KAAKkJ,GAAG7F,QACRrD,KAAKkJ,GAAK,MAQlB5C,MACI,MAAMpC,EAASlE,KAAKiC,KAAKyC,OAAS,MAAQ,KACpC3B,EAAQ/C,KAAK+C,OAAS,GAS5B,OAPI/C,KAAKiC,KAAK6H,oBACV/G,EAAM/C,KAAKiC,KAAK8H,gBAAkBzE,KAGjCtF,KAAK/E,iBACN8H,EAAMiH,IAAM,GAEThK,KAAKiE,UAAUC,EAAQnB,GAQlCgG,QACI,QAASV,IChJb4B,aCFG,cAAiBpH,EAChBiG,WACA,MAAO,eAEX1F,SAEgC,mBAAjB8G,eAIXlK,KAAKmK,UAAY,IAAID,aAAalK,KAAKiE,UAAU,SAAUjE,KAAKiC,KAAKmI,iBAAiBpK,KAAK8I,OAC3F9I,KAAKmK,UAAUE,OACVpM,MAAK,KACN+B,KAAKuD,SAAS,IAEb+G,OAAO3E,IACR3F,KAAKiD,QAAQ,qBAAsB0C,EAAI,IAG3C3F,KAAKmK,UAAUI,MAAMtM,MAAK,KACtB+B,KAAKmK,UAAUK,4BAA4BvM,MAAMwM,IAC7C,MAAMC,Eb8Df,SAAmCC,EAAYpO,GAC7CwC,IACDA,EAAe,IAAI6L,aAEvB,MAAM3L,EAAS,GACf,IAAI4L,EAAQ,EACRC,GAAkB,EAClBC,GAAW,EACf,OAAO,IAAInN,gBAAgB,CACvBC,UAAUuB,EAAOrB,GAEb,IADAkB,EAAOiB,KAAKd,KACC,CACT,GAAc,IAAVyL,EAA+B,CAC/B,GAAI7L,EAAYC,GAAU,EACtB,MAEJ,MAAMV,EAASc,EAAaJ,EAAQ,GACpC8L,EAAkC,MAAV,IAAZxM,EAAO,IACnBuM,EAA6B,IAAZvM,EAAO,GAEpBsM,EADAC,EAAiB,IACT,EAEgB,MAAnBA,EACG,EAGA,OAGX,GAAc,IAAVD,EAA2C,CAChD,GAAI7L,EAAYC,GAAU,EACtB,MAEJ,MAAM+L,EAAc3L,EAAaJ,EAAQ,GACzC6L,EAAiB,IAAItM,SAASwM,EAAYjQ,OAAQiQ,EAAYnP,WAAYmP,EAAY7O,QAAQ8O,UAAU,GACxGJ,EAAQ,OAEP,GAAc,IAAVA,EAA2C,CAChD,GAAI7L,EAAYC,GAAU,EACtB,MAEJ,MAAM+L,EAAc3L,EAAaJ,EAAQ,GACnCP,EAAO,IAAIF,SAASwM,EAAYjQ,OAAQiQ,EAAYnP,WAAYmP,EAAY7O,QAC5E+O,EAAIxM,EAAKyM,UAAU,GACzB,GAAID,EAAI9F,KAAKgG,IAAI,EAAG,IAAW,EAAG,CAE9BrN,EAAWe,QAAQ3E,GACnB,MAEJ2Q,EAAiBI,EAAI9F,KAAKgG,IAAI,EAAG,IAAM1M,EAAKyM,UAAU,GACtDN,EAAQ,MAEP,CACD,GAAI7L,EAAYC,GAAU6L,EACtB,MAEJ,MAAMzQ,EAAOgF,EAAaJ,EAAQ6L,GAClC/M,EAAWe,QAAQzC,EAAa0O,EAAW1Q,EAAO0E,EAAaxB,OAAOlD,GAAOkC,IAC7EsO,EAAQ,EAEZ,GAAuB,IAAnBC,GAAwBA,EAAiBH,EAAY,CACrD5M,EAAWe,QAAQ3E,GACnB,UAKpB,CajIsCkR,CAA0B1G,OAAO2G,iBAAkBtL,KAAKgD,OAAOzG,YAC/EgP,EAASd,EAAOe,SAASC,YAAYf,GAAegB,YACpDC,EAAgBhO,IACtBgO,EAAcH,SAASI,OAAOnB,EAAO3H,UACrC9C,KAAK6L,OAASF,EAAc7I,SAASgJ,YACrC,MAAMC,EAAO,KACTR,EACKQ,OACA9N,MAAK,EAAG+N,OAAMvG,YACXuG,IAGJhM,KAAK6D,SAAS4B,GACdsG,IAAM,IAELzB,OAAO3E,IAAD,GACT,EAENoG,IACA,MAAMjO,EAAS,CAAE1D,KAAM,QACnB4F,KAAK+C,MAAMkJ,MACXnO,EAAOzD,KAAO,WAAW2F,KAAK+C,MAAMkJ,SAExCjM,KAAK6L,OAAOnI,MAAM5F,GAAQG,MAAK,IAAM+B,KAAK2D,UAAS,GACrD,KAGVD,MAAMD,GACFzD,KAAK8C,UAAW,EAChB,IAAK,IAAI5G,EAAI,EAAGA,EAAIuH,EAAQtH,OAAQD,IAAK,CACrC,MAAM4B,EAAS2F,EAAQvH,GACjB2N,EAAa3N,IAAMuH,EAAQtH,OAAS,EAC1C6D,KAAK6L,OAAOnI,MAAM5F,GAAQG,MAAK,KACvB4L,GACA3B,GAAS,KACLlI,KAAK8C,UAAW,EAChB9C,KAAKgB,aAAa,QAAQ,GAC3BhB,KAAKmC,kBAKxBmB,UACI,IAAImD,EACsB,QAAzBA,EAAKzG,KAAKmK,iBAA8B,IAAP1D,GAAyBA,EAAGpD,UD9DlE6I,QHQG,cAAsBrJ,EAOzBL,YAAYP,GAGR,GAFAW,MAAMX,GACNjC,KAAKkM,SAAU,EACS,oBAAbC,SAA0B,CACjC,MAAMC,EAAQ,WAAaD,SAASE,SACpC,IAAI5H,EAAO0H,SAAS1H,KAEfA,IACDA,EAAO2H,EAAQ,MAAQ,MAE3BpM,KAAK0G,GACoB,oBAAbyF,UACJlK,EAAKsC,WAAa4H,SAAS5H,UAC3BE,IAASxC,EAAKwC,KAK1B,MAAMoE,EAAc5G,GAAQA,EAAK4G,YACjC7I,KAAK/E,eAAiBkL,IAAY0C,EAC9B7I,KAAKiC,KAAKgF,kBACVjH,KAAK+G,eAAYuF,GAGrBxD,WACA,MAAO,UAQX1F,SACIpD,KAAKuM,OAQTxI,MAAMC,GACFhE,KAAKmD,WAAa,UAClB,MAAMY,EAAQ,KACV/D,KAAKmD,WAAa,SAClBa,GAAS,EAEb,GAAIhE,KAAKkM,UAAYlM,KAAK8C,SAAU,CAChC,IAAI0J,EAAQ,EACRxM,KAAKkM,UACLM,IACAxM,KAAKG,KAAK,gBAAgB,aACpBqM,GAASzI,QAGd/D,KAAK8C,WACN0J,IACAxM,KAAKG,KAAK,SAAS,aACbqM,GAASzI,aAKnBA,IAQRwI,OACIvM,KAAKkM,SAAU,EACflM,KAAKyM,SACLzM,KAAKgB,aAAa,QAOtB4C,OAAOvJ,GTpFW,EAACqS,EAAgBnQ,KACnC,MAAMoQ,EAAiBD,EAAejR,MAAM+B,GACtCiG,EAAU,GAChB,IAAK,IAAIvH,EAAI,EAAGA,EAAIyQ,EAAexQ,OAAQD,IAAK,CAC5C,MAAM0Q,EAAgBvQ,EAAasQ,EAAezQ,GAAIK,GAEtD,GADAkH,EAAQvD,KAAK0M,GACc,UAAvBA,EAAcxS,KACd,MAGR,OAAOqJ,CAAO,ESyFVoJ,CAAcxS,EAAM2F,KAAKgD,OAAOzG,YAAYtC,SAd1B6D,IAMd,GAJI,YAAckC,KAAKmD,YAA8B,SAAhBrF,EAAO1D,MACxC4F,KAAK2D,SAGL,UAAY7F,EAAO1D,KAEnB,OADA4F,KAAKuD,QAAQ,CAAEb,YAAa,oCACrB,EAGX1C,KAAK6D,SAAS/F,EAAO,IAKrB,WAAakC,KAAKmD,aAElBnD,KAAKkM,SAAU,EACflM,KAAKgB,aAAa,gBACd,SAAWhB,KAAKmD,YAChBnD,KAAKuM,QAWjBjJ,UACI,MAAMD,EAAQ,KACVrD,KAAK0D,MAAM,CAAC,CAAEtJ,KAAM,UAAW,EAE/B,SAAW4F,KAAKmD,WAChBE,IAKArD,KAAKG,KAAK,OAAQkD,GAS1BK,MAAMD,GACFzD,KAAK8C,UAAW,ETxJF,EAACW,EAASvI,KAE5B,MAAMiB,EAASsH,EAAQtH,OACjBwQ,EAAiB,IAAI5L,MAAM5E,GACjC,IAAI2Q,EAAQ,EACZrJ,EAAQxJ,SAAQ,CAAC6D,EAAQ5B,KAErBlB,EAAa8C,GAAQ,GAAOxB,IACxBqQ,EAAezQ,GAAKI,IACdwQ,IAAU3Q,GACZjB,EAASyR,EAAe1G,KAAKzI,MAEnC,GACJ,ES4IEuP,CAActJ,GAAUpJ,IACpB2F,KAAKgN,QAAQ3S,GAAM,KACf2F,KAAK8C,UAAW,EAChB9C,KAAKgB,aAAa,QAAQ,GAC5B,IAQVsF,MACI,MAAMpC,EAASlE,KAAKiC,KAAKyC,OAAS,QAAU,OACtC3B,EAAQ/C,KAAK+C,OAAS,GAQ5B,OANI,IAAU/C,KAAKiC,KAAK6H,oBACpB/G,EAAM/C,KAAKiC,KAAK8H,gBAAkBzE,KAEjCtF,KAAK/E,gBAAmB8H,EAAMkJ,MAC/BlJ,EAAMiH,IAAM,GAEThK,KAAKiE,UAAUC,EAAQnB,GAQlCkK,QAAQhL,EAAO,IAEX,OADApI,OAAOqT,OAAOjL,EAAM,CAAEyE,GAAI1G,KAAK0G,GAAIK,UAAW/G,KAAK+G,WAAa/G,KAAKiC,MAC9D,IAAIoE,EAAQrG,KAAKsG,MAAOrE,GASnC+K,QAAQ3S,EAAM0F,GACV,MAAMoN,EAAMnN,KAAKiN,QAAQ,CACrB1G,OAAQ,OACRlM,KAAMA,IAEV8S,EAAIvN,GAAG,UAAWG,GAClBoN,EAAIvN,GAAG,SAAS,CAACwN,EAAWzK,KACxB3C,KAAKiD,QAAQ,iBAAkBmK,EAAWzK,EAAQ,IAQ1D8J,SACI,MAAMU,EAAMnN,KAAKiN,UACjBE,EAAIvN,GAAG,OAAQI,KAAK4D,OAAOxB,KAAKpC,OAChCmN,EAAIvN,GAAG,SAAS,CAACwN,EAAWzK,KACxB3C,KAAKiD,QAAQ,iBAAkBmK,EAAWzK,EAAQ,IAEtD3C,KAAKqN,QAAUF,KKxMjBG,EAAK,sPACLC,EAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,EAAM3I,GAClB,GAAIA,EAAI1I,OAAS,IACb,KAAM,eAEV,MAAMsR,EAAM5I,EAAK6I,EAAI7I,EAAIL,QAAQ,KAAMuB,EAAIlB,EAAIL,QAAQ,MAC7C,GAANkJ,IAAiB,GAAN3H,IACXlB,EAAMA,EAAIlI,UAAU,EAAG+Q,GAAK7I,EAAIlI,UAAU+Q,EAAG3H,GAAG4H,QAAQ,KAAM,KAAO9I,EAAIlI,UAAUoJ,EAAGlB,EAAI1I,SAE9F,IAAIyR,EAAIN,EAAGO,KAAKhJ,GAAO,IAAKyB,EAAM,GAAIpK,EAAI,GAC1C,KAAOA,KACHoK,EAAIiH,EAAMrR,IAAM0R,EAAE1R,IAAM,GAU5B,OARU,GAANwR,IAAiB,GAAN3H,IACXO,EAAIwH,OAASL,EACbnH,EAAIyH,KAAOzH,EAAIyH,KAAKpR,UAAU,EAAG2J,EAAIyH,KAAK5R,OAAS,GAAGwR,QAAQ,KAAM,KACpErH,EAAI0H,UAAY1H,EAAI0H,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9ErH,EAAI2H,SAAU,GAElB3H,EAAI4H,UAIR,SAAmBpT,EAAKuJ,GACpB,MAAM8J,EAAO,WAAYC,EAAQ/J,EAAKsJ,QAAQQ,EAAM,KAAK1S,MAAM,KACvC,KAApB4I,EAAK5E,MAAM,EAAG,IAA6B,IAAhB4E,EAAKlI,QAChCiS,EAAMxN,OAAO,EAAG,GAEE,KAAlByD,EAAK5E,OAAO,IACZ2O,EAAMxN,OAAOwN,EAAMjS,OAAS,EAAG,GAEnC,OAAOiS,CACX,CAboBF,CAAU5H,EAAKA,EAAU,MACzCA,EAAI+H,SAaR,SAAkB/H,EAAKvD,GACnB,MAAM1I,EAAO,GAMb,OALA0I,EAAM4K,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACAlU,EAAKkU,GAAMC,MAGZnU,CACX,CArBmBgU,CAAS/H,EAAKA,EAAW,OACjCA,CACX,CCrCO,MAAMmI,UAAe/O,EAOxB8C,YAAY8D,EAAKrE,EAAO,IACpBW,QACA5C,KAAKzD,WLJoB,cKKzByD,KAAK0O,YAAc,GACfpI,GAAO,iBAAoBA,IAC3BrE,EAAOqE,EACPA,EAAM,MAENA,GACAA,EAAMkH,EAAMlH,GACZrE,EAAKsC,SAAW+B,EAAIyH,KACpB9L,EAAKyC,OAA0B,UAAjB4B,EAAI+F,UAAyC,QAAjB/F,EAAI+F,SAC9CpK,EAAKwC,KAAO6B,EAAI7B,KACZ6B,EAAIvD,QACJd,EAAKc,MAAQuD,EAAIvD,QAEhBd,EAAK8L,OACV9L,EAAKsC,SAAWiJ,EAAMvL,EAAK8L,MAAMA,MAErC/L,EAAsBhC,KAAMiC,GAC5BjC,KAAK0E,OACD,MAAQzC,EAAKyC,OACPzC,EAAKyC,OACe,oBAAbyH,UAA4B,WAAaA,SAASE,SAC/DpK,EAAKsC,WAAatC,EAAKwC,OAEvBxC,EAAKwC,KAAOzE,KAAK0E,OAAS,MAAQ,MAEtC1E,KAAKuE,SACDtC,EAAKsC,WACoB,oBAAb4H,SAA2BA,SAAS5H,SAAW,aAC/DvE,KAAKyE,KACDxC,EAAKwC,OACoB,oBAAb0H,UAA4BA,SAAS1H,KACvC0H,SAAS1H,KACTzE,KAAK0E,OACD,MACA,MAClB1E,KAAK2I,WAAa1G,EAAK0G,YAAc,CACjC,UACA,YACA,gBAEJ3I,KAAK0O,YAAc,GACnB1O,KAAK2O,cAAgB,EACrB3O,KAAKiC,KAAOpI,OAAOqT,OAAO,CACtB7I,KAAM,aACNuK,OAAO,EACP3H,iBAAiB,EACjB4H,SAAS,EACT9E,eAAgB,IAChB+E,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEf9E,iBAAkB,GAClB+E,qBAAqB,GACtBlN,GACHjC,KAAKiC,KAAKoC,KACNrE,KAAKiC,KAAKoC,KAAKsJ,QAAQ,MAAO,KACzB3N,KAAKiC,KAAK8M,iBAAmB,IAAM,IACb,iBAApB/O,KAAKiC,KAAKc,QACjB/C,KAAKiC,KAAKc,MVrDf,SAAgBqM,GACnB,IAAIC,EAAM,GACNC,EAAQF,EAAG3T,MAAM,KACrB,IAAK,IAAIS,EAAI,EAAGqT,EAAID,EAAMnT,OAAQD,EAAIqT,EAAGrT,IAAK,CAC1C,IAAIsT,EAAOF,EAAMpT,GAAGT,MAAM,KAC1B4T,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,IAE/D,OAAOH,CACX,CU6C8B9R,CAAOyC,KAAKiC,KAAKc,QAGvC/C,KAAK0P,GAAK,KACV1P,KAAK2P,SAAW,KAChB3P,KAAK4P,aAAe,KACpB5P,KAAK6P,YAAc,KAEnB7P,KAAK8P,iBAAmB,KACQ,mBAArBjQ,mBACHG,KAAKiC,KAAKkN,sBAIVnP,KAAK+P,0BAA4B,KACzB/P,KAAKmK,YAELnK,KAAKmK,UAAU3J,qBACfR,KAAKmK,UAAU9G,UAGvBxD,iBAAiB,eAAgBG,KAAK+P,2BAA2B,IAE/C,cAAlB/P,KAAKuE,WACLvE,KAAKgQ,qBAAuB,KACxBhQ,KAAKuD,QAAQ,kBAAmB,CAC5Bb,YAAa,2BACf,EAEN7C,iBAAiB,UAAWG,KAAKgQ,sBAAsB,KAG/DhQ,KAAKkD,OAST+M,gBAAgBnH,GACZ,MAAM/F,EAAQlJ,OAAOqT,OAAO,GAAIlN,KAAKiC,KAAKc,OAE1CA,EAAMmN,IfgCU,Ee9BhBnN,EAAMoH,UAAYrB,EAEd9I,KAAK0P,KACL3M,EAAMkJ,IAAMjM,KAAK0P,IACrB,MAAMzN,EAAOpI,OAAOqT,OAAO,GAAIlN,KAAKiC,KAAM,CACtCc,QACAC,OAAQhD,KACRuE,SAAUvE,KAAKuE,SACfG,OAAQ1E,KAAK0E,OACbD,KAAMzE,KAAKyE,MACZzE,KAAKiC,KAAKmI,iBAAiBtB,IAC9B,OAAO,IAAIH,EAAWG,GAAM7G,GAOhCiB,OACI,IAAIiH,EACJ,GAAInK,KAAKiC,KAAK6M,iBACVL,EAAO0B,wBACmC,IAA1CnQ,KAAK2I,WAAWnE,QAAQ,aACxB2F,EAAY,gBAEX,IAAI,IAAMnK,KAAK2I,WAAWxM,OAK3B,YAHA6D,KAAKmC,cAAa,KACdnC,KAAKgB,aAAa,QAAS,0BAA0B,GACtD,GAIHmJ,EAAYnK,KAAK2I,WAAW,GAEhC3I,KAAKmD,WAAa,UAElB,IACIgH,EAAYnK,KAAKiQ,gBAAgB9F,GAErC,MAAOpE,GAGH,OAFA/F,KAAK2I,WAAWpJ,aAChBS,KAAKkD,OAGTiH,EAAUjH,OACVlD,KAAKoQ,aAAajG,GAOtBiG,aAAajG,GACLnK,KAAKmK,WACLnK,KAAKmK,UAAU3J,qBAGnBR,KAAKmK,UAAYA,EAEjBA,EACKvK,GAAG,QAASI,KAAKqQ,QAAQjO,KAAKpC,OAC9BJ,GAAG,SAAUI,KAAK6D,SAASzB,KAAKpC,OAChCJ,GAAG,QAASI,KAAKiD,QAAQb,KAAKpC,OAC9BJ,GAAG,SAAU6C,GAAWzC,KAAKuD,QAAQ,kBAAmBd,KAQjE6N,MAAMxH,GACF,IAAIqB,EAAYnK,KAAKiQ,gBAAgBnH,GACjCyH,GAAS,EACb9B,EAAO0B,uBAAwB,EAC/B,MAAMK,EAAkB,KAChBD,IAEJpG,EAAU3G,KAAK,CAAC,CAAEpJ,KAAM,OAAQC,KAAM,WACtC8P,EAAUhK,KAAK,UAAWsQ,IACtB,IAAIF,EAEJ,GAAI,SAAWE,EAAIrW,MAAQ,UAAYqW,EAAIpW,KAAM,CAG7C,GAFA2F,KAAK0Q,WAAY,EACjB1Q,KAAKgB,aAAa,YAAamJ,IAC1BA,EACD,OACJsE,EAAO0B,sBAAwB,cAAgBhG,EAAUrB,KACzD9I,KAAKmK,UAAUpG,OAAM,KACbwM,GAEA,WAAavQ,KAAKmD,aAEtByE,IACA5H,KAAKoQ,aAAajG,GAClBA,EAAU3G,KAAK,CAAC,CAAEpJ,KAAM,aACxB4F,KAAKgB,aAAa,UAAWmJ,GAC7BA,EAAY,KACZnK,KAAK0Q,WAAY,EACjB1Q,KAAK2Q,QAAO,QAGf,CACD,MAAMhL,EAAM,IAAIpD,MAAM,eAEtBoD,EAAIwE,UAAYA,EAAUrB,KAC1B9I,KAAKgB,aAAa,eAAgB2E,OAExC,EAEN,SAASiL,IACDL,IAGJA,GAAS,EACT3I,IACAuC,EAAU9G,QACV8G,EAAY,MAGhB,MAAMP,EAAWjE,IACb,MAAMkL,EAAQ,IAAItO,MAAM,gBAAkBoD,GAE1CkL,EAAM1G,UAAYA,EAAUrB,KAC5B8H,IACA5Q,KAAKgB,aAAa,eAAgB6P,EAAM,EAE5C,SAASC,IACLlH,EAAQ,oBAGZ,SAASJ,IACLI,EAAQ,iBAGZ,SAASmH,EAAUC,GACX7G,GAAa6G,EAAGlI,OAASqB,EAAUrB,MACnC8H,IAIR,MAAMhJ,EAAU,KACZuC,EAAU5J,eAAe,OAAQiQ,GACjCrG,EAAU5J,eAAe,QAASqJ,GAClCO,EAAU5J,eAAe,QAASuQ,GAClC9Q,KAAKI,IAAI,QAASoJ,GAClBxJ,KAAKI,IAAI,YAAa2Q,EAAU,EAEpC5G,EAAUhK,KAAK,OAAQqQ,GACvBrG,EAAUhK,KAAK,QAASyJ,GACxBO,EAAUhK,KAAK,QAAS2Q,GACxB9Q,KAAKG,KAAK,QAASqJ,GACnBxJ,KAAKG,KAAK,YAAa4Q,IACwB,IAA3C/Q,KAAK2P,SAASnL,QAAQ,iBACb,iBAATsE,EAEA9I,KAAKmC,cAAa,KACToO,GACDpG,EAAUjH,SAEf,KAGHiH,EAAUjH,OAQlBS,SAOI,GANA3D,KAAKmD,WAAa,OAClBsL,EAAO0B,sBAAwB,cAAgBnQ,KAAKmK,UAAUrB,KAC9D9I,KAAKgB,aAAa,QAClBhB,KAAK2Q,QAGD,SAAW3Q,KAAKmD,YAAcnD,KAAKiC,KAAK4M,QAAS,CACjD,IAAI3S,EAAI,EACR,MAAMqT,EAAIvP,KAAK2P,SAASxT,OACxB,KAAOD,EAAIqT,EAAGrT,IACV8D,KAAKsQ,MAAMtQ,KAAK2P,SAASzT,KASrC2H,SAAS/F,GACL,GAAI,YAAckC,KAAKmD,YACnB,SAAWnD,KAAKmD,YAChB,YAAcnD,KAAKmD,WAKnB,OAJAnD,KAAKgB,aAAa,SAAUlD,GAE5BkC,KAAKgB,aAAa,aAClBhB,KAAKiR,mBACGnT,EAAO1D,MACX,IAAK,OACD4F,KAAKkR,YAAYC,KAAK3D,MAAM1P,EAAOzD,OACnC,MACJ,IAAK,OACD2F,KAAKoR,WAAW,QAChBpR,KAAKgB,aAAa,QAClBhB,KAAKgB,aAAa,QAClB,MACJ,IAAK,QACD,MAAM2E,EAAM,IAAIpD,MAAM,gBAEtBoD,EAAI0L,KAAOvT,EAAOzD,KAClB2F,KAAKiD,QAAQ0C,GACb,MACJ,IAAK,UACD3F,KAAKgB,aAAa,OAAQlD,EAAOzD,MACjC2F,KAAKgB,aAAa,UAAWlD,EAAOzD,OAapD6W,YAAY7W,GACR2F,KAAKgB,aAAa,YAAa3G,GAC/B2F,KAAK0P,GAAKrV,EAAK4R,IACfjM,KAAKmK,UAAUpH,MAAMkJ,IAAM5R,EAAK4R,IAChCjM,KAAK2P,SAAW3P,KAAKsR,eAAejX,EAAKsV,UACzC3P,KAAK4P,aAAevV,EAAKuV,aACzB5P,KAAK6P,YAAcxV,EAAKwV,YACxB7P,KAAK2K,WAAatQ,EAAKsQ,WACvB3K,KAAK2D,SAED,WAAa3D,KAAKmD,YAEtBnD,KAAKiR,mBAOTA,mBACIjR,KAAKqC,eAAerC,KAAK8P,kBACzB9P,KAAK8P,iBAAmB9P,KAAKmC,cAAa,KACtCnC,KAAKuD,QAAQ,eAAe,GAC7BvD,KAAK4P,aAAe5P,KAAK6P,aACxB7P,KAAKiC,KAAKoH,WACVrJ,KAAK8P,iBAAiBvG,QAQ9B8G,UACIrQ,KAAK0O,YAAY9N,OAAO,EAAGZ,KAAK2O,eAIhC3O,KAAK2O,cAAgB,EACjB,IAAM3O,KAAK0O,YAAYvS,OACvB6D,KAAKgB,aAAa,SAGlBhB,KAAK2Q,QAQbA,QACI,GAAI,WAAa3Q,KAAKmD,YAClBnD,KAAKmK,UAAUrH,WACd9C,KAAK0Q,WACN1Q,KAAK0O,YAAYvS,OAAQ,CACzB,MAAMsH,EAAUzD,KAAKuR,qBACrBvR,KAAKmK,UAAU3G,KAAKC,GAGpBzD,KAAK2O,cAAgBlL,EAAQtH,OAC7B6D,KAAKgB,aAAa,UAS1BuQ,qBAII,KAH+BvR,KAAK2K,YACR,YAAxB3K,KAAKmK,UAAUrB,MACf9I,KAAK0O,YAAYvS,OAAS,GAE1B,OAAO6D,KAAK0O,YAEhB,IAAI8C,EAAc,EAClB,IAAK,IAAItV,EAAI,EAAGA,EAAI8D,KAAK0O,YAAYvS,OAAQD,IAAK,CAC9C,MAAM7B,EAAO2F,KAAK0O,YAAYxS,GAAG7B,KAIjC,GAHIA,IACAmX,GZzZO,iBADI1W,EY0ZeT,GZnZ1C,SAAoBwK,GAChB,IAAI4M,EAAI,EAAGtV,EAAS,EACpB,IAAK,IAAID,EAAI,EAAGqT,EAAI1K,EAAI1I,OAAQD,EAAIqT,EAAGrT,IACnCuV,EAAI5M,EAAIzI,WAAWF,GACfuV,EAAI,IACJtV,GAAU,EAELsV,EAAI,KACTtV,GAAU,EAELsV,EAAI,OAAUA,GAAK,MACxBtV,GAAU,GAGVD,IACAC,GAAU,GAGlB,OAAOA,CACX,CAxBeuV,CAAW5W,GAGfsK,KAAKuM,KAPQ,MAOF7W,EAAIgB,YAAchB,EAAIwE,QYuZ5BpD,EAAI,GAAKsV,EAAcxR,KAAK2K,WAC5B,OAAO3K,KAAK0O,YAAYjP,MAAM,EAAGvD,GAErCsV,GAAe,EZ/ZpB,IAAoB1W,EYianB,OAAOkF,KAAK0O,YAUhBhL,MAAM+M,EAAKmB,EAAS7R,GAEhB,OADAC,KAAKoR,WAAW,UAAWX,EAAKmB,EAAS7R,GAClCC,KAEXwD,KAAKiN,EAAKmB,EAAS7R,GAEf,OADAC,KAAKoR,WAAW,UAAWX,EAAKmB,EAAS7R,GAClCC,KAWXoR,WAAWhX,EAAMC,EAAMuX,EAAS7R,GAS5B,GARI,mBAAsB1F,IACtB0F,EAAK1F,EACLA,OAAOmM,GAEP,mBAAsBoL,IACtB7R,EAAK6R,EACLA,EAAU,MAEV,YAAc5R,KAAKmD,YAAc,WAAanD,KAAKmD,WACnD,QAEJyO,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,MAAM/T,EAAS,CACX1D,KAAMA,EACNC,KAAMA,EACNuX,QAASA,GAEb5R,KAAKgB,aAAa,eAAgBlD,GAClCkC,KAAK0O,YAAYxO,KAAKpC,GAClBiC,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAK2Q,QAKTtN,QACI,MAAMA,EAAQ,KACVrD,KAAKuD,QAAQ,gBACbvD,KAAKmK,UAAU9G,OAAO,EAEpByO,EAAkB,KACpB9R,KAAKI,IAAI,UAAW0R,GACpB9R,KAAKI,IAAI,eAAgB0R,GACzBzO,GAAO,EAEL0O,EAAiB,KAEnB/R,KAAKG,KAAK,UAAW2R,GACrB9R,KAAKG,KAAK,eAAgB2R,EAAgB,EAqB9C,MAnBI,YAAc9R,KAAKmD,YAAc,SAAWnD,KAAKmD,aACjDnD,KAAKmD,WAAa,UACdnD,KAAK0O,YAAYvS,OACjB6D,KAAKG,KAAK,SAAS,KACXH,KAAK0Q,UACLqB,IAGA1O,OAIHrD,KAAK0Q,UACVqB,IAGA1O,KAGDrD,KAOXiD,QAAQ0C,GACJ8I,EAAO0B,uBAAwB,EAC/BnQ,KAAKgB,aAAa,QAAS2E,GAC3B3F,KAAKuD,QAAQ,kBAAmBoC,GAOpCpC,QAAQd,EAAQC,GACR,YAAc1C,KAAKmD,YACnB,SAAWnD,KAAKmD,YAChB,YAAcnD,KAAKmD,aAEnBnD,KAAKqC,eAAerC,KAAK8P,kBAEzB9P,KAAKmK,UAAU3J,mBAAmB,SAElCR,KAAKmK,UAAU9G,QAEfrD,KAAKmK,UAAU3J,qBACoB,mBAAxBC,sBACPA,oBAAoB,eAAgBT,KAAK+P,2BAA2B,GACpEtP,oBAAoB,UAAWT,KAAKgQ,sBAAsB,IAG9DhQ,KAAKmD,WAAa,SAElBnD,KAAK0P,GAAK,KAEV1P,KAAKgB,aAAa,QAASyB,EAAQC,GAGnC1C,KAAK0O,YAAc,GACnB1O,KAAK2O,cAAgB,GAS7B2C,eAAe3B,GACX,MAAMqC,EAAmB,GACzB,IAAI9V,EAAI,EACR,MAAMsD,EAAImQ,EAASxT,OACnB,KAAOD,EAAIsD,EAAGtD,KACL8D,KAAK2I,WAAWnE,QAAQmL,EAASzT,KAClC8V,EAAiB9R,KAAKyP,EAASzT,IAEvC,OAAO8V,GAGfvD,EAAOpC,SfvbiB,EgBxJZ,MAACA,EAAWoC,EAAOpC"}