import { IsEmail, IsNotEmpty, IsEnum, Validate<PERSON>f, IsS<PERSON> } from 'class-validator';
import { AuthProvider } from './signin.dto';

export class VerifyOtpDto {
  @IsEnum(AuthProvider)
  @IsNotEmpty()
  provider: AuthProvider;

  @ValidateIf(o => o.provider === AuthProvider.EMAIL)
  @IsEmail()
  @IsNotEmpty()
  email?: string;

  @ValidateIf(o => o.provider === AuthProvider.WHATSAPP || o.provider === AuthProvider.SMS)
  @IsString()
  @IsNotEmpty()
  phoneNumber?: string;
  
  @IsString()
  @IsNotEmpty()
  code?: string;
}