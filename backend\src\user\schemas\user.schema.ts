import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
}

export enum Religion {
  ISLAM = 'islam',
  KRISTEN = 'kristen',
  KATHOLIK = 'katholik',
  HINDU = 'hindu',
  BUDDHA = 'buddha',
  KONGHUCU = 'konghucu'
}

export type UserDocument = User & Document & { photos: Types.DocumentArray<Photo> };

@Schema({ timestamps: true })
export class Photo {
  @Prop({ default: null })
  url: string;

  @Prop({ default: null })
  key: string;
}

export const PhotoSchema = SchemaFactory.createForClass(Photo);

@Schema({ timestamps: true })
export class User {
  @Prop({ default: null })
  name: string;

  @Prop({ default: null })
  email: string;

  @Prop({ default: null })
  verified: boolean;

  @Prop({ default: null })
  phoneNumber: string;

  @Prop({ type: Photo, default: null })
  image: Photo;

  @Prop({ default: null })
  dateOfBirth: Date;

  @Prop({ type: [PhotoSchema], default: [] })
  photos: Types.DocumentArray<Photo>;

  @Prop({ enum: Gender, default: null })
  gender: string;

  @Prop({ enum: Religion, default: null })
  religion: string;

  @Prop({ default: null })
  occupation: string;

  @Prop({ default: false })
  isSmoker: boolean;

  @Prop({ default: false })
  acceptDifferentReligion: boolean;

  @Prop({ default: null })
  about: string;

  @Prop({ default: false })
  psychTestCompleted: boolean;

  @Prop({ enum: UserStatus, default: UserStatus.ACTIVE })
  status: UserStatus;
}

export const UserSchema = SchemaFactory.createForClass(User);
