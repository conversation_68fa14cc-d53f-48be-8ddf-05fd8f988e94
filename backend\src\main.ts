import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { json } from 'express';
import { SeedService } from './seed/seed.service';

if (!(global as any).crypto) {
  (global as any).crypto = require('crypto');
}

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  
  // Configure body parser for larger payloads (needed for WhatsApp media)
  app.use(json({ limit: '50mb' }));
  
  // Enable CORS
  app.enableCors();
  
  // Enable validation
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true
  }));
  
  const port = configService.get<number>('app.port') as number;
  const corsEnabled = configService.get<boolean>('app.cors.enabled');
  
  // Run seed in development
  if (process.env.NODE_ENV === 'development') {
    const seedService = app.get(SeedService);
    await seedService.seed();
  }
  const corsOrigin = configService.get<string>('app.cors.origin');
  
  // Configure CORS if enabled
  if (corsEnabled) {
    app.enableCors({
      origin: corsOrigin,
    });
  }
  
  await app.listen(port);
  logger.log(`Application is running on: http://localhost:${port}`);
}
bootstrap();
