# Authentication API Documentation

This document provides detailed information about the Authentication API endpoints, request/response formats, and examples.

## Table of Contents
- [Sign In](#sign-in)
- [Sign Up](#sign-up)
- [Verify OTP](#verify-otp)
- [Get Profile](#get-profile)
- [Update Profile](#update-profile)
- [Upload Profile Image](#upload-profile-image)
- [Upload Photos](#upload-photos)
- [Delete Photos](#delete-photos)
- [Data Types](#data-types)
  - [Auth Profile Response](#auth-profile-response)
  - [Photo Response](#photo-response)

## Base URL
```
{{base_url}}/auth
```

## Authentication
Most endpoints require JWT authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Sign In

Authenticate a user and request OTP for verification.

### Endpoint
```
POST /signin
```

### Request Body
```typescript
{
  "provider": "email" | "whatsapp" | "sms",
  "email"?: string,  // Required if provider is 'email'
  "phoneNumber"?: string  // Required if provider is 'whatsapp' or 'sms'
}
```

### Example Request
```json
{
  "provider": "email",
  "email": "<EMAIL>"
}
```

### Response
```json
{
  "message": "OTP sent to email"
}
```

### Possible Errors
- `400 Bad Request`: If provider is invalid or required fields are missing
- `404 Not Found`: If user doesn't exist (for sign in)

## Sign Up

Register a new user account.

### Endpoint
```
POST /signup
```

### Request Body
```typescript
{
  "provider": "email" | "whatsapp" | "sms",
  "name": string,
  "email"?: string,  // Required if provider is 'email'
  "phoneNumber"?: string,  // Required if provider is 'whatsapp' or 'sms'
  "gender"?: string,
  "religion"?: string,
  "occupation"?: string,
  "isSmoker"?: boolean,
  "acceptDifferentReligion"?: boolean,
  "about"?: string
}
```

### Example Request
```json
{
  "provider": "email",
  "name": "John Doe",
  "email": "<EMAIL>",
  "gender": "male",
  "religion": "islam"
}
```

### Response
```json
{
  "message": "OTP sent to email"
}
```

### Possible Errors
- `400 Bad Request`: If user already exists or validation fails
- `400 Bad Request`: If provider is invalid or required fields are missing

## Verify OTP

Verify the OTP sent to the user's email/phone.

### Endpoint
```
POST /verify-otp
```

### Request Body
```typescript
{
  "provider": "email" | "whatsapp" | "sms",
  "email"?: string,  // Required if provider is 'email'
  "phoneNumber"?: string,  // Required if provider is 'whatsapp' or 'sms'
  "code": string  // The OTP code
}
```

### Example Request
```json
{
  "provider": "email",
  "email": "<EMAIL>",
  "code": "123456"
}
```

### Response
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiredAt": **********,
  "issuedAt": **********
}
```

### Possible Errors
- `400 Bad Request`: If OTP is invalid or expired
- `404 Not Found`: If user doesn't exist

## Get Profile

Get the authenticated user's profile.

### Endpoint
```
GET /profile
```

### Response
```json
{
  "id": "60d5ec9f2f8e4c3d4c8b4567",
  "name": "John Doe",
  "email": "<EMAIL>",
  "phoneNumber": "+6281234567890",
  "image": "https://example.com/profile.jpg",
  "dateOfBirth": "1990-01-01T00:00:00.000Z",
  "photos": [
    {
      "id": "60d5ec9f2f8e4c3d4c8b4568",
      "url": "https://example.com/photo1.jpg",
      "key": "images/photos/60d5ec9f2f8e4c3d4c8b4568.jpg"
    }
  ],
  "gender": "male",
  "religion": "islam",
  "occupation": "Software Engineer",
  "isSmoker": false,
  "acceptDifferentReligion": true,
  "about": "I'm a software engineer with 5 years of experience.",
  "psychTestCompleted": true,
  "status": "active"
}
```

## Update Profile

Update the authenticated user's profile.

### Endpoint
```
PUT /profile
```

### Request Body
```typescript
{
  "name"?: string,
  "gender"?: string,
  "religion"?: string,
  "occupation"?: string,
  "isSmoker"?: boolean,
  "acceptDifferentReligion"?: boolean,
  "about"?: string,
  "dateOfBirth"?: string | Date
}
```

### Example Request
```json
{
  "name": "John Doe Updated",
  "occupation": "Senior Software Engineer",
  "about": "I have 6 years of experience now!"
}
```

### Response
```json
{
  "id": "60d5ec9f2f8e4c3d4c8b4567",
  "name": "John Doe Updated",
  "email": "<EMAIL>",
  "phoneNumber": "+6281234567890",
  "image": "https://example.com/profile.jpg",
  "dateOfBirth": "1990-01-01T00:00:00.000Z",
  "photos": [
    {
      "id": "60d5ec9f2f8e4c3d4c8b4568",
      "url": "https://example.com/photo1.jpg",
      "key": "images/photos/60d5ec9f2f8e4c3d4c8b4568.jpg"
    }
  ],
  "gender": "male",
  "religion": "islam",
  "occupation": "Senior Software Engineer",
  "isSmoker": false,
  "acceptDifferentReligion": true,
  "about": "I have 6 years of experience now!",
  "psychTestCompleted": true,
  "status": "active"
}
```

## Upload Profile Image

Upload a new profile image. The image will be automatically resized and converted to a square format.

### Endpoint
```
PUT /profile/image
```

### Request
- Method: `PUT`
- Content-Type: `multipart/form-data`
- Body: `file` (image file)

### Response
```json
{
  "url": "https://example.com/profile/1234567890.jpg",
  "key": "images/profile/1234567890.jpg"
}
```

### Possible Errors
- `400 Bad Request`: If no file is provided or file type is not supported
- `413 Payload Too Large`: If file size exceeds the limit (2MB)

## Upload Photos

Upload multiple photos. Each photo will be automatically resized and converted to a square format.

### Endpoint
```
PUT /profile/photos
```

### Request
- Method: `PUT`
- Content-Type: `multipart/form-data`
- Body: `files` (array of image files, max 5 files)

### Response
```json
[
  {
    "url": "https://example.com/photos/1234567890.jpg",
    "key": "images/photos/1234567890.jpg"
  },
  {
    "url": "https://example.com/photos/0987654321.jpg",
    "key": "images/photos/0987654321.jpg"
  }
]
```

### Possible Errors
- `400 Bad Request`: If no files are provided or file type is not supported
- `413 Payload Too Large`: If any file size exceeds the limit (2MB)

## Delete Photos

Delete multiple photos by their IDs.

### Endpoint
```
DELETE /profile/photos/:ids
```

### Path Parameters
| Parameter | Type   | Required | Description                     |
|-----------|--------|----------|---------------------------------|
| ids       | string | Yes      | Comma-separated list of photo IDs |

### Example Request
```
DELETE /profile/photos/60d5ec9f2f8e4c3d4c8b4568,60d5ec9f2f8e4c3d4c8b4569
```

### Response
```json
{
  "message": "Photos deleted successfully"
}
```

### Possible Errors
- `400 Bad Request`: If no photo IDs are provided
- `404 Not Found`: If any photo ID doesn't exist

## Data Types

### Auth Profile Response
Represents the user's profile information.

```typescript
{
  id: string;                     // User ID
  name: string;                   // User's full name
  email: string;                  // User's email
  phoneNumber: string;            // User's phone number
  image: string;                  // URL to user's profile image
  dateOfBirth: string | Date;     // User's date of birth
  photos: PhotoResponse[];        // Array of user's photos
  gender: string;                 // User's gender
  religion: string;               // User's religion
  occupation: string;             // User's occupation
  isSmoker: boolean;              // Whether the user smokes
  acceptDifferentReligion: boolean; // Whether the user accepts different religions
  about: string;                  // User's bio/description
  psychTestCompleted: boolean;    // Whether the user has completed the personality test
  status: string;                 // User's account status (e.g., 'active', 'inactive')
}
```

### Photo Response
Represents a photo in the user's gallery.

```typescript
{
  id: string;    // Photo ID
  url: string;   // Public URL of the photo
  key: string;   // S3 object key
}
```
