import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PersonalityController } from './personality.controller';
import { PersonalityService } from './services/personality.service';
import { PersonalityTest, PersonalityTestSchema } from './schemas/personality-test.schema';
import { PersonalityQuestion, PersonalityQuestionSchema } from './schemas/question.schema';
import { QuestionService } from './services/question.service';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: PersonalityTest.name, schema: PersonalityTestSchema },
      { name: PersonalityQuestion.name, schema: PersonalityQuestionSchema },
    ]),
    UserModule
  ],
  controllers: [PersonalityController],
  providers: [PersonalityService, QuestionService],
  exports: [PersonalityService, QuestionService],
})
export class PersonalityModule {}
