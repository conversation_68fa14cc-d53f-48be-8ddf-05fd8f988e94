import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ChatMessage } from './chat-message.schema';
import { User } from 'src/user/schemas/user.schema';

export type ChatDocument = Chat & Document;

@Schema({ timestamps: true })
export class Chat {
  @Prop({ type: Types.ObjectId, ref: User.name, required: true })
  user1Id: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: User.name, required: true })
  user2Id: Types.ObjectId;

  @Prop({ type: String, default: null })
  matchId: string;

  @Prop({ type: Types.ObjectId, ref: ChatMessage.name })
  lastMessage: Types.ObjectId;

  @Prop({ type: Boolean, default: false })
  isLocked: boolean;
}

export const ChatSchema = SchemaFactory.createForClass(Chat);

// Create compound index to ensure one userId can only have one isOnline status
ChatSchema.index({ user1Id: 1, user2Id: 1, matchId: 1 }, { unique: true });

