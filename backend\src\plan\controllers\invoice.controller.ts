import { Body, Controller, Get, Param, Post, Request } from '@nestjs/common';
import { InvoiceService } from '../services/invoice.service';
import { IsPublic } from 'src/auth/decorators/is-public.decorator';
import { RequestWithUser } from 'src/common/interfaces/request-with-user.interface';
import { Query } from '@nestjs/common';
import { PaginatedResponseDto, PaginationDto } from 'src/common/dto/pagination.dto';
import { InvoiceResponseDto } from '../dto/invoice.dto';

@Controller('plans/invoices')
export class InvoiceController {
  constructor(
    private readonly invoiceService: InvoiceService
  ) {}

  @Get()
  async getInvoices(
    @Request() req: RequestWithUser,
    @Query() paginationDto: PaginationDto
  ): Promise<PaginatedResponseDto<InvoiceResponseDto>> {
    const { page, limit } = paginationDto;
    const invoices = await this.invoiceService.getUserInvoices(req.user.userId, page, limit);
    const data = invoices.data.map((invoice) => ({
      id: invoice.id,
      userId: invoice.userId,
      planId: invoice.planId,
      items: invoice.items,
      totals: invoice.totals,
      paidDate: invoice.paidDate,
      metadata: invoice.metadata,
      status: invoice.status,
      invoiceCode: invoice.invoiceCode,
      invoiceLink: invoice.invoiceLink,
      paymentLink: invoice.paymentLink,
      paymentToken: invoice.paymentToken,
      createdAt: invoice.createdAt,
      updatedAt: invoice.updatedAt
    }));

    return {
      data,
      totalItems: invoices.totalItems,
      itemsPerPage: invoices.itemsPerPage,
      totalPages: invoices.totalPages,
      currentPage: invoices.currentPage
    };
  }

  @IsPublic()
  @Post('/notification')
  async notification(@Body() body: any) {
    return this.invoiceService.handleNotification(body)
  }

  @Post('/validate')
  async validateInvoice(@Body() body: { invoiceId: string, transactionId: string, orderId: string }) {
    return this.invoiceService.validateInvoice(body.invoiceId, body.transactionId, body.orderId);
  }
}
