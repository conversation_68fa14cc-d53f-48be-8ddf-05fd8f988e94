import {
  Controller,
  Get
} from '@nestjs/common';
import { PlanService } from '../services/plan.service';
import { IsPublic } from 'src/auth/decorators/is-public.decorator';
import { PlanResponseDto } from '../dto/plan-response.dto';
import { PlanDocument } from '../schemas/plan.schema';

@Controller('plans')
export class PlanController {
  constructor(
    private readonly planService: PlanService
  ) {}

  /**
   * Get all active plans
   * @returns Array of active plans
   */
  @Get()
  @IsPublic()
  async findAllPlans(): Promise<PlanResponseDto[]> {
    const plans = await this.planService.findActivePlans();

    return plans.map((plan: PlanDocument) => {
      return {
        id: plan.id,
        code: plan.code,
        name: plan.name,
        description: plan.description,
        price: plan.price,
        interval: plan.interval,
        psychologistConsultations: plan.psychologistConsultations,
        chats: plan.chats,
        features: plan.features,
        status: plan.status,
        sortOrder: plan.sortOrder,
        isPopular: plan.isPopular,
        isDefault: plan.isDefault,
        isHidden: plan.isHidden,
      };
    });
  }
}
