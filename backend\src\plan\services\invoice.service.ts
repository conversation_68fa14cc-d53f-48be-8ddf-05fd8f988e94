import { BadRequestException, forwardRef, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Invoice, InvoiceDocument, InvoiceItem, InvoiceStatus } from '../schemas/invoice.schema';
import { PlanService } from './plan.service';
import { UserService } from 'src/user/user.service';
import { SubscriptionService, TRIAL_DAYS } from './subscription.service';
import { ItemDetails, MidtransService, TransactionStatus } from './midtrans.service';
import { AddonService } from './addon.service';

@Injectable()
export class InvoiceService {
  private readonly logger = new Logger(InvoiceService.name);

  constructor(
    @InjectModel(Invoice.name) private invoiceModel: Model<InvoiceDocument>,
    private readonly planService: PlanService,
    private readonly midtransService: MidtransService,
    private readonly userService: UserService,
    @Inject(forwardRef(() => SubscriptionService)) private readonly subscriptionService: SubscriptionService,
    @Inject(forwardRef(() => AddonService)) private readonly addonService: AddonService
  ) {}

  async createSubscriptionInvoice(userId: string, planId: string) {
    try {
      const plan = await this.planService.findOnePlan(planId);

      const items = [
        {
          id: planId,
          description: `${plan.name} - ${plan.interval.toUpperCase()}`,
          quantity: 1,
          price: plan.price
        }
      ];

      return await this.invoiceModel.create({
        userId,
        planId,
        items,
        totals: [
          {
            type: 'subtotal',
            description: 'Subtotal',
            amount: plan.price
          },
          {
            type: 'total',
            description: 'Total',
            amount: plan.price
          }
        ],
        status: InvoiceStatus.PENDING,
        invoiceCode: new Types.ObjectId().toString()
      });
    } catch (error) {
      this.logger.error(`Error creating subscription invoice: ${error.message}`, error.stack);
      throw error;
    }
  }

  async createTrialSubscriptionInvoice(userId: string, planId: string) {
    try {
      const plan = await this.planService.findOnePlan(planId);

      const items = [
        {
          id: planId,
          description: `${plan.name} (Trial for ${TRIAL_DAYS} days)`,
          quantity: 1,
          price: plan.price
        }
      ];

      return await this.invoiceModel.create({
        userId,
        planId,
        items,
        totals: [
          {
            type: 'subtotal',
            description: 'Subtotal',
            amount: plan.price
          },
          {
            type: 'total',
            description: 'Total',
            amount: plan.price
          }
        ],
        status: InvoiceStatus.PAID,
        invoiceCode: new Types.ObjectId().toString()
      });
    } catch (error) {
      this.logger.error(`Error creating trial invoice: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getUserInvoices(userId: string, page = 1, limit = 10) {
    try {
      const skip = (page - 1) * limit;
      
      const [invoices, totalItems] = await Promise.all([
        this.invoiceModel.find({ userId })
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.invoiceModel.countDocuments({ userId }).exec()
      ]);
      
      const totalPages = Math.ceil(totalItems / limit);
      
      return {
        data: invoices,
        totalItems,
        itemsPerPage: limit,
        totalPages,
        currentPage: page
      };
    } catch (error) {
      this.logger.error(`Error getting user invoices: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getPendingInvoiceForPlan(userId: string, planId: string) {
    try {
      const invoice = await this.invoiceModel.findOne({
        userId,
        planId,
        status: InvoiceStatus.PENDING
      }).sort({ createdAt: -1 }).exec();

      return invoice;
    } catch (error) {
      this.logger.error(`Error getting pending invoice for subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  async generateInvoicePayment(invoiceId: string) {
    try {
      const invoice = await this.invoiceModel.findOne({
        _id: new Types.ObjectId(invoiceId),
        status: InvoiceStatus.PENDING
      }).exec();

      if (!invoice) {
        throw new NotFoundException(`Invoice ${invoiceId} not found`);
      }

      const user = await this.userService.findOne(invoice.userId);
      
      let grossAmount: number = 0;
      let itemDetails: ItemDetails[] = [];
      
      invoice.items.forEach(item => {
        itemDetails.push({
          id: item.id,
          price: item.price,
          quantity: item.quantity,
          name: item.description
        });

        grossAmount += item.price * item.quantity;
      });
      
      // new invoice code
      const invoiceCode = new Types.ObjectId().toString();
      const paymentToken = await this.midtransService.createSnapTransaction({
        transaction_details: {
          order_id: invoiceCode,
          gross_amount: grossAmount
        },
        item_details: itemDetails,
        customer_details: {
          first_name: user.name,
          email: user.email
        }
      });

      invoice.invoiceCode = invoiceCode;
      invoice.paymentToken = paymentToken.token;
      invoice.paymentLink = paymentToken.redirect_url;
      
      await invoice.save();

      return invoice;
    } catch (error) {
      this.logger.error(`Error generating invoice payment: ${error.message}`, error.stack);
      throw error;
    }
  }

  async handleNotification(notification: any) {
    try {
      const statusResponse = await this.midtransService.handleNotification(notification);
      const orderId = statusResponse.order_id;
      const transactionStatus = statusResponse.transaction_status;
      const fraudStatus = statusResponse.fraud_status;

      this.logger.log(`Transaction notification received. Order ID: ${orderId}. Transaction status: ${transactionStatus}. Fraud status: ${fraudStatus}`);
      
      let status: InvoiceStatus = InvoiceStatus.PENDING;
      
      if (transactionStatus === TransactionStatus.CAPTURE) {
        // capture only applies to card transaction, which you need to check for the fraudStatus
        if (fraudStatus === TransactionStatus.CHALLENGE) {
          // set transaction status on your databaase to 'challenge
          status = InvoiceStatus.PENDING;
        } else if (fraudStatus === TransactionStatus.ACCEPT) {
          // set transaction status on your databaase to 'success'
          status = InvoiceStatus.PAID;
        }
      } else if (transactionStatus == TransactionStatus.SETTLEMENT) {
        // set transaction status on your databaase to 'success'
        status = InvoiceStatus.PAID;
      } else if (transactionStatus == TransactionStatus.DENY) {
        // can ignore 'deny', because most of the time it allows payment retries and later can become success
      } else if (transactionStatus == TransactionStatus.CANCEL || transactionStatus == TransactionStatus.EXPIRE) {
        // set transaction status on your databaase to 'failure'
        status = InvoiceStatus.CANCELLED;
      } else if (transactionStatus == TransactionStatus.PENDING) {
        // set transaction status on your databaase to 'pending' / waiting payment
        status = InvoiceStatus.PENDING;
      }

      // update invoice status
      const invoice = await this.invoiceModel.findOneAndUpdate({
        invoiceCode: orderId,
        status: InvoiceStatus.PENDING
      }, {
        status,
        metadata: {
          ...statusResponse
        }
      }, {
        new: true
      });

      // activate subscription
      if (invoice && invoice.status === InvoiceStatus.PAID) {
        // activate subscription
        if (invoice.planId) {
          await this.subscriptionService.activateSubscription(invoice.userId, invoice.planId, invoice.id);
        }

        // activate addons
        if (invoice.purchaseId) {
          await this.addonService.activateAddons(invoice.purchaseId, invoice.id);
        }
        this.logger.log(`Invoice ${orderId} activated subscription`);
      } else {
        this.logger.error(`Invoice ${orderId} not found or not in pending status`);
        return false;
      }

      return true;
    } catch(error) {
      throw new BadRequestException(error.message);
    }
  }

  async setInvoiceStatus(invoiceId: string, status: InvoiceStatus) {
    try {
      const invoice = await this.invoiceModel.findOneAndUpdate({
        _id: new Types.ObjectId(invoiceId)
      }, {
        status
      }, {
        new: true
      });

      return invoice;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async validateInvoice(invoiceId: string, transactionId: string, invoiceCode: string) {
    try {
      const invoice = await this.invoiceModel.findOne({
        _id: new Types.ObjectId(invoiceId),
        invoiceCode
      });

      if (!invoice) {
        throw new NotFoundException(`Invoice ${invoiceId} not found`);
      }

      if (invoice.status !== InvoiceStatus.PENDING) {
        throw new BadRequestException(`Invoice ${invoiceId} is not in pending status`);
      }

      const validationResponse = await this.midtransService.validateTransaction(transactionId);

      if (!validationResponse) {
        throw new BadRequestException(`Invoice ${invoiceId} validation failed`);
      }

      const orderId = validationResponse.order_id;
      const transactionStatus = validationResponse.transaction_status;
      const fraudStatus = validationResponse.fraud_status;

      let status: InvoiceStatus = InvoiceStatus.PENDING;
      
      if (transactionStatus === TransactionStatus.CAPTURE) {
        // capture only applies to card transaction, which you need to check for the fraudStatus
        if (fraudStatus === TransactionStatus.CHALLENGE) {
          // set transaction status on your databaase to 'challenge
          status = InvoiceStatus.PENDING;
        } else if (fraudStatus === TransactionStatus.ACCEPT) {
          // set transaction status on your databaase to 'success'
          status = InvoiceStatus.PAID;
        }
      } else if (transactionStatus == TransactionStatus.SETTLEMENT) {
        // set transaction status on your databaase to 'success'
        status = InvoiceStatus.PAID;
      } else if (transactionStatus == TransactionStatus.DENY) {
        // can ignore 'deny', because most of the time it allows payment retries and later can become success
      } else if (transactionStatus == TransactionStatus.CANCEL || transactionStatus == TransactionStatus.EXPIRE) {
        // set transaction status on your databaase to 'failure'
        status = InvoiceStatus.CANCELLED;
      } else if (transactionStatus == TransactionStatus.PENDING) {
        // set transaction status on your databaase to 'pending' / waiting payment
        status = InvoiceStatus.PENDING;
      }

      invoice.status = status;
      invoice.paidDate = validationResponse.settlement_time ?? new Date();
      invoice.metadata = {
        ...validationResponse
      };

      await invoice.save();

      // activate subscription
      if (invoice && invoice.status === InvoiceStatus.PAID) {
        // activate subscription
        if (invoice.planId) {
          await this.subscriptionService.activateSubscription(invoice.userId, invoice.planId, invoice.id);
        }

        // activate addons
        if (invoice.purchaseId) {
          await this.addonService.activateAddons(invoice.purchaseId, invoice.id);
        }
        
        this.logger.log(`Invoice ${orderId} activated subscription`);
      } else {
        this.logger.error(`Invoice ${orderId} not found or not in pending status`);
      }

      return invoice;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getPendingInvoiceForPurchase(userId: string, purchaseId: string) {
    try {
      const invoice = await this.invoiceModel.findOne({
        userId,
        purchaseId,
        status: InvoiceStatus.PENDING
      }).sort({ createdAt: -1 }).exec();

      return invoice;
    } catch (error) {
      this.logger.error(`Error getting pending invoice for purchase: ${error.message}`, error.stack);
      throw error;
    }
  }

  async createPurchaseInvoice(
    userId: string,
    purchaseId: string,
    items: InvoiceItem[]
  ) {
    try {
      let total = 0;

      items.forEach((item) => {
        total += item.price * item.quantity;
      });

      return await this.invoiceModel.create({
        userId,
        purchaseId,
        items,
        totals: [
          {
            type: 'subtotal',
            description: 'Subtotal',
            amount: total
          },
          {
            type: 'total',
            description: 'Total',
            amount: total
          }
        ],
        status: InvoiceStatus.PENDING,
        invoiceCode: new Types.ObjectId().toString()
      });
    } catch (error) {
      this.logger.error(`Error creating purchase invoice: ${error.message}`, error.stack);
      throw error;
    }
  }
}
