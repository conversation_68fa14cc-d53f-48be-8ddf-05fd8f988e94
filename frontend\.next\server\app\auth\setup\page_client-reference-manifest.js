globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/auth/setup/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/dashboard/dashboard-layout-client.tsx":{"*":{"id":"(ssr)/./src/components/dashboard/dashboard-layout-client.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/chat/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/chat/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/matches/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/matches/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth/login-form.tsx":{"*":{"id":"(ssr)/./src/components/auth/login-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth/register-form.tsx":{"*":{"id":"(ssr)/./src/components/auth/register-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth/otp-verification-form.tsx":{"*":{"id":"(ssr)/./src/components/auth/otp-verification-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/setup/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/setup/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/profile/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/subscription/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/subscription/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"E:\\pairsona\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat\"}],\"variableName\":\"montserrat\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat\"}],\"variableName\":\"montserrat\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\pairsona\\frontend\\src\\styles\\globals.css":{"id":"(app-pages-browser)/./src/styles/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\pairsona\\frontend\\src\\components\\dashboard\\dashboard-layout-client.tsx":{"id":"(app-pages-browser)/./src/components/dashboard/dashboard-layout-client.tsx","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\src\\app\\dashboard\\chat\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/chat/page.tsx","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\pairsona\\frontend\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\src\\app\\dashboard\\matches\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/matches/page.tsx","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\(auth)\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat\"}],\"variableName\":\"montserrat\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\(auth)\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat\"}],\"variableName\":\"montserrat\"}","name":"*","chunks":["app/(auth)/layout","static/chunks/app/(auth)/layout.js"],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\src\\components\\auth\\login-form.tsx":{"id":"(app-pages-browser)/./src/components/auth/login-form.tsx","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\src\\components\\auth\\register-form.tsx":{"id":"(app-pages-browser)/./src/components/auth/register-form.tsx","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\src\\components\\auth\\otp-verification-form.tsx":{"id":"(app-pages-browser)/./src/components/auth/otp-verification-form.tsx","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\src\\app\\auth\\setup\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/setup/page.tsx","name":"*","chunks":["app/auth/setup/page","static/chunks/app/auth/setup/page.js"],"async":false},"E:\\pairsona\\frontend\\src\\app\\dashboard\\profile\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/profile/page.tsx","name":"*","chunks":[],"async":false},"E:\\pairsona\\frontend\\src\\app\\dashboard\\subscription\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/subscription/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"E:\\pairsona\\frontend\\src\\":[],"E:\\pairsona\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"E:\\pairsona\\frontend\\src\\app\\(auth)\\layout":[{"inlined":false,"path":"static/css/app/(auth)/layout.css"}],"E:\\pairsona\\frontend\\src\\app\\auth\\setup\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/styles/globals.css":{"*":{"id":"(rsc)/./src/styles/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard/dashboard-layout-client.tsx":{"*":{"id":"(rsc)/./src/components/dashboard/dashboard-layout-client.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/chat/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/chat/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/matches/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/matches/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth/login-form.tsx":{"*":{"id":"(rsc)/./src/components/auth/login-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth/register-form.tsx":{"*":{"id":"(rsc)/./src/components/auth/register-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth/otp-verification-form.tsx":{"*":{"id":"(rsc)/./src/components/auth/otp-verification-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/setup/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/setup/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/profile/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/subscription/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/subscription/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}