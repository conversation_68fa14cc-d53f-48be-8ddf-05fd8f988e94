{"version": 3, "file": "engine.io.js", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../build/esm/globalThis.browser.js", "../build/esm/util.js", "../build/esm/contrib/parseqs.js", "../build/esm/transport.js", "../build/esm/contrib/yeast.js", "../build/esm/contrib/has-cors.js", "../build/esm/transports/xmlhttprequest.browser.js", "../build/esm/transports/polling.js", "../build/esm/transports/websocket-constructor.browser.js", "../build/esm/transports/websocket.js", "../build/esm/transports/webtransport.js", "../build/esm/transports/index.js", "../build/esm/contrib/parseuri.js", "../build/esm/socket.js", "../build/esm/browser-entrypoint.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data\n            .arrayBuffer()\n            .then(toArray)\n            .then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, encoded => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, encodedPacket => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        }\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else if (state === 2 /* READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        }\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\nexport function createCookieJar() { }\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { createCookieJar, XHR as XMLHttpRequest, } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n        if (this.opts.withCredentials) {\n            this.cookieJar = createCookieJar();\n        }\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, cookieJar: this.cookieJar }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        var _a;\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, true);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(xhr);\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"./websocket-constructor.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        // @ts-ignore\n        if (typeof WebTransport !== \"function\") {\n            return;\n        }\n        // @ts-ignore\n        this.transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        this.transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this.transport.ready.then(() => {\n            this.transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this.writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this.writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this.writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this.transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: Polling,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 2000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { defaultBinaryType } from \"./transports/websocket-constructor.js\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\n            \"polling\",\n            \"websocket\",\n            \"webtransport\",\n        ];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this.upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            this.resetPingTimeout();\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport default (uri, opts) => new Socket(uri, opts);\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "_ref", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "encodePacketToBinary", "packet", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "chars", "lookup", "i", "length", "charCodeAt", "decode", "base64", "bufferLength", "len", "p", "encoded1", "encoded2", "encoded3", "encoded4", "arraybuffer", "bytes", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "packetType", "decoded", "SEPARATOR", "String", "fromCharCode", "encodePayload", "packets", "encodedPackets", "Array", "count", "join", "decodePayload", "encodedPayload", "decodedPacket", "push", "createPacketEncoderStream", "TransformStream", "transform", "controller", "payloadLength", "header", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "TEXT_DECODER", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "createPacketDecoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "Math", "pow", "protocol", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "_callbacks", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "callbacks", "cb", "splice", "emit", "args", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "_len", "attr", "_key", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "BASE64_OVERHEAD", "utf8Length", "ceil", "str", "c", "l", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "TransportError", "_Error", "_inherits", "_super", "_createSuper", "reason", "description", "context", "_this", "_classCallCheck", "_wrapNativeSuper", "Error", "Transport", "_Emitter", "_super2", "_this2", "writable", "_assertThisInitialized", "query", "socket", "_createClass", "value", "onError", "_get", "_getPrototypeOf", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "write", "onOpen", "onData", "onPacket", "details", "pause", "onPause", "createUri", "schema", "undefined", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "alphabet", "map", "seed", "prev", "num", "floor", "yeast", "now", "Date", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "createCookieJar", "empty", "hasXHR2", "xhr", "responseType", "Polling", "_Transport", "polling", "location", "isSSL", "xd", "forceBase64", "withCredentials", "cookieJar", "poll", "total", "doPoll", "_this3", "_this4", "_this5", "doWrite", "uri", "timestampRequests", "timestampParam", "sid", "b64", "request", "_extends", "Request", "_this6", "req", "method", "xhrStatus", "_this7", "pollXhr", "get", "_this8", "_this9", "_a", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "addCookies", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "status", "onLoad", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "terminationEvent", "nextTick", "isPromiseAvailable", "Promise", "resolve", "WebSocket", "MozWebSocket", "usingBrowserWebSocket", "defaultBinaryType", "isReactNative", "navigator", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "_loop", "lastPacket", "WT", "WebTransport", "transport", "transportOptions", "name", "closed", "ready", "createBidirectionalStream", "stream", "decoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "writer", "getWriter", "read", "done", "transports", "websocket", "webtransport", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "query<PERSON><PERSON>", "regx", "names", "$0", "$1", "$2", "Socket", "writeBuffer", "_typeof", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "offlineEventListener", "createTransport", "EIO", "priorWebsocketSuccess", "setTransport", "onDrain", "probe", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "resetPingTimeout", "onHandshake", "JSON", "sendPacket", "code", "filterUpgrades", "getWritablePackets", "shouldCheckPayloadSize", "payloadSize", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IAAMA,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EACzCF,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG;EAC1BA,YAAY,CAAC,OAAO,CAAC,GAAG,GAAG;EAC3BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG;EAC1BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG;EAC1BA,YAAY,CAAC,SAAS,CAAC,GAAG,GAAG;EAC7BA,YAAY,CAAC,SAAS,CAAC,GAAG,GAAG;EAC7BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG;EAC1B,IAAMG,oBAAoB,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAChDD,MAAM,CAACG,IAAI,CAACJ,YAAY,CAAC,CAACK,OAAO,CAAC,UAAAC,GAAG,EAAI;IACrCH,oBAAoB,CAACH,YAAY,CAACM,GAAG,CAAC,CAAC,GAAGA,GAAG;EACjD,CAAC,CAAC;EACF,IAAMC,YAAY,GAAG;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAe,CAAC;;ECX5D,IAAMC,cAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBV,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACH,IAAI,CAAC,KAAK,0BAA2B;EAC5E,IAAMI,uBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU;EAC/D;EACA,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAGC,GAAG,EAAI;IAClB,OAAO,OAAOF,WAAW,CAACC,MAAM,KAAK,UAAU,GACzCD,WAAW,CAACC,MAAM,CAACC,GAAG,CAAC,GACvBA,GAAG,IAAIA,GAAG,CAACC,MAAM,YAAYH,WAAW;EAClD,CAAC;EACD,IAAMI,YAAY,GAAG,SAAfA,YAAYA,CAAAC,IAAA,EAAoBC,cAAc,EAAEC,QAAQ,EAAK;IAAA,IAA3Cf,IAAI,GAAAa,IAAA,CAAJb,IAAI;MAAEC,IAAI,GAAAY,IAAA,CAAJZ,IAAI;IAC9B,IAAIC,cAAc,IAAID,IAAI,YAAYE,IAAI,EAAE;MACxC,IAAIW,cAAc,EAAE;QAChB,OAAOC,QAAQ,CAACd,IAAI,CAAC;OACxB,MACI;QACD,OAAOe,kBAAkB,CAACf,IAAI,EAAEc,QAAQ,CAAC;;KAEhD,MACI,IAAIR,uBAAqB,KACzBN,IAAI,YAAYO,WAAW,IAAIC,MAAM,CAACR,IAAI,CAAC,CAAC,EAAE;MAC/C,IAAIa,cAAc,EAAE;QAChB,OAAOC,QAAQ,CAACd,IAAI,CAAC;OACxB,MACI;QACD,OAAOe,kBAAkB,CAAC,IAAIb,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,EAAEc,QAAQ,CAAC;;;;IAI7D,OAAOA,QAAQ,CAACvB,YAAY,CAACQ,IAAI,CAAC,IAAIC,IAAI,IAAI,EAAE,CAAC,CAAC;EACtD,CAAC;EACD,IAAMe,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIf,IAAI,EAAEc,QAAQ,EAAK;IAC3C,IAAME,UAAU,GAAG,IAAIC,UAAU,EAAE;IACnCD,UAAU,CAACE,MAAM,GAAG,YAAY;MAC5B,IAAMC,OAAO,GAAGH,UAAU,CAACI,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/CP,QAAQ,CAAC,GAAG,IAAIK,OAAO,IAAI,EAAE,CAAC,CAAC;KAClC;IACD,OAAOH,UAAU,CAACM,aAAa,CAACtB,IAAI,CAAC;EACzC,CAAC;EACD,SAASuB,OAAOA,CAACvB,IAAI,EAAE;IACnB,IAAIA,IAAI,YAAYwB,UAAU,EAAE;MAC5B,OAAOxB,IAAI;KACd,MACI,IAAIA,IAAI,YAAYO,WAAW,EAAE;MAClC,OAAO,IAAIiB,UAAU,CAACxB,IAAI,CAAC;KAC9B,MACI;MACD,OAAO,IAAIwB,UAAU,CAACxB,IAAI,CAACU,MAAM,EAAEV,IAAI,CAACyB,UAAU,EAAEzB,IAAI,CAAC0B,UAAU,CAAC;;EAE5E;EACA,IAAIC,YAAY;EACT,SAASC,oBAAoBA,CAACC,MAAM,EAAEf,QAAQ,EAAE;IACnD,IAAIb,cAAc,IAAI4B,MAAM,CAAC7B,IAAI,YAAYE,IAAI,EAAE;MAC/C,OAAO2B,MAAM,CAAC7B,IAAI,CACb8B,WAAW,EAAE,CACbC,IAAI,CAACR,OAAO,CAAC,CACbQ,IAAI,CAACjB,QAAQ,CAAC;KACtB,MACI,IAAIR,uBAAqB,KACzBuB,MAAM,CAAC7B,IAAI,YAAYO,WAAW,IAAIC,MAAM,CAACqB,MAAM,CAAC7B,IAAI,CAAC,CAAC,EAAE;MAC7D,OAAOc,QAAQ,CAACS,OAAO,CAACM,MAAM,CAAC7B,IAAI,CAAC,CAAC;;IAEzCW,YAAY,CAACkB,MAAM,EAAE,KAAK,EAAE,UAAAG,OAAO,EAAI;MACnC,IAAI,CAACL,YAAY,EAAE;QACfA,YAAY,GAAG,IAAIM,WAAW,EAAE;;MAEpCnB,QAAQ,CAACa,YAAY,CAACO,MAAM,CAACF,OAAO,CAAC,CAAC;KACzC,CAAC;EACN;;ECrEA;EACA,IAAMG,KAAK,GAAG,kEAAkE;EAChF;EACA,IAAMC,MAAM,GAAG,OAAOZ,UAAU,KAAK,WAAW,GAAG,EAAE,GAAG,IAAIA,UAAU,CAAC,GAAG,CAAC;EAC3E,KAAK,IAAIa,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,GAAC,EAAE,EAAE;IACnCD,MAAM,CAACD,KAAK,CAACI,UAAU,CAACF,GAAC,CAAC,CAAC,GAAGA,GAAC;EACnC;EAiBO,IAAMG,QAAM,GAAG,SAATA,MAAMA,CAAIC,MAAM,EAAK;IAC9B,IAAIC,YAAY,GAAGD,MAAM,CAACH,MAAM,GAAG,IAAI;MAAEK,GAAG,GAAGF,MAAM,CAACH,MAAM;MAAED,CAAC;MAAEO,CAAC,GAAG,CAAC;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC,QAAQ;IAC9G,IAAIP,MAAM,CAACA,MAAM,CAACH,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MACnCI,YAAY,EAAE;MACd,IAAID,MAAM,CAACA,MAAM,CAACH,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QACnCI,YAAY,EAAE;;;IAGtB,IAAMO,WAAW,GAAG,IAAI1C,WAAW,CAACmC,YAAY,CAAC;MAAEQ,KAAK,GAAG,IAAI1B,UAAU,CAACyB,WAAW,CAAC;IACtF,KAAKZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,GAAG,EAAEN,CAAC,IAAI,CAAC,EAAE;MACzBQ,QAAQ,GAAGT,MAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,CAAC,CAAC;MACvCS,QAAQ,GAAGV,MAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3CU,QAAQ,GAAGX,MAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3CW,QAAQ,GAAGZ,MAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3Ca,KAAK,CAACN,CAAC,EAAE,CAAC,GAAIC,QAAQ,IAAI,CAAC,GAAKC,QAAQ,IAAI,CAAE;MAC9CI,KAAK,CAACN,CAAC,EAAE,CAAC,GAAI,CAACE,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAKC,QAAQ,IAAI,CAAE;MACrDG,KAAK,CAACN,CAAC,EAAE,CAAC,GAAI,CAACG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAKC,QAAQ,GAAG,EAAG;;IAExD,OAAOC,WAAW;EACtB,CAAC;;ECxCD,IAAM3C,qBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU;EACxD,IAAM4C,YAAY,GAAG,SAAfA,YAAYA,CAAIC,aAAa,EAAEC,UAAU,EAAK;IACvD,IAAI,OAAOD,aAAa,KAAK,QAAQ,EAAE;MACnC,OAAO;QACHrD,IAAI,EAAE,SAAS;QACfC,IAAI,EAAEsD,SAAS,CAACF,aAAa,EAAEC,UAAU;OAC5C;;IAEL,IAAMtD,IAAI,GAAGqD,aAAa,CAACG,MAAM,CAAC,CAAC,CAAC;IACpC,IAAIxD,IAAI,KAAK,GAAG,EAAE;MACd,OAAO;QACHA,IAAI,EAAE,SAAS;QACfC,IAAI,EAAEwD,kBAAkB,CAACJ,aAAa,CAACK,SAAS,CAAC,CAAC,CAAC,EAAEJ,UAAU;OAClE;;IAEL,IAAMK,UAAU,GAAGhE,oBAAoB,CAACK,IAAI,CAAC;IAC7C,IAAI,CAAC2D,UAAU,EAAE;MACb,OAAO5D,YAAY;;IAEvB,OAAOsD,aAAa,CAACd,MAAM,GAAG,CAAC,GACzB;MACEvC,IAAI,EAAEL,oBAAoB,CAACK,IAAI,CAAC;MAChCC,IAAI,EAAEoD,aAAa,CAACK,SAAS,CAAC,CAAC;KAClC,GACC;MACE1D,IAAI,EAAEL,oBAAoB,CAACK,IAAI;KAClC;EACT,CAAC;EACD,IAAMyD,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIxD,IAAI,EAAEqD,UAAU,EAAK;IAC7C,IAAI/C,qBAAqB,EAAE;MACvB,IAAMqD,OAAO,GAAGnB,QAAM,CAACxC,IAAI,CAAC;MAC5B,OAAOsD,SAAS,CAACK,OAAO,EAAEN,UAAU,CAAC;KACxC,MACI;MACD,OAAO;QAAEZ,MAAM,EAAE,IAAI;QAAEzC,IAAI,EAAJA;OAAM,CAAC;;EAEtC,CAAC;;EACD,IAAMsD,SAAS,GAAG,SAAZA,SAASA,CAAItD,IAAI,EAAEqD,UAAU,EAAK;IACpC,QAAQA,UAAU;MACd,KAAK,MAAM;QACP,IAAIrD,IAAI,YAAYE,IAAI,EAAE;;UAEtB,OAAOF,IAAI;SACd,MACI;;UAED,OAAO,IAAIE,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC;;MAE/B,KAAK,aAAa;MAClB;QACI,IAAIA,IAAI,YAAYO,WAAW,EAAE;;UAE7B,OAAOP,IAAI;SACd,MACI;;UAED,OAAOA,IAAI,CAACU,MAAM;;;EAGlC,CAAC;;EC1DD,IAAMkD,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,CAAC,CAAC;EAC1C,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,OAAO,EAAElD,QAAQ,EAAK;;IAEzC,IAAMwB,MAAM,GAAG0B,OAAO,CAAC1B,MAAM;IAC7B,IAAM2B,cAAc,GAAG,IAAIC,KAAK,CAAC5B,MAAM,CAAC;IACxC,IAAI6B,KAAK,GAAG,CAAC;IACbH,OAAO,CAACpE,OAAO,CAAC,UAACiC,MAAM,EAAEQ,CAAC,EAAK;;MAE3B1B,YAAY,CAACkB,MAAM,EAAE,KAAK,EAAE,UAAAuB,aAAa,EAAI;QACzCa,cAAc,CAAC5B,CAAC,CAAC,GAAGe,aAAa;QACjC,IAAI,EAAEe,KAAK,KAAK7B,MAAM,EAAE;UACpBxB,QAAQ,CAACmD,cAAc,CAACG,IAAI,CAACR,SAAS,CAAC,CAAC;;OAE/C,CAAC;KACL,CAAC;EACN,CAAC;EACD,IAAMS,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,cAAc,EAAEjB,UAAU,EAAK;IAClD,IAAMY,cAAc,GAAGK,cAAc,CAACjD,KAAK,CAACuC,SAAS,CAAC;IACtD,IAAMI,OAAO,GAAG,EAAE;IAClB,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,cAAc,CAAC3B,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAMkC,aAAa,GAAGpB,YAAY,CAACc,cAAc,CAAC5B,CAAC,CAAC,EAAEgB,UAAU,CAAC;MACjEW,OAAO,CAACQ,IAAI,CAACD,aAAa,CAAC;MAC3B,IAAIA,aAAa,CAACxE,IAAI,KAAK,OAAO,EAAE;QAChC;;;IAGR,OAAOiE,OAAO;EAClB,CAAC;EACM,SAASS,yBAAyBA,GAAG;IACxC,OAAO,IAAIC,eAAe,CAAC;MACvBC,SAAS,WAAAA,UAAC9C,MAAM,EAAE+C,UAAU,EAAE;QAC1BhD,oBAAoB,CAACC,MAAM,EAAE,UAAAuB,aAAa,EAAI;UAC1C,IAAMyB,aAAa,GAAGzB,aAAa,CAACd,MAAM;UAC1C,IAAIwC,MAAM;;UAEV,IAAID,aAAa,GAAG,GAAG,EAAE;YACrBC,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC;YAC1B,IAAIuD,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC,CAACsE,QAAQ,CAAC,CAAC,EAAEH,aAAa,CAAC;WACzD,MACI,IAAIA,aAAa,GAAG,KAAK,EAAE;YAC5BC,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC;YAC1B,IAAMyD,IAAI,GAAG,IAAIF,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC;YACxCuE,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YACrBC,IAAI,CAACC,SAAS,CAAC,CAAC,EAAEL,aAAa,CAAC;WACnC,MACI;YACDC,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC;YAC1B,IAAMyD,KAAI,GAAG,IAAIF,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC;YACxCuE,KAAI,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YACrBC,KAAI,CAACE,YAAY,CAAC,CAAC,EAAEC,MAAM,CAACP,aAAa,CAAC,CAAC;;;UAG/C,IAAIhD,MAAM,CAAC7B,IAAI,IAAI,OAAO6B,MAAM,CAAC7B,IAAI,KAAK,QAAQ,EAAE;YAChD8E,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI;;UAErBF,UAAU,CAACS,OAAO,CAACP,MAAM,CAAC;UAC1BF,UAAU,CAACS,OAAO,CAACjC,aAAa,CAAC;SACpC,CAAC;;KAET,CAAC;EACN;EACA,IAAIkC,YAAY;EAChB,SAASC,WAAWA,CAACC,MAAM,EAAE;IACzB,OAAOA,MAAM,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,KAAK;MAAA,OAAKD,GAAG,GAAGC,KAAK,CAACrD,MAAM;OAAE,CAAC,CAAC;EAC/D;EACA,SAASsD,YAAYA,CAACJ,MAAM,EAAEK,IAAI,EAAE;IAChC,IAAIL,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,KAAKuD,IAAI,EAAE;MAC3B,OAAOL,MAAM,CAACM,KAAK,EAAE;;IAEzB,IAAMpF,MAAM,GAAG,IAAIc,UAAU,CAACqE,IAAI,CAAC;IACnC,IAAIE,CAAC,GAAG,CAAC;IACT,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,IAAI,EAAExD,CAAC,EAAE,EAAE;MAC3B3B,MAAM,CAAC2B,CAAC,CAAC,GAAGmD,MAAM,CAAC,CAAC,CAAC,CAACO,CAAC,EAAE,CAAC;MAC1B,IAAIA,CAAC,KAAKP,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,EAAE;QACxBkD,MAAM,CAACM,KAAK,EAAE;QACdC,CAAC,GAAG,CAAC;;;IAGb,IAAIP,MAAM,CAAClD,MAAM,IAAIyD,CAAC,GAAGP,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,EAAE;MACvCkD,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACD,CAAC,CAAC;;IAElC,OAAOrF,MAAM;EACjB;EACO,SAASuF,yBAAyBA,CAACC,UAAU,EAAE7C,UAAU,EAAE;IAC9D,IAAI,CAACiC,YAAY,EAAE;MACfA,YAAY,GAAG,IAAIa,WAAW,EAAE;;IAEpC,IAAMX,MAAM,GAAG,EAAE;IACjB,IAAIY,KAAK,GAAG,CAAC;IACb,IAAIC,cAAc,GAAG,CAAC,CAAC;IACvB,IAAIC,QAAQ,GAAG,KAAK;IACpB,OAAO,IAAI5B,eAAe,CAAC;MACvBC,SAAS,WAAAA,UAACgB,KAAK,EAAEf,UAAU,EAAE;QACzBY,MAAM,CAAChB,IAAI,CAACmB,KAAK,CAAC;QAClB,OAAO,IAAI,EAAE;UACT,IAAIS,KAAK,KAAK,CAAC,oBAAoB;YAC/B,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;cACzB;;YAEJ,IAAMV,MAAM,GAAGc,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC;YACtCc,QAAQ,GAAG,CAACxB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI;YACtCuB,cAAc,GAAGvB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;YACjC,IAAIuB,cAAc,GAAG,GAAG,EAAE;cACtBD,KAAK,GAAG,CAAC;aACZ,MACI,IAAIC,cAAc,KAAK,GAAG,EAAE;cAC7BD,KAAK,GAAG,CAAC;aACZ,MACI;cACDA,KAAK,GAAG,CAAC;;WAEhB,MACI,IAAIA,KAAK,KAAK,CAAC,gCAAgC;YAChD,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;cACzB;;YAEJ,IAAMe,WAAW,GAAGX,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC;YAC3Ca,cAAc,GAAG,IAAItB,QAAQ,CAACwB,WAAW,CAAC7F,MAAM,EAAE6F,WAAW,CAAC9E,UAAU,EAAE8E,WAAW,CAACjE,MAAM,CAAC,CAACkE,SAAS,CAAC,CAAC,CAAC;YAC1GJ,KAAK,GAAG,CAAC;WACZ,MACI,IAAIA,KAAK,KAAK,CAAC,gCAAgC;YAChD,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;cACzB;;YAEJ,IAAMe,YAAW,GAAGX,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC;YAC3C,IAAMP,IAAI,GAAG,IAAIF,QAAQ,CAACwB,YAAW,CAAC7F,MAAM,EAAE6F,YAAW,CAAC9E,UAAU,EAAE8E,YAAW,CAACjE,MAAM,CAAC;YACzF,IAAMmE,CAAC,GAAGxB,IAAI,CAACyB,SAAS,CAAC,CAAC,CAAC;YAC3B,IAAID,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE;;cAE9BhC,UAAU,CAACS,OAAO,CAACvF,YAAY,CAAC;cAChC;;YAEJuG,cAAc,GAAGI,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG3B,IAAI,CAACyB,SAAS,CAAC,CAAC,CAAC;YACxDN,KAAK,GAAG,CAAC;WACZ,MACI;YACD,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAGa,cAAc,EAAE;cACtC;;YAEJ,IAAMrG,IAAI,GAAG4F,YAAY,CAACJ,MAAM,EAAEa,cAAc,CAAC;YACjDzB,UAAU,CAACS,OAAO,CAAClC,YAAY,CAACmD,QAAQ,GAAGtG,IAAI,GAAGsF,YAAY,CAAC9C,MAAM,CAACxC,IAAI,CAAC,EAAEqD,UAAU,CAAC,CAAC;YACzF+C,KAAK,GAAG,CAAC;;;UAEb,IAAIC,cAAc,KAAK,CAAC,IAAIA,cAAc,GAAGH,UAAU,EAAE;YACrDtB,UAAU,CAACS,OAAO,CAACvF,YAAY,CAAC;YAChC;;;;KAIf,CAAC;EACN;EACO,IAAM+G,QAAQ,GAAG,CAAC;;EC1JzB;EACA;EACA;EACA;EACA;;EAEO,SAASC,OAAOA,CAACrG,GAAG,EAAE;IAC3B,IAAIA,GAAG,EAAE,OAAOsG,KAAK,CAACtG,GAAG,CAAC;EAC5B;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,SAASsG,KAAKA,CAACtG,GAAG,EAAE;IAClB,KAAK,IAAIZ,GAAG,IAAIiH,OAAO,CAAC3G,SAAS,EAAE;MACjCM,GAAG,CAACZ,GAAG,CAAC,GAAGiH,OAAO,CAAC3G,SAAS,CAACN,GAAG,CAAC;;IAEnC,OAAOY,GAAG;EACZ;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAqG,OAAO,CAAC3G,SAAS,CAAC6G,EAAE,GACpBF,OAAO,CAAC3G,SAAS,CAAC8G,gBAAgB,GAAG,UAASC,KAAK,EAAEC,EAAE,EAAC;IACtD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE;IACvC,CAAC,IAAI,CAACA,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,GAAG,IAAI,CAACE,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,IAAI,EAAE,EAC/D1C,IAAI,CAAC2C,EAAE,CAAC;IACX,OAAO,IAAI;EACb,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAL,OAAO,CAAC3G,SAAS,CAACkH,IAAI,GAAG,UAASH,KAAK,EAAEC,EAAE,EAAC;IAC1C,SAASH,EAAEA,GAAG;MACZ,IAAI,CAACM,GAAG,CAACJ,KAAK,EAAEF,EAAE,CAAC;MACnBG,EAAE,CAACI,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;;IAG3BR,EAAE,CAACG,EAAE,GAAGA,EAAE;IACV,IAAI,CAACH,EAAE,CAACE,KAAK,EAAEF,EAAE,CAAC;IAClB,OAAO,IAAI;EACb,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAF,OAAO,CAAC3G,SAAS,CAACmH,GAAG,GACrBR,OAAO,CAAC3G,SAAS,CAACsH,cAAc,GAChCX,OAAO,CAAC3G,SAAS,CAACuH,kBAAkB,GACpCZ,OAAO,CAAC3G,SAAS,CAACwH,mBAAmB,GAAG,UAAST,KAAK,EAAEC,EAAE,EAAC;IACzD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE;;;IAGvC,IAAI,CAAC,IAAII,SAAS,CAAClF,MAAM,EAAE;MACzB,IAAI,CAAC8E,UAAU,GAAG,EAAE;MACpB,OAAO,IAAI;;;;IAIb,IAAIQ,SAAS,GAAG,IAAI,CAACR,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC;IAC5C,IAAI,CAACU,SAAS,EAAE,OAAO,IAAI;;;IAG3B,IAAI,CAAC,IAAIJ,SAAS,CAAClF,MAAM,EAAE;MACzB,OAAO,IAAI,CAAC8E,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC;MACnC,OAAO,IAAI;;;;IAIb,IAAIW,EAAE;IACN,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuF,SAAS,CAACtF,MAAM,EAAED,CAAC,EAAE,EAAE;MACzCwF,EAAE,GAAGD,SAAS,CAACvF,CAAC,CAAC;MACjB,IAAIwF,EAAE,KAAKV,EAAE,IAAIU,EAAE,CAACV,EAAE,KAAKA,EAAE,EAAE;QAC7BS,SAAS,CAACE,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC;QACtB;;;;;;IAMJ,IAAIuF,SAAS,CAACtF,MAAM,KAAK,CAAC,EAAE;MAC1B,OAAO,IAAI,CAAC8E,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC;;IAGrC,OAAO,IAAI;EACb,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAJ,OAAO,CAAC3G,SAAS,CAAC4H,IAAI,GAAG,UAASb,KAAK,EAAC;IACtC,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE;IAEvC,IAAIY,IAAI,GAAG,IAAI9D,KAAK,CAACsD,SAAS,CAAClF,MAAM,GAAG,CAAC,CAAC;MACtCsF,SAAS,GAAG,IAAI,CAACR,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC;IAE5C,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,SAAS,CAAClF,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC2F,IAAI,CAAC3F,CAAC,GAAG,CAAC,CAAC,GAAGmF,SAAS,CAACnF,CAAC,CAAC;;IAG5B,IAAIuF,SAAS,EAAE;MACbA,SAAS,GAAGA,SAAS,CAAC5B,KAAK,CAAC,CAAC,CAAC;MAC9B,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAGiF,SAAS,CAACtF,MAAM,EAAED,CAAC,GAAGM,GAAG,EAAE,EAAEN,CAAC,EAAE;QACpDuF,SAAS,CAACvF,CAAC,CAAC,CAACkF,KAAK,CAAC,IAAI,EAAES,IAAI,CAAC;;;IAIlC,OAAO,IAAI;EACb,CAAC;;EAED;EACAlB,OAAO,CAAC3G,SAAS,CAAC8H,YAAY,GAAGnB,OAAO,CAAC3G,SAAS,CAAC4H,IAAI;;EAEvD;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAjB,OAAO,CAAC3G,SAAS,CAAC+H,SAAS,GAAG,UAAShB,KAAK,EAAC;IAC3C,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE;IACvC,OAAO,IAAI,CAACA,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,IAAI,EAAE;EAC3C,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAJ,OAAO,CAAC3G,SAAS,CAACgI,YAAY,GAAG,UAASjB,KAAK,EAAC;IAC9C,OAAO,CAAC,CAAE,IAAI,CAACgB,SAAS,CAAChB,KAAK,CAAC,CAAC5E,MAAM;EACxC,CAAC;;ECxKM,IAAM8F,cAAc,GAAI,YAAM;IACjC,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;MAC7B,OAAOA,IAAI;KACd,MACI,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;MACpC,OAAOA,MAAM;KAChB,MACI;MACD,OAAOC,QAAQ,CAAC,aAAa,CAAC,EAAE;;EAExC,CAAC,EAAG;;ECTG,SAASC,IAAIA,CAAC/H,GAAG,EAAW;IAAA,SAAAgI,IAAA,GAAAjB,SAAA,CAAAlF,MAAA,EAANoG,IAAI,OAAAxE,KAAA,CAAAuE,IAAA,OAAAA,IAAA,WAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;MAAJD,IAAI,CAAAC,IAAA,QAAAnB,SAAA,CAAAmB,IAAA;;IAC7B,OAAOD,IAAI,CAACjD,MAAM,CAAC,UAACC,GAAG,EAAEkD,CAAC,EAAK;MAC3B,IAAInI,GAAG,CAACoI,cAAc,CAACD,CAAC,CAAC,EAAE;QACvBlD,GAAG,CAACkD,CAAC,CAAC,GAAGnI,GAAG,CAACmI,CAAC,CAAC;;MAEnB,OAAOlD,GAAG;KACb,EAAE,EAAE,CAAC;EACV;EACA;EACA,IAAMoD,kBAAkB,GAAGC,cAAU,CAACC,UAAU;EAChD,IAAMC,oBAAoB,GAAGF,cAAU,CAACG,YAAY;EAC7C,SAASC,qBAAqBA,CAAC1I,GAAG,EAAE2I,IAAI,EAAE;IAC7C,IAAIA,IAAI,CAACC,eAAe,EAAE;MACtB5I,GAAG,CAAC6I,YAAY,GAAGR,kBAAkB,CAACS,IAAI,CAACR,cAAU,CAAC;MACtDtI,GAAG,CAAC+I,cAAc,GAAGP,oBAAoB,CAACM,IAAI,CAACR,cAAU,CAAC;KAC7D,MACI;MACDtI,GAAG,CAAC6I,YAAY,GAAGP,cAAU,CAACC,UAAU,CAACO,IAAI,CAACR,cAAU,CAAC;MACzDtI,GAAG,CAAC+I,cAAc,GAAGT,cAAU,CAACG,YAAY,CAACK,IAAI,CAACR,cAAU,CAAC;;EAErE;EACA;EACA,IAAMU,eAAe,GAAG,IAAI;EAC5B;EACO,SAAS/H,UAAUA,CAACjB,GAAG,EAAE;IAC5B,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACzB,OAAOiJ,UAAU,CAACjJ,GAAG,CAAC;;;IAG1B,OAAOkG,IAAI,CAACgD,IAAI,CAAC,CAAClJ,GAAG,CAACiB,UAAU,IAAIjB,GAAG,CAACoF,IAAI,IAAI4D,eAAe,CAAC;EACpE;EACA,SAASC,UAAUA,CAACE,GAAG,EAAE;IACrB,IAAIC,CAAC,GAAG,CAAC;MAAEvH,MAAM,GAAG,CAAC;IACrB,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEyH,CAAC,GAAGF,GAAG,CAACtH,MAAM,EAAED,CAAC,GAAGyH,CAAC,EAAEzH,CAAC,EAAE,EAAE;MACxCwH,CAAC,GAAGD,GAAG,CAACrH,UAAU,CAACF,CAAC,CAAC;MACrB,IAAIwH,CAAC,GAAG,IAAI,EAAE;QACVvH,MAAM,IAAI,CAAC;OACd,MACI,IAAIuH,CAAC,GAAG,KAAK,EAAE;QAChBvH,MAAM,IAAI,CAAC;OACd,MACI,IAAIuH,CAAC,GAAG,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;QAChCvH,MAAM,IAAI,CAAC;OACd,MACI;QACDD,CAAC,EAAE;QACHC,MAAM,IAAI,CAAC;;;IAGnB,OAAOA,MAAM;EACjB;;ECnDA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASJ,QAAMA,CAACzB,GAAG,EAAE;IACxB,IAAImJ,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIvH,CAAC,IAAI5B,GAAG,EAAE;MACf,IAAIA,GAAG,CAACoI,cAAc,CAACxG,CAAC,CAAC,EAAE;QACvB,IAAIuH,GAAG,CAACtH,MAAM,EACVsH,GAAG,IAAI,GAAG;QACdA,GAAG,IAAIG,kBAAkB,CAAC1H,CAAC,CAAC,GAAG,GAAG,GAAG0H,kBAAkB,CAACtJ,GAAG,CAAC4B,CAAC,CAAC,CAAC;;;IAGvE,OAAOuH,GAAG;EACd;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASpH,MAAMA,CAACwH,EAAE,EAAE;IACvB,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,KAAK,GAAGF,EAAE,CAAC3I,KAAK,CAAC,GAAG,CAAC;IACzB,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEyH,CAAC,GAAGI,KAAK,CAAC5H,MAAM,EAAED,CAAC,GAAGyH,CAAC,EAAEzH,CAAC,EAAE,EAAE;MAC1C,IAAI8H,IAAI,GAAGD,KAAK,CAAC7H,CAAC,CAAC,CAAChB,KAAK,CAAC,GAAG,CAAC;MAC9B4I,GAAG,CAACG,kBAAkB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,kBAAkB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC;;IAElE,OAAOF,GAAG;EACd;;MC7BaI,cAAc,0BAAAC,MAAA;IAAAC,SAAA,CAAAF,cAAA,EAAAC,MAAA;IAAA,IAAAE,MAAA,GAAAC,YAAA,CAAAJ,cAAA;IACvB,SAAAA,eAAYK,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;MAAA,IAAAC,KAAA;MAAAC,eAAA,OAAAT,cAAA;MACtCQ,KAAA,GAAAL,MAAA,CAAAnK,IAAA,OAAMqK,MAAM;MACZG,KAAA,CAAKF,WAAW,GAAGA,WAAW;MAC9BE,KAAA,CAAKD,OAAO,GAAGA,OAAO;MACtBC,KAAA,CAAK9K,IAAI,GAAG,gBAAgB;MAAC,OAAA8K,KAAA;;IAChC,OAAAR,cAAA;EAAA,gBAAAU,gBAAA,CAN+BC,KAAK;MAQ5BC,SAAS,0BAAAC,QAAA;IAAAX,SAAA,CAAAU,SAAA,EAAAC,QAAA;IAAA,IAAAC,OAAA,GAAAV,YAAA,CAAAQ,SAAA;;EAEtB;EACA;EACA;EACA;EACA;IACI,SAAAA,UAAY7B,IAAI,EAAE;MAAA,IAAAgC,MAAA;MAAAN,eAAA,OAAAG,SAAA;MACdG,MAAA,GAAAD,OAAA,CAAA9K,IAAA;MACA+K,MAAA,CAAKC,QAAQ,GAAG,KAAK;MACrBlC,qBAAqB,CAAAmC,sBAAA,CAAAF,MAAA,GAAOhC,IAAI,CAAC;MACjCgC,MAAA,CAAKhC,IAAI,GAAGA,IAAI;MAChBgC,MAAA,CAAKG,KAAK,GAAGnC,IAAI,CAACmC,KAAK;MACvBH,MAAA,CAAKI,MAAM,GAAGpC,IAAI,CAACoC,MAAM;MAAC,OAAAJ,MAAA;;;EAGlC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IARIK,YAAA,CAAAR,SAAA;MAAApL,GAAA;MAAA6L,KAAA,WAAAC,QASQjB,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;QAClCgB,IAAA,CAAAC,eAAA,CAAAZ,SAAA,CAAA9K,SAAA,yBAAAE,IAAA,OAAmB,OAAO,EAAE,IAAIgK,cAAc,CAACK,MAAM,EAAEC,WAAW,EAAEC,OAAO,CAAC;QAC5E,OAAO,IAAI;;;EAGnB;EACA;;MAFI/K,GAAA;MAAA6L,KAAA,WAAAI,OAGO;QACH,IAAI,CAACC,UAAU,GAAG,SAAS;QAC3B,IAAI,CAACC,MAAM,EAAE;QACb,OAAO,IAAI;;;EAGnB;EACA;;MAFInM,GAAA;MAAA6L,KAAA,WAAAO,QAGQ;QACJ,IAAI,IAAI,CAACF,UAAU,KAAK,SAAS,IAAI,IAAI,CAACA,UAAU,KAAK,MAAM,EAAE;UAC7D,IAAI,CAACG,OAAO,EAAE;UACd,IAAI,CAACC,OAAO,EAAE;;QAElB,OAAO,IAAI;;;EAGnB;EACA;EACA;EACA;;MAJItM,GAAA;MAAA6L,KAAA,WAAAU,KAKKpI,OAAO,EAAE;QACV,IAAI,IAAI,CAAC+H,UAAU,KAAK,MAAM,EAAE;UAC5B,IAAI,CAACM,KAAK,CAACrI,OAAO,CAAC;;;;EAO/B;EACA;EACA;EACA;;MAJInE,GAAA;MAAA6L,KAAA,WAAAY,SAKS;QACL,IAAI,CAACP,UAAU,GAAG,MAAM;QACxB,IAAI,CAACV,QAAQ,GAAG,IAAI;QACpBO,IAAA,CAAAC,eAAA,CAAAZ,SAAA,CAAA9K,SAAA,yBAAAE,IAAA,OAAmB,MAAM;;;EAGjC;EACA;EACA;EACA;EACA;;MALIR,GAAA;MAAA6L,KAAA,WAAAa,OAMOvM,IAAI,EAAE;QACT,IAAM6B,MAAM,GAAGsB,YAAY,CAACnD,IAAI,EAAE,IAAI,CAACwL,MAAM,CAACnI,UAAU,CAAC;QACzD,IAAI,CAACmJ,QAAQ,CAAC3K,MAAM,CAAC;;;EAG7B;EACA;EACA;EACA;;MAJIhC,GAAA;MAAA6L,KAAA,WAAAc,SAKS3K,MAAM,EAAE;QACb+J,IAAA,CAAAC,eAAA,CAAAZ,SAAA,CAAA9K,SAAA,yBAAAE,IAAA,OAAmB,QAAQ,EAAEwB,MAAM;;;EAG3C;EACA;EACA;EACA;;MAJIhC,GAAA;MAAA6L,KAAA,WAAAS,QAKQM,OAAO,EAAE;QACb,IAAI,CAACV,UAAU,GAAG,QAAQ;QAC1BH,IAAA,CAAAC,eAAA,CAAAZ,SAAA,CAAA9K,SAAA,yBAAAE,IAAA,OAAmB,OAAO,EAAEoM,OAAO;;;EAG3C;EACA;EACA;EACA;;MAJI5M,GAAA;MAAA6L,KAAA,WAAAgB,MAKMC,OAAO,EAAE;;MAAG9M,GAAA;MAAA6L,KAAA,WAAAkB,UACRC,MAAM,EAAc;QAAA,IAAZtB,KAAK,GAAA/D,SAAA,CAAAlF,MAAA,QAAAkF,SAAA,QAAAsF,SAAA,GAAAtF,SAAA,MAAG,EAAE;QACxB,OAAQqF,MAAM,GACV,KAAK,GACL,IAAI,CAACE,SAAS,EAAE,GAChB,IAAI,CAACC,KAAK,EAAE,GACZ,IAAI,CAAC5D,IAAI,CAAC6D,IAAI,GACd,IAAI,CAACC,MAAM,CAAC3B,KAAK,CAAC;;;MACzB1L,GAAA;MAAA6L,KAAA,WAAAqB,YACW;QACR,IAAMI,QAAQ,GAAG,IAAI,CAAC/D,IAAI,CAAC+D,QAAQ;QACnC,OAAOA,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAGD,QAAQ,GAAG,GAAG,GAAGA,QAAQ,GAAG,GAAG;;;MACxEtN,GAAA;MAAA6L,KAAA,WAAAsB,QACO;QACJ,IAAI,IAAI,CAAC5D,IAAI,CAACiE,IAAI,KACZ,IAAI,CAACjE,IAAI,CAACkE,MAAM,IAAIC,MAAM,CAAC,IAAI,CAACnE,IAAI,CAACiE,IAAI,KAAK,GAAG,CAAC,IAC/C,CAAC,IAAI,CAACjE,IAAI,CAACkE,MAAM,IAAIC,MAAM,CAAC,IAAI,CAACnE,IAAI,CAACiE,IAAI,CAAC,KAAK,EAAG,CAAC,EAAE;UAC3D,OAAO,GAAG,GAAG,IAAI,CAACjE,IAAI,CAACiE,IAAI;SAC9B,MACI;UACD,OAAO,EAAE;;;;MAEhBxN,GAAA;MAAA6L,KAAA,WAAAwB,OACM3B,KAAK,EAAE;QACV,IAAMiC,YAAY,GAAGtL,QAAM,CAACqJ,KAAK,CAAC;QAClC,OAAOiC,YAAY,CAAClL,MAAM,GAAG,GAAG,GAAGkL,YAAY,GAAG,EAAE;;;IACvD,OAAAvC,SAAA;EAAA,EA/H0BnE,OAAO;;ECZtC;;EAEA,IAAM2G,QAAQ,GAAG,kEAAkE,CAACpM,KAAK,CAAC,EAAE,CAAC;IAAEiB,MAAM,GAAG,EAAE;IAAEoL,GAAG,GAAG,EAAE;EACpH,IAAIC,IAAI,GAAG,CAAC;IAAEtL,CAAC,GAAG,CAAC;IAAEuL,IAAI;EACzB;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS1L,MAAMA,CAAC2L,GAAG,EAAE;IACxB,IAAI7L,OAAO,GAAG,EAAE;IAChB,GAAG;MACCA,OAAO,GAAGyL,QAAQ,CAACI,GAAG,GAAGvL,MAAM,CAAC,GAAGN,OAAO;MAC1C6L,GAAG,GAAGlH,IAAI,CAACmH,KAAK,CAACD,GAAG,GAAGvL,MAAM,CAAC;KACjC,QAAQuL,GAAG,GAAG,CAAC;IAChB,OAAO7L,OAAO;EAClB;EAeA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS+L,KAAKA,GAAG;IACpB,IAAMC,GAAG,GAAG9L,MAAM,CAAC,CAAC,IAAI+L,IAAI,EAAE,CAAC;IAC/B,IAAID,GAAG,KAAKJ,IAAI,EACZ,OAAOD,IAAI,GAAG,CAAC,EAAEC,IAAI,GAAGI,GAAG;IAC/B,OAAOA,GAAG,GAAG,GAAG,GAAG9L,MAAM,CAACyL,IAAI,EAAE,CAAC;EACrC;EACA;EACA;EACA;EACA,OAAOtL,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE;IAClBqL,GAAG,CAACD,QAAQ,CAACpL,CAAC,CAAC,CAAC,GAAGA,CAAC;EAAC;;ECjDzB;EACA,IAAIqJ,KAAK,GAAG,KAAK;EACjB,IAAI;IACAA,KAAK,GAAG,OAAOwC,cAAc,KAAK,WAAW,IACzC,iBAAiB,IAAI,IAAIA,cAAc,EAAE;EACjD,CAAC,CACD,OAAOC,GAAG,EAAE;;;EAER;EAEG,IAAMC,OAAO,GAAG1C,KAAK;;ECV5B;EAGO,SAAS2C,GAAGA,CAACjF,IAAI,EAAE;IACtB,IAAMkF,OAAO,GAAGlF,IAAI,CAACkF,OAAO;;IAE5B,IAAI;MACA,IAAI,WAAW,KAAK,OAAOJ,cAAc,KAAK,CAACI,OAAO,IAAIF,OAAO,CAAC,EAAE;QAChE,OAAO,IAAIF,cAAc,EAAE;;KAElC,CACD,OAAOK,CAAC,EAAE;IACV,IAAI,CAACD,OAAO,EAAE;MACV,IAAI;QACA,OAAO,IAAIvF,cAAU,CAAC,CAAC,QAAQ,CAAC,CAACyF,MAAM,CAAC,QAAQ,CAAC,CAACpK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC;OACpF,CACD,OAAOmK,CAAC,EAAE;;EAElB;EACO,SAASE,eAAeA,GAAG;;ECZlC,SAASC,KAAKA,GAAG;EACjB,IAAMC,OAAO,GAAI,YAAY;IACzB,IAAMC,GAAG,GAAG,IAAIV,GAAc,CAAC;MAC3BI,OAAO,EAAE;KACZ,CAAC;IACF,OAAO,IAAI,IAAIM,GAAG,CAACC,YAAY;EACnC,CAAC,EAAG;MACSC,OAAO,0BAAAC,UAAA;IAAAxE,SAAA,CAAAuE,OAAA,EAAAC,UAAA;IAAA,IAAAvE,MAAA,GAAAC,YAAA,CAAAqE,OAAA;;EAEpB;EACA;EACA;EACA;EACA;IACI,SAAAA,QAAY1F,IAAI,EAAE;MAAA,IAAAyB,KAAA;MAAAC,eAAA,OAAAgE,OAAA;MACdjE,KAAA,GAAAL,MAAA,CAAAnK,IAAA,OAAM+I,IAAI;MACVyB,KAAA,CAAKmE,OAAO,GAAG,KAAK;MACpB,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;QACjC,IAAMC,KAAK,GAAG,QAAQ,KAAKD,QAAQ,CAACpI,QAAQ;QAC5C,IAAIwG,IAAI,GAAG4B,QAAQ,CAAC5B,IAAI;;QAExB,IAAI,CAACA,IAAI,EAAE;UACPA,IAAI,GAAG6B,KAAK,GAAG,KAAK,GAAG,IAAI;;QAE/BrE,KAAA,CAAKsE,EAAE,GACF,OAAOF,QAAQ,KAAK,WAAW,IAC5B7F,IAAI,CAAC+D,QAAQ,KAAK8B,QAAQ,CAAC9B,QAAQ,IACnCE,IAAI,KAAKjE,IAAI,CAACiE,IAAI;;;EAGtC;EACA;MACQ,IAAM+B,WAAW,GAAGhG,IAAI,IAAIA,IAAI,CAACgG,WAAW;MAC5CvE,KAAA,CAAKhK,cAAc,GAAG8N,OAAO,IAAI,CAACS,WAAW;MAC7C,IAAIvE,KAAA,CAAKzB,IAAI,CAACiG,eAAe,EAAE;QAC3BxE,KAAA,CAAKyE,SAAS,GAAGb,eAAe,EAAE;;MACrC,OAAA5D,KAAA;;IACJY,YAAA,CAAAqD,OAAA;MAAAjP,GAAA;;EAKL;EACA;EACA;EACA;EACA;MALI6L,KAAA,WAAAM,SAMS;QACL,IAAI,CAACuD,IAAI,EAAE;;;EAGnB;EACA;EACA;EACA;EACA;;MALI1P,GAAA;MAAA6L,KAAA,WAAAgB,MAMMC,OAAO,EAAE;QAAA,IAAAvB,MAAA;QACX,IAAI,CAACW,UAAU,GAAG,SAAS;QAC3B,IAAMW,KAAK,GAAG,SAARA,KAAKA,GAAS;UAChBtB,MAAI,CAACW,UAAU,GAAG,QAAQ;UAC1BY,OAAO,EAAE;SACZ;QACD,IAAI,IAAI,CAACqC,OAAO,IAAI,CAAC,IAAI,CAAC3D,QAAQ,EAAE;UAChC,IAAImE,KAAK,GAAG,CAAC;UACb,IAAI,IAAI,CAACR,OAAO,EAAE;YACdQ,KAAK,EAAE;YACP,IAAI,CAACnI,IAAI,CAAC,cAAc,EAAE,YAAY;cAClC,EAAEmI,KAAK,IAAI9C,KAAK,EAAE;aACrB,CAAC;;UAEN,IAAI,CAAC,IAAI,CAACrB,QAAQ,EAAE;YAChBmE,KAAK,EAAE;YACP,IAAI,CAACnI,IAAI,CAAC,OAAO,EAAE,YAAY;cAC3B,EAAEmI,KAAK,IAAI9C,KAAK,EAAE;aACrB,CAAC;;SAET,MACI;UACDA,KAAK,EAAE;;;;EAInB;EACA;EACA;EACA;;MAJI7M,GAAA;MAAA6L,KAAA,WAAA6D,OAKO;QACH,IAAI,CAACP,OAAO,GAAG,IAAI;QACnB,IAAI,CAACS,MAAM,EAAE;QACb,IAAI,CAACxH,YAAY,CAAC,MAAM,CAAC;;;EAGjC;EACA;EACA;EACA;;MAJIpI,GAAA;MAAA6L,KAAA,WAAAa,OAKOvM,IAAI,EAAE;QAAA,IAAA0P,MAAA;QACT,IAAM5O,QAAQ,GAAG,SAAXA,QAAQA,CAAIe,MAAM,EAAK;;UAEzB,IAAI,SAAS,KAAK6N,MAAI,CAAC3D,UAAU,IAAIlK,MAAM,CAAC9B,IAAI,KAAK,MAAM,EAAE;YACzD2P,MAAI,CAACpD,MAAM,EAAE;;;UAGjB,IAAI,OAAO,KAAKzK,MAAM,CAAC9B,IAAI,EAAE;YACzB2P,MAAI,CAACvD,OAAO,CAAC;cAAExB,WAAW,EAAE;aAAkC,CAAC;YAC/D,OAAO,KAAK;;;UAGhB+E,MAAI,CAAClD,QAAQ,CAAC3K,MAAM,CAAC;SACxB;;QAEDwC,aAAa,CAACrE,IAAI,EAAE,IAAI,CAACwL,MAAM,CAACnI,UAAU,CAAC,CAACzD,OAAO,CAACkB,QAAQ,CAAC;;QAE7D,IAAI,QAAQ,KAAK,IAAI,CAACiL,UAAU,EAAE;;UAE9B,IAAI,CAACiD,OAAO,GAAG,KAAK;UACpB,IAAI,CAAC/G,YAAY,CAAC,cAAc,CAAC;UACjC,IAAI,MAAM,KAAK,IAAI,CAAC8D,UAAU,EAAE;YAC5B,IAAI,CAACwD,IAAI,EAAE;;;;;EAO3B;EACA;EACA;EACA;;MAJI1P,GAAA;MAAA6L,KAAA,WAAAQ,UAKU;QAAA,IAAAyD,MAAA;QACN,IAAM1D,KAAK,GAAG,SAARA,KAAKA,GAAS;UAChB0D,MAAI,CAACtD,KAAK,CAAC,CAAC;YAAEtM,IAAI,EAAE;WAAS,CAAC,CAAC;SAClC;QACD,IAAI,MAAM,KAAK,IAAI,CAACgM,UAAU,EAAE;UAC5BE,KAAK,EAAE;SACV,MACI;;;UAGD,IAAI,CAAC5E,IAAI,CAAC,MAAM,EAAE4E,KAAK,CAAC;;;;EAIpC;EACA;EACA;EACA;EACA;;MALIpM,GAAA;MAAA6L,KAAA,WAAAW,MAMMrI,OAAO,EAAE;QAAA,IAAA4L,MAAA;QACX,IAAI,CAACvE,QAAQ,GAAG,KAAK;QACrBtH,aAAa,CAACC,OAAO,EAAE,UAAChE,IAAI,EAAK;UAC7B4P,MAAI,CAACC,OAAO,CAAC7P,IAAI,EAAE,YAAM;YACrB4P,MAAI,CAACvE,QAAQ,GAAG,IAAI;YACpBuE,MAAI,CAAC3H,YAAY,CAAC,OAAO,CAAC;WAC7B,CAAC;SACL,CAAC;;;EAGV;EACA;EACA;EACA;;MAJIpI,GAAA;MAAA6L,KAAA,WAAAoE,MAKM;QACF,IAAMjD,MAAM,GAAG,IAAI,CAACzD,IAAI,CAACkE,MAAM,GAAG,OAAO,GAAG,MAAM;QAClD,IAAM/B,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,EAAE;;QAE9B,IAAI,KAAK,KAAK,IAAI,CAACnC,IAAI,CAAC2G,iBAAiB,EAAE;UACvCxE,KAAK,CAAC,IAAI,CAACnC,IAAI,CAAC4G,cAAc,CAAC,GAAGjC,KAAK,EAAE;;QAE7C,IAAI,CAAC,IAAI,CAAClN,cAAc,IAAI,CAAC0K,KAAK,CAAC0E,GAAG,EAAE;UACpC1E,KAAK,CAAC2E,GAAG,GAAG,CAAC;;QAEjB,OAAO,IAAI,CAACtD,SAAS,CAACC,MAAM,EAAEtB,KAAK,CAAC;;;EAG5C;EACA;EACA;EACA;EACA;;MALI1L,GAAA;MAAA6L,KAAA,WAAAyE,UAMmB;QAAA,IAAX/G,IAAI,GAAA5B,SAAA,CAAAlF,MAAA,QAAAkF,SAAA,QAAAsF,SAAA,GAAAtF,SAAA,MAAG,EAAE;QACb4I,QAAA,CAAchH,IAAI,EAAE;UAAE+F,EAAE,EAAE,IAAI,CAACA,EAAE;UAAEG,SAAS,EAAE,IAAI,CAACA;SAAW,EAAE,IAAI,CAAClG,IAAI,CAAC;QAC1E,OAAO,IAAIiH,OAAO,CAAC,IAAI,CAACP,GAAG,EAAE,EAAE1G,IAAI,CAAC;;;EAG5C;EACA;EACA;EACA;EACA;EACA;;MANIvJ,GAAA;MAAA6L,KAAA,WAAAmE,QAOQ7P,IAAI,EAAEmH,EAAE,EAAE;QAAA,IAAAmJ,MAAA;QACd,IAAMC,GAAG,GAAG,IAAI,CAACJ,OAAO,CAAC;UACrBK,MAAM,EAAE,MAAM;UACdxQ,IAAI,EAAEA;SACT,CAAC;QACFuQ,GAAG,CAACvJ,EAAE,CAAC,SAAS,EAAEG,EAAE,CAAC;QACrBoJ,GAAG,CAACvJ,EAAE,CAAC,OAAO,EAAE,UAACyJ,SAAS,EAAE7F,OAAO,EAAK;UACpC0F,MAAI,CAAC3E,OAAO,CAAC,gBAAgB,EAAE8E,SAAS,EAAE7F,OAAO,CAAC;SACrD,CAAC;;;EAGV;EACA;EACA;EACA;;MAJI/K,GAAA;MAAA6L,KAAA,WAAA+D,SAKS;QAAA,IAAAiB,MAAA;QACL,IAAMH,GAAG,GAAG,IAAI,CAACJ,OAAO,EAAE;QAC1BI,GAAG,CAACvJ,EAAE,CAAC,MAAM,EAAE,IAAI,CAACuF,MAAM,CAAChD,IAAI,CAAC,IAAI,CAAC,CAAC;QACtCgH,GAAG,CAACvJ,EAAE,CAAC,OAAO,EAAE,UAACyJ,SAAS,EAAE7F,OAAO,EAAK;UACpC8F,MAAI,CAAC/E,OAAO,CAAC,gBAAgB,EAAE8E,SAAS,EAAE7F,OAAO,CAAC;SACrD,CAAC;QACF,IAAI,CAAC+F,OAAO,GAAGJ,GAAG;;;MACrB1Q,GAAA;MAAA+Q,GAAA,WAAAA,MA/KU;QACP,OAAO,SAAS;;;IACnB,OAAA9B,OAAA;EAAA,EAjCwB7D,SAAS;MAgNzBoF,OAAO,0BAAAnF,QAAA;IAAAX,SAAA,CAAA8F,OAAA,EAAAnF,QAAA;IAAA,IAAAC,OAAA,GAAAV,YAAA,CAAA4F,OAAA;;EAEpB;EACA;EACA;EACA;EACA;IACI,SAAAA,QAAYP,GAAG,EAAE1G,IAAI,EAAE;MAAA,IAAAyH,MAAA;MAAA/F,eAAA,OAAAuF,OAAA;MACnBQ,MAAA,GAAA1F,OAAA,CAAA9K,IAAA;MACA8I,qBAAqB,CAAAmC,sBAAA,CAAAuF,MAAA,GAAOzH,IAAI,CAAC;MACjCyH,MAAA,CAAKzH,IAAI,GAAGA,IAAI;MAChByH,MAAA,CAAKL,MAAM,GAAGpH,IAAI,CAACoH,MAAM,IAAI,KAAK;MAClCK,MAAA,CAAKf,GAAG,GAAGA,GAAG;MACde,MAAA,CAAK7Q,IAAI,GAAG8M,SAAS,KAAK1D,IAAI,CAACpJ,IAAI,GAAGoJ,IAAI,CAACpJ,IAAI,GAAG,IAAI;MACtD6Q,MAAA,CAAKpR,MAAM,EAAE;MAAC,OAAAoR,MAAA;;;EAGtB;EACA;EACA;EACA;IAJIpF,YAAA,CAAA4E,OAAA;MAAAxQ,GAAA;MAAA6L,KAAA,WAAAjM,SAKS;QAAA,IAAAqR,MAAA;QACL,IAAIC,EAAE;QACN,IAAM3H,IAAI,GAAGZ,IAAI,CAAC,IAAI,CAACY,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,WAAW,CAAC;QAC7HA,IAAI,CAACkF,OAAO,GAAG,CAAC,CAAC,IAAI,CAAClF,IAAI,CAAC+F,EAAE;QAC7B,IAAMP,GAAG,GAAI,IAAI,CAACA,GAAG,GAAG,IAAIV,GAAc,CAAC9E,IAAI,CAAE;QACjD,IAAI;UACAwF,GAAG,CAAC9C,IAAI,CAAC,IAAI,CAAC0E,MAAM,EAAE,IAAI,CAACV,GAAG,EAAE,IAAI,CAAC;UACrC,IAAI;YACA,IAAI,IAAI,CAAC1G,IAAI,CAAC4H,YAAY,EAAE;cACxBpC,GAAG,CAACqC,qBAAqB,IAAIrC,GAAG,CAACqC,qBAAqB,CAAC,IAAI,CAAC;cAC5D,KAAK,IAAI5O,CAAC,IAAI,IAAI,CAAC+G,IAAI,CAAC4H,YAAY,EAAE;gBAClC,IAAI,IAAI,CAAC5H,IAAI,CAAC4H,YAAY,CAACnI,cAAc,CAACxG,CAAC,CAAC,EAAE;kBAC1CuM,GAAG,CAACsC,gBAAgB,CAAC7O,CAAC,EAAE,IAAI,CAAC+G,IAAI,CAAC4H,YAAY,CAAC3O,CAAC,CAAC,CAAC;;;;WAIjE,CACD,OAAOkM,CAAC,EAAE;UACV,IAAI,MAAM,KAAK,IAAI,CAACiC,MAAM,EAAE;YACxB,IAAI;cACA5B,GAAG,CAACsC,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC;aACnE,CACD,OAAO3C,CAAC,EAAE;;UAEd,IAAI;YACAK,GAAG,CAACsC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC;WACxC,CACD,OAAO3C,CAAC,EAAE;UACV,CAACwC,EAAE,GAAG,IAAI,CAAC3H,IAAI,CAACkG,SAAS,MAAM,IAAI,IAAIyB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,UAAU,CAACvC,GAAG,CAAC;;UAElF,IAAI,iBAAiB,IAAIA,GAAG,EAAE;YAC1BA,GAAG,CAACS,eAAe,GAAG,IAAI,CAACjG,IAAI,CAACiG,eAAe;;UAEnD,IAAI,IAAI,CAACjG,IAAI,CAACgI,cAAc,EAAE;YAC1BxC,GAAG,CAACyC,OAAO,GAAG,IAAI,CAACjI,IAAI,CAACgI,cAAc;;UAE1CxC,GAAG,CAAC0C,kBAAkB,GAAG,YAAM;YAC3B,IAAIP,EAAE;YACN,IAAInC,GAAG,CAAC7C,UAAU,KAAK,CAAC,EAAE;cACtB,CAACgF,EAAE,GAAGD,MAAI,CAAC1H,IAAI,CAACkG,SAAS,MAAM,IAAI,IAAIyB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,YAAY,CAAC3C,GAAG,CAAC;;YAExF,IAAI,CAAC,KAAKA,GAAG,CAAC7C,UAAU,EACpB;YACJ,IAAI,GAAG,KAAK6C,GAAG,CAAC4C,MAAM,IAAI,IAAI,KAAK5C,GAAG,CAAC4C,MAAM,EAAE;cAC3CV,MAAI,CAACW,MAAM,EAAE;aAChB,MACI;;;cAGDX,MAAI,CAACxH,YAAY,CAAC,YAAM;gBACpBwH,MAAI,CAACnF,OAAO,CAAC,OAAOiD,GAAG,CAAC4C,MAAM,KAAK,QAAQ,GAAG5C,GAAG,CAAC4C,MAAM,GAAG,CAAC,CAAC;eAChE,EAAE,CAAC,CAAC;;WAEZ;UACD5C,GAAG,CAACxC,IAAI,CAAC,IAAI,CAACpM,IAAI,CAAC;SACtB,CACD,OAAOuO,CAAC,EAAE;;;;UAIN,IAAI,CAACjF,YAAY,CAAC,YAAM;YACpBwH,MAAI,CAACnF,OAAO,CAAC4C,CAAC,CAAC;WAClB,EAAE,CAAC,CAAC;UACL;;QAEJ,IAAI,OAAOmD,QAAQ,KAAK,WAAW,EAAE;UACjC,IAAI,CAACC,KAAK,GAAGtB,OAAO,CAACuB,aAAa,EAAE;UACpCvB,OAAO,CAACwB,QAAQ,CAAC,IAAI,CAACF,KAAK,CAAC,GAAG,IAAI;;;;EAI/C;EACA;EACA;EACA;;MAJI9R,GAAA;MAAA6L,KAAA,WAAAC,QAKQwC,GAAG,EAAE;QACT,IAAI,CAAClG,YAAY,CAAC,OAAO,EAAEkG,GAAG,EAAE,IAAI,CAACS,GAAG,CAAC;QACzC,IAAI,CAACkD,OAAO,CAAC,IAAI,CAAC;;;EAG1B;EACA;EACA;EACA;;MAJIjS,GAAA;MAAA6L,KAAA,WAAAoG,QAKQC,SAAS,EAAE;QACf,IAAI,WAAW,KAAK,OAAO,IAAI,CAACnD,GAAG,IAAI,IAAI,KAAK,IAAI,CAACA,GAAG,EAAE;UACtD;;QAEJ,IAAI,CAACA,GAAG,CAAC0C,kBAAkB,GAAG5C,KAAK;QACnC,IAAIqD,SAAS,EAAE;UACX,IAAI;YACA,IAAI,CAACnD,GAAG,CAACoD,KAAK,EAAE;WACnB,CACD,OAAOzD,CAAC,EAAE;;QAEd,IAAI,OAAOmD,QAAQ,KAAK,WAAW,EAAE;UACjC,OAAOrB,OAAO,CAACwB,QAAQ,CAAC,IAAI,CAACF,KAAK,CAAC;;QAEvC,IAAI,CAAC/C,GAAG,GAAG,IAAI;;;EAGvB;EACA;EACA;EACA;;MAJI/O,GAAA;MAAA6L,KAAA,WAAA+F,SAKS;QACL,IAAMzR,IAAI,GAAG,IAAI,CAAC4O,GAAG,CAACqD,YAAY;QAClC,IAAIjS,IAAI,KAAK,IAAI,EAAE;UACf,IAAI,CAACiI,YAAY,CAAC,MAAM,EAAEjI,IAAI,CAAC;UAC/B,IAAI,CAACiI,YAAY,CAAC,SAAS,CAAC;UAC5B,IAAI,CAAC6J,OAAO,EAAE;;;;EAI1B;EACA;EACA;EACA;;MAJIjS,GAAA;MAAA6L,KAAA,WAAAsG,QAKQ;QACJ,IAAI,CAACF,OAAO,EAAE;;;IACjB,OAAAzB,OAAA;EAAA,EA7IwBvJ,OAAO;EA+IpCuJ,OAAO,CAACuB,aAAa,GAAG,CAAC;EACzBvB,OAAO,CAACwB,QAAQ,GAAG,EAAE;EACrB;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOH,QAAQ,KAAK,WAAW,EAAE;;IAEjC,IAAI,OAAOQ,WAAW,KAAK,UAAU,EAAE;;MAEnCA,WAAW,CAAC,UAAU,EAAEC,aAAa,CAAC;KACzC,MACI,IAAI,OAAOlL,gBAAgB,KAAK,UAAU,EAAE;MAC7C,IAAMmL,gBAAgB,GAAG,YAAY,IAAIrJ,cAAU,GAAG,UAAU,GAAG,QAAQ;MAC3E9B,gBAAgB,CAACmL,gBAAgB,EAAED,aAAa,EAAE,KAAK,CAAC;;EAEhE;EACA,SAASA,aAAaA,GAAG;IACrB,KAAK,IAAI9P,CAAC,IAAIgO,OAAO,CAACwB,QAAQ,EAAE;MAC5B,IAAIxB,OAAO,CAACwB,QAAQ,CAAChJ,cAAc,CAACxG,CAAC,CAAC,EAAE;QACpCgO,OAAO,CAACwB,QAAQ,CAACxP,CAAC,CAAC,CAAC2P,KAAK,EAAE;;;EAGvC;;ECpYO,IAAMK,QAAQ,GAAI,YAAM;IAC3B,IAAMC,kBAAkB,GAAG,OAAOC,OAAO,KAAK,UAAU,IAAI,OAAOA,OAAO,CAACC,OAAO,KAAK,UAAU;IACjG,IAAIF,kBAAkB,EAAE;MACpB,OAAO,UAACzK,EAAE;QAAA,OAAK0K,OAAO,CAACC,OAAO,EAAE,CAACzQ,IAAI,CAAC8F,EAAE,CAAC;;KAC5C,MACI;MACD,OAAO,UAACA,EAAE,EAAEyB,YAAY;QAAA,OAAKA,YAAY,CAACzB,EAAE,EAAE,CAAC,CAAC;;;EAExD,CAAC,EAAG;EACG,IAAM4K,SAAS,GAAG1J,cAAU,CAAC0J,SAAS,IAAI1J,cAAU,CAAC2J,YAAY;EACjE,IAAMC,qBAAqB,GAAG,IAAI;EAClC,IAAMC,iBAAiB,GAAG,aAAa;;ECP9C;EACA,IAAMC,aAAa,GAAG,OAAOC,SAAS,KAAK,WAAW,IAClD,OAAOA,SAAS,CAACC,OAAO,KAAK,QAAQ,IACrCD,SAAS,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,aAAa;MACxCC,EAAE,0BAAAlE,UAAA;IAAAxE,SAAA,CAAA0I,EAAA,EAAAlE,UAAA;IAAA,IAAAvE,MAAA,GAAAC,YAAA,CAAAwI,EAAA;;EAEf;EACA;EACA;EACA;EACA;IACI,SAAAA,GAAY7J,IAAI,EAAE;MAAA,IAAAyB,KAAA;MAAAC,eAAA,OAAAmI,EAAA;MACdpI,KAAA,GAAAL,MAAA,CAAAnK,IAAA,OAAM+I,IAAI;MACVyB,KAAA,CAAKhK,cAAc,GAAG,CAACuI,IAAI,CAACgG,WAAW;MAAC,OAAAvE,KAAA;;IAC3CY,YAAA,CAAAwH,EAAA;MAAApT,GAAA;MAAA6L,KAAA,WAAAM,SAIQ;QACL,IAAI,CAAC,IAAI,CAACkH,KAAK,EAAE,EAAE;;UAEf;;QAEJ,IAAMpD,GAAG,GAAG,IAAI,CAACA,GAAG,EAAE;QACtB,IAAMqD,SAAS,GAAG,IAAI,CAAC/J,IAAI,CAAC+J,SAAS;;QAErC,IAAM/J,IAAI,GAAGyJ,aAAa,GACpB,EAAE,GACFrK,IAAI,CAAC,IAAI,CAACY,IAAI,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,cAAc,EAAE,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,qBAAqB,CAAC;QAC1N,IAAI,IAAI,CAACA,IAAI,CAAC4H,YAAY,EAAE;UACxB5H,IAAI,CAACgK,OAAO,GAAG,IAAI,CAAChK,IAAI,CAAC4H,YAAY;;QAEzC,IAAI;UACA,IAAI,CAACqC,EAAE,GACHV,qBAAqB,IAAI,CAACE,aAAa,GACjCM,SAAS,GACL,IAAIV,SAAS,CAAC3C,GAAG,EAAEqD,SAAS,CAAC,GAC7B,IAAIV,SAAS,CAAC3C,GAAG,CAAC,GACtB,IAAI2C,SAAS,CAAC3C,GAAG,EAAEqD,SAAS,EAAE/J,IAAI,CAAC;SAChD,CACD,OAAO+E,GAAG,EAAE;UACR,OAAO,IAAI,CAAClG,YAAY,CAAC,OAAO,EAAEkG,GAAG,CAAC;;QAE1C,IAAI,CAACkF,EAAE,CAAChQ,UAAU,GAAG,IAAI,CAACmI,MAAM,CAACnI,UAAU;QAC3C,IAAI,CAACiQ,iBAAiB,EAAE;;;EAGhC;EACA;EACA;EACA;;MAJIzT,GAAA;MAAA6L,KAAA,WAAA4H,oBAKoB;QAAA,IAAAlI,MAAA;QAChB,IAAI,CAACiI,EAAE,CAACE,MAAM,GAAG,YAAM;UACnB,IAAInI,MAAI,CAAChC,IAAI,CAACoK,SAAS,EAAE;YACrBpI,MAAI,CAACiI,EAAE,CAACI,OAAO,CAACC,KAAK,EAAE;;UAE3BtI,MAAI,CAACkB,MAAM,EAAE;SAChB;QACD,IAAI,CAAC+G,EAAE,CAACM,OAAO,GAAG,UAACC,UAAU;UAAA,OAAKxI,MAAI,CAACe,OAAO,CAAC;YAC3CxB,WAAW,EAAE,6BAA6B;YAC1CC,OAAO,EAAEgJ;WACZ,CAAC;;QACF,IAAI,CAACP,EAAE,CAACQ,SAAS,GAAG,UAACC,EAAE;UAAA,OAAK1I,MAAI,CAACmB,MAAM,CAACuH,EAAE,CAAC9T,IAAI,CAAC;;QAChD,IAAI,CAACqT,EAAE,CAACU,OAAO,GAAG,UAACxF,CAAC;UAAA,OAAKnD,MAAI,CAACO,OAAO,CAAC,iBAAiB,EAAE4C,CAAC,CAAC;;;;MAC9D1O,GAAA;MAAA6L,KAAA,WAAAW,MACKrI,OAAO,EAAE;QAAA,IAAA0L,MAAA;QACX,IAAI,CAACrE,QAAQ,GAAG,KAAK;;;QAErB,IAAA2I,KAAA,YAAAA,MACS3R,CAAC;UACN,IAAMR,MAAM,GAAGmC,OAAO,CAAC3B,CAAC,CAAC;UACzB,IAAM4R,UAAU,GAAG5R,CAAC,KAAK2B,OAAO,CAAC1B,MAAM,GAAG,CAAC;UAC3C3B,YAAY,CAACkB,MAAM,EAAE6N,MAAI,CAAC7O,cAAc,EAAE,UAACb,IAAI,EAAK;;YAEhD,IAAMoJ,IAAI,GAAG,EAAE;;;;YAiBf,IAAI;cACA,IAAIuJ,qBAAqB,EAAE;;gBAEvBjD,MAAI,CAAC2D,EAAE,CAACjH,IAAI,CAACpM,IAAI,CAAC;;aAKzB,CACD,OAAOuO,CAAC,EAAE;YAEV,IAAI0F,UAAU,EAAE;;;cAGZ5B,QAAQ,CAAC,YAAM;gBACX3C,MAAI,CAACrE,QAAQ,GAAG,IAAI;gBACpBqE,MAAI,CAACzH,YAAY,CAAC,OAAO,CAAC;eAC7B,EAAEyH,MAAI,CAACpG,YAAY,CAAC;;WAE5B,CAAC;;QAzCN,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,OAAO,CAAC1B,MAAM,EAAED,CAAC,EAAE,EAAE;UAAA2R,KAAA,CAAhC3R,CAAC;;;;MA2CbxC,GAAA;MAAA6L,KAAA,WAAAQ,UACS;QACN,IAAI,OAAO,IAAI,CAACmH,EAAE,KAAK,WAAW,EAAE;UAChC,IAAI,CAACA,EAAE,CAACpH,KAAK,EAAE;UACf,IAAI,CAACoH,EAAE,GAAG,IAAI;;;;EAI1B;EACA;EACA;EACA;;MAJIxT,GAAA;MAAA6L,KAAA,WAAAoE,MAKM;QACF,IAAMjD,MAAM,GAAG,IAAI,CAACzD,IAAI,CAACkE,MAAM,GAAG,KAAK,GAAG,IAAI;QAC9C,IAAM/B,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,EAAE;;QAE9B,IAAI,IAAI,CAACnC,IAAI,CAAC2G,iBAAiB,EAAE;UAC7BxE,KAAK,CAAC,IAAI,CAACnC,IAAI,CAAC4G,cAAc,CAAC,GAAGjC,KAAK,EAAE;;;QAG7C,IAAI,CAAC,IAAI,CAAClN,cAAc,EAAE;UACtB0K,KAAK,CAAC2E,GAAG,GAAG,CAAC;;QAEjB,OAAO,IAAI,CAACtD,SAAS,CAACC,MAAM,EAAEtB,KAAK,CAAC;;;EAG5C;EACA;EACA;EACA;EACA;;MALI1L,GAAA;MAAA6L,KAAA,WAAAwH,QAMQ;QACJ,OAAO,CAAC,CAACT,SAAS;;;MACrB5S,GAAA;MAAA+Q,GAAA,WAAAA,MAlIU;QACP,OAAO,WAAW;;;IACrB,OAAAqC,EAAA;EAAA,EAbmBhI,SAAS;;MCNpBiJ,EAAE,0BAAAnF,UAAA;IAAAxE,SAAA,CAAA2J,EAAA,EAAAnF,UAAA;IAAA,IAAAvE,MAAA,GAAAC,YAAA,CAAAyJ,EAAA;IAAA,SAAAA;MAAApJ,eAAA,OAAAoJ,EAAA;MAAA,OAAA1J,MAAA,CAAAjD,KAAA,OAAAC,SAAA;;IAAAiE,YAAA,CAAAyI,EAAA;MAAArU,GAAA;MAAA6L,KAAA,WAAAM,SAIF;QAAA,IAAAnB,KAAA;;QAEL,IAAI,OAAOsJ,YAAY,KAAK,UAAU,EAAE;UACpC;;;QAGJ,IAAI,CAACC,SAAS,GAAG,IAAID,YAAY,CAAC,IAAI,CAACvH,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,CAACxD,IAAI,CAACiL,gBAAgB,CAAC,IAAI,CAACC,IAAI,CAAC,CAAC;QACjG,IAAI,CAACF,SAAS,CAACG,MAAM,CAChBxS,IAAI,CAAC,YAAM;UACZ8I,KAAI,CAACsB,OAAO,EAAE;SACjB,CAAC,SACQ,CAAC,UAACgC,GAAG,EAAK;UAChBtD,KAAI,CAACc,OAAO,CAAC,oBAAoB,EAAEwC,GAAG,CAAC;SAC1C,CAAC;;QAEF,IAAI,CAACiG,SAAS,CAACI,KAAK,CAACzS,IAAI,CAAC,YAAM;UAC5B8I,KAAI,CAACuJ,SAAS,CAACK,yBAAyB,EAAE,CAAC1S,IAAI,CAAC,UAAC2S,MAAM,EAAK;YACxD,IAAMC,aAAa,GAAG1O,yBAAyB,CAACsH,MAAM,CAACqH,gBAAgB,EAAE/J,KAAI,CAACW,MAAM,CAACnI,UAAU,CAAC;YAChG,IAAMwR,MAAM,GAAGH,MAAM,CAACI,QAAQ,CAACC,WAAW,CAACJ,aAAa,CAAC,CAACK,SAAS,EAAE;YACrE,IAAMC,aAAa,GAAGxQ,yBAAyB,EAAE;YACjDwQ,aAAa,CAACH,QAAQ,CAACI,MAAM,CAACR,MAAM,CAACrJ,QAAQ,CAAC;YAC9CR,KAAI,CAACsK,MAAM,GAAGF,aAAa,CAAC5J,QAAQ,CAAC+J,SAAS,EAAE;YAChD,IAAMC,IAAI,GAAG,SAAPA,IAAIA,GAAS;cACfR,MAAM,CACDQ,IAAI,EAAE,CACNtT,IAAI,CAAC,UAAAnB,IAAA,EAAqB;gBAAA,IAAlB0U,IAAI,GAAA1U,IAAA,CAAJ0U,IAAI;kBAAE5J,KAAK,GAAA9K,IAAA,CAAL8K,KAAK;gBACpB,IAAI4J,IAAI,EAAE;kBACN;;gBAEJzK,KAAI,CAAC2B,QAAQ,CAACd,KAAK,CAAC;gBACpB2J,IAAI,EAAE;eACT,CAAC,SACQ,CAAC,UAAClH,GAAG,EAAK,EACnB,CAAC;aACL;YACDkH,IAAI,EAAE;YACN,IAAMxT,MAAM,GAAG;cAAE9B,IAAI,EAAE;aAAQ;YAC/B,IAAI8K,KAAI,CAACU,KAAK,CAAC0E,GAAG,EAAE;cAChBpO,MAAM,CAAC7B,IAAI,iBAAAwO,MAAA,CAAc3D,KAAI,CAACU,KAAK,CAAC0E,GAAG,QAAI;;YAE/CpF,KAAI,CAACsK,MAAM,CAAC9I,KAAK,CAACxK,MAAM,CAAC,CAACE,IAAI,CAAC;cAAA,OAAM8I,KAAI,CAACyB,MAAM,EAAE;cAAC;WACtD,CAAC;SACL,CAAC;;;MACLzM,GAAA;MAAA6L,KAAA,WAAAW,MACKrI,OAAO,EAAE;QAAA,IAAAoH,MAAA;QACX,IAAI,CAACC,QAAQ,GAAG,KAAK;QAAC,IAAA2I,KAAA,YAAAA,MACb3R,CAAC;UACN,IAAMR,MAAM,GAAGmC,OAAO,CAAC3B,CAAC,CAAC;UACzB,IAAM4R,UAAU,GAAG5R,CAAC,KAAK2B,OAAO,CAAC1B,MAAM,GAAG,CAAC;UAC3C8I,MAAI,CAAC+J,MAAM,CAAC9I,KAAK,CAACxK,MAAM,CAAC,CAACE,IAAI,CAAC,YAAM;YACjC,IAAIkS,UAAU,EAAE;cACZ5B,QAAQ,CAAC,YAAM;gBACXjH,MAAI,CAACC,QAAQ,GAAG,IAAI;gBACpBD,MAAI,CAACnD,YAAY,CAAC,OAAO,CAAC;eAC7B,EAAEmD,MAAI,CAAC9B,YAAY,CAAC;;WAE5B,CAAC;;QAVN,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,OAAO,CAAC1B,MAAM,EAAED,CAAC,EAAE,EAAE;UAAA2R,KAAA,CAAhC3R,CAAC;;;;MAYbxC,GAAA;MAAA6L,KAAA,WAAAQ,UACS;QACN,IAAI6E,EAAE;QACN,CAACA,EAAE,GAAG,IAAI,CAACqD,SAAS,MAAM,IAAI,IAAIrD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC9E,KAAK,EAAE;;;MACxEpM,GAAA;MAAA+Q,GAAA,WAAAA,MAjEU;QACP,OAAO,cAAc;;;IACxB,OAAAsD,EAAA;EAAA,EAHmBjJ,SAAS;;ECA1B,IAAMsK,UAAU,GAAG;IACtBC,SAAS,EAAEvC,EAAE;IACbwC,YAAY,EAAEvB,EAAE;IAChBlF,OAAO,EAAEF;EACb,CAAC;;ECPD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAM4G,EAAE,GAAG,qPAAqP;EAChQ,IAAMC,KAAK,GAAG,CACV,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAChJ;EACM,SAASC,KAAKA,CAAChM,GAAG,EAAE;IACvB,IAAIA,GAAG,CAACtH,MAAM,GAAG,IAAI,EAAE;MACnB,MAAM,cAAc;;IAExB,IAAMuT,GAAG,GAAGjM,GAAG;MAAEkM,CAAC,GAAGlM,GAAG,CAACwD,OAAO,CAAC,GAAG,CAAC;MAAEmB,CAAC,GAAG3E,GAAG,CAACwD,OAAO,CAAC,GAAG,CAAC;IAC3D,IAAI0I,CAAC,IAAI,CAAC,CAAC,IAAIvH,CAAC,IAAI,CAAC,CAAC,EAAE;MACpB3E,GAAG,GAAGA,GAAG,CAACnG,SAAS,CAAC,CAAC,EAAEqS,CAAC,CAAC,GAAGlM,GAAG,CAACnG,SAAS,CAACqS,CAAC,EAAEvH,CAAC,CAAC,CAACwH,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGnM,GAAG,CAACnG,SAAS,CAAC8K,CAAC,EAAE3E,GAAG,CAACtH,MAAM,CAAC;;IAErG,IAAI0T,CAAC,GAAGN,EAAE,CAACO,IAAI,CAACrM,GAAG,IAAI,EAAE,CAAC;MAAEkG,GAAG,GAAG,EAAE;MAAEzN,CAAC,GAAG,EAAE;IAC5C,OAAOA,CAAC,EAAE,EAAE;MACRyN,GAAG,CAAC6F,KAAK,CAACtT,CAAC,CAAC,CAAC,GAAG2T,CAAC,CAAC3T,CAAC,CAAC,IAAI,EAAE;;IAE9B,IAAIyT,CAAC,IAAI,CAAC,CAAC,IAAIvH,CAAC,IAAI,CAAC,CAAC,EAAE;MACpBuB,GAAG,CAACoG,MAAM,GAAGL,GAAG;MAChB/F,GAAG,CAACqG,IAAI,GAAGrG,GAAG,CAACqG,IAAI,CAAC1S,SAAS,CAAC,CAAC,EAAEqM,GAAG,CAACqG,IAAI,CAAC7T,MAAM,GAAG,CAAC,CAAC,CAACyT,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MACxEjG,GAAG,CAACsG,SAAS,GAAGtG,GAAG,CAACsG,SAAS,CAACL,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAClFjG,GAAG,CAACuG,OAAO,GAAG,IAAI;;IAEtBvG,GAAG,CAACwG,SAAS,GAAGA,SAAS,CAACxG,GAAG,EAAEA,GAAG,CAAC,MAAM,CAAC,CAAC;IAC3CA,GAAG,CAACyG,QAAQ,GAAGA,QAAQ,CAACzG,GAAG,EAAEA,GAAG,CAAC,OAAO,CAAC,CAAC;IAC1C,OAAOA,GAAG;EACd;EACA,SAASwG,SAASA,CAAC7V,GAAG,EAAEwM,IAAI,EAAE;IAC1B,IAAMuJ,IAAI,GAAG,UAAU;MAAEC,KAAK,GAAGxJ,IAAI,CAAC8I,OAAO,CAACS,IAAI,EAAE,GAAG,CAAC,CAACnV,KAAK,CAAC,GAAG,CAAC;IACnE,IAAI4L,IAAI,CAACjH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,IAAIiH,IAAI,CAAC3K,MAAM,KAAK,CAAC,EAAE;MAC9CmU,KAAK,CAAC3O,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEtB,IAAImF,IAAI,CAACjH,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MACvByQ,KAAK,CAAC3O,MAAM,CAAC2O,KAAK,CAACnU,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;;IAErC,OAAOmU,KAAK;EAChB;EACA,SAASF,QAAQA,CAACzG,GAAG,EAAEvE,KAAK,EAAE;IAC1B,IAAMvL,IAAI,GAAG,EAAE;IACfuL,KAAK,CAACwK,OAAO,CAAC,2BAA2B,EAAE,UAAUW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MAC7D,IAAID,EAAE,EAAE;QACJ3W,IAAI,CAAC2W,EAAE,CAAC,GAAGC,EAAE;;KAEpB,CAAC;IACF,OAAO5W,IAAI;EACf;;MCxDa6W,MAAM,0BAAA3L,QAAA;IAAAX,SAAA,CAAAsM,MAAA,EAAA3L,QAAA;IAAA,IAAAV,MAAA,GAAAC,YAAA,CAAAoM,MAAA;;EAEnB;EACA;EACA;EACA;EACA;IACI,SAAAA,OAAY/G,GAAG,EAAa;MAAA,IAAAjF,KAAA;MAAA,IAAXzB,IAAI,GAAA5B,SAAA,CAAAlF,MAAA,QAAAkF,SAAA,QAAAsF,SAAA,GAAAtF,SAAA,MAAG,EAAE;MAAAsD,eAAA,OAAA+L,MAAA;MACtBhM,KAAA,GAAAL,MAAA,CAAAnK,IAAA;MACAwK,KAAA,CAAKxH,UAAU,GAAGuP,iBAAiB;MACnC/H,KAAA,CAAKiM,WAAW,GAAG,EAAE;MACrB,IAAIhH,GAAG,IAAI,QAAQ,KAAAiH,OAAA,CAAYjH,GAAG,GAAE;QAChC1G,IAAI,GAAG0G,GAAG;QACVA,GAAG,GAAG,IAAI;;MAEd,IAAIA,GAAG,EAAE;QACLA,GAAG,GAAG8F,KAAK,CAAC9F,GAAG,CAAC;QAChB1G,IAAI,CAAC+D,QAAQ,GAAG2C,GAAG,CAACqG,IAAI;QACxB/M,IAAI,CAACkE,MAAM,GAAGwC,GAAG,CAACjJ,QAAQ,KAAK,OAAO,IAAIiJ,GAAG,CAACjJ,QAAQ,KAAK,KAAK;QAChEuC,IAAI,CAACiE,IAAI,GAAGyC,GAAG,CAACzC,IAAI;QACpB,IAAIyC,GAAG,CAACvE,KAAK,EACTnC,IAAI,CAACmC,KAAK,GAAGuE,GAAG,CAACvE,KAAK;OAC7B,MACI,IAAInC,IAAI,CAAC+M,IAAI,EAAE;QAChB/M,IAAI,CAAC+D,QAAQ,GAAGyI,KAAK,CAACxM,IAAI,CAAC+M,IAAI,CAAC,CAACA,IAAI;;MAEzChN,qBAAqB,CAAAmC,sBAAA,CAAAT,KAAA,GAAOzB,IAAI,CAAC;MACjCyB,KAAA,CAAKyC,MAAM,GACP,IAAI,IAAIlE,IAAI,CAACkE,MAAM,GACblE,IAAI,CAACkE,MAAM,GACX,OAAO2B,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAKA,QAAQ,CAACpI,QAAQ;MAC3E,IAAIuC,IAAI,CAAC+D,QAAQ,IAAI,CAAC/D,IAAI,CAACiE,IAAI,EAAE;;QAE7BjE,IAAI,CAACiE,IAAI,GAAGxC,KAAA,CAAKyC,MAAM,GAAG,KAAK,GAAG,IAAI;;MAE1CzC,KAAA,CAAKsC,QAAQ,GACT/D,IAAI,CAAC+D,QAAQ,KACR,OAAO8B,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAAC9B,QAAQ,GAAG,WAAW,CAAC;MAC3EtC,KAAA,CAAKwC,IAAI,GACLjE,IAAI,CAACiE,IAAI,KACJ,OAAO4B,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAAC5B,IAAI,GAC3C4B,QAAQ,CAAC5B,IAAI,GACbxC,KAAA,CAAKyC,MAAM,GACP,KAAK,GACL,IAAI,CAAC;MACvBzC,KAAA,CAAK0K,UAAU,GAAGnM,IAAI,CAACmM,UAAU,IAAI,CACjC,SAAS,EACT,WAAW,EACX,cAAc,CACjB;MACD1K,KAAA,CAAKiM,WAAW,GAAG,EAAE;MACrBjM,KAAA,CAAKmM,aAAa,GAAG,CAAC;MACtBnM,KAAA,CAAKzB,IAAI,GAAGgH,QAAA,CAAc;QACtBnD,IAAI,EAAE,YAAY;QAClBgK,KAAK,EAAE,KAAK;QACZ5H,eAAe,EAAE,KAAK;QACtB6H,OAAO,EAAE,IAAI;QACblH,cAAc,EAAE,GAAG;QACnBmH,eAAe,EAAE,KAAK;QACtBC,gBAAgB,EAAE,IAAI;QACtBC,kBAAkB,EAAE,IAAI;QACxBC,iBAAiB,EAAE;UACfC,SAAS,EAAE;SACd;QACDlD,gBAAgB,EAAE,EAAE;QACpBmD,mBAAmB,EAAE;OACxB,EAAEpO,IAAI,CAAC;MACRyB,KAAA,CAAKzB,IAAI,CAAC6D,IAAI,GACVpC,KAAA,CAAKzB,IAAI,CAAC6D,IAAI,CAAC8I,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAC5BlL,KAAA,CAAKzB,IAAI,CAACgO,gBAAgB,GAAG,GAAG,GAAG,EAAE,CAAC;MAC/C,IAAI,OAAOvM,KAAA,CAAKzB,IAAI,CAACmC,KAAK,KAAK,QAAQ,EAAE;QACrCV,KAAA,CAAKzB,IAAI,CAACmC,KAAK,GAAG/I,MAAM,CAACqI,KAAA,CAAKzB,IAAI,CAACmC,KAAK,CAAC;;;MAG7CV,KAAA,CAAK4M,EAAE,GAAG,IAAI;MACd5M,KAAA,CAAK6M,QAAQ,GAAG,IAAI;MACpB7M,KAAA,CAAK8M,YAAY,GAAG,IAAI;MACxB9M,KAAA,CAAK+M,WAAW,GAAG,IAAI;;MAEvB/M,KAAA,CAAKgN,gBAAgB,GAAG,IAAI;MAC5B,IAAI,OAAO5Q,gBAAgB,KAAK,UAAU,EAAE;QACxC,IAAI4D,KAAA,CAAKzB,IAAI,CAACoO,mBAAmB,EAAE;;;;UAI/B3M,KAAA,CAAKiN,yBAAyB,GAAG,YAAM;YACnC,IAAIjN,KAAA,CAAKuJ,SAAS,EAAE;;cAEhBvJ,KAAA,CAAKuJ,SAAS,CAAC1M,kBAAkB,EAAE;cACnCmD,KAAA,CAAKuJ,SAAS,CAACnI,KAAK,EAAE;;WAE7B;UACDhF,gBAAgB,CAAC,cAAc,EAAE4D,KAAA,CAAKiN,yBAAyB,EAAE,KAAK,CAAC;;QAE3E,IAAIjN,KAAA,CAAKsC,QAAQ,KAAK,WAAW,EAAE;UAC/BtC,KAAA,CAAKkN,oBAAoB,GAAG,YAAM;YAC9BlN,KAAA,CAAKsB,OAAO,CAAC,iBAAiB,EAAE;cAC5BxB,WAAW,EAAE;aAChB,CAAC;WACL;UACD1D,gBAAgB,CAAC,SAAS,EAAE4D,KAAA,CAAKkN,oBAAoB,EAAE,KAAK,CAAC;;;MAGrElN,KAAA,CAAKiB,IAAI,EAAE;MAAC,OAAAjB,KAAA;;;EAGpB;EACA;EACA;EACA;EACA;EACA;IANIY,YAAA,CAAAoL,MAAA;MAAAhX,GAAA;MAAA6L,KAAA,WAAAsM,gBAOgB1D,IAAI,EAAE;QAClB,IAAM/I,KAAK,GAAG6E,QAAA,CAAc,EAAE,EAAE,IAAI,CAAChH,IAAI,CAACmC,KAAK,CAAC;;QAEhDA,KAAK,CAAC0M,GAAG,GAAGpR,QAAQ;;QAEpB0E,KAAK,CAAC6I,SAAS,GAAGE,IAAI;;QAEtB,IAAI,IAAI,CAACmD,EAAE,EACPlM,KAAK,CAAC0E,GAAG,GAAG,IAAI,CAACwH,EAAE;QACvB,IAAMrO,IAAI,GAAGgH,QAAA,CAAc,EAAE,EAAE,IAAI,CAAChH,IAAI,EAAE;UACtCmC,KAAK,EAALA,KAAK;UACLC,MAAM,EAAE,IAAI;UACZ2B,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBG,MAAM,EAAE,IAAI,CAACA,MAAM;UACnBD,IAAI,EAAE,IAAI,CAACA;SACd,EAAE,IAAI,CAACjE,IAAI,CAACiL,gBAAgB,CAACC,IAAI,CAAC,CAAC;QACpC,OAAO,IAAIiB,UAAU,CAACjB,IAAI,CAAC,CAAClL,IAAI,CAAC;;;EAGzC;EACA;EACA;EACA;;MAJIvJ,GAAA;MAAA6L,KAAA,WAAAI,OAKO;QAAA,IAAAV,MAAA;QACH,IAAIgJ,SAAS;QACb,IAAI,IAAI,CAAChL,IAAI,CAAC+N,eAAe,IACzBN,MAAM,CAACqB,qBAAqB,IAC5B,IAAI,CAAC3C,UAAU,CAACnI,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;UAC7CgH,SAAS,GAAG,WAAW;SAC1B,MACI,IAAI,CAAC,KAAK,IAAI,CAACmB,UAAU,CAACjT,MAAM,EAAE;;UAEnC,IAAI,CAACgH,YAAY,CAAC,YAAM;YACpB8B,MAAI,CAACnD,YAAY,CAAC,OAAO,EAAE,yBAAyB,CAAC;WACxD,EAAE,CAAC,CAAC;UACL;SACH,MACI;UACDmM,SAAS,GAAG,IAAI,CAACmB,UAAU,CAAC,CAAC,CAAC;;QAElC,IAAI,CAACxJ,UAAU,GAAG,SAAS;;QAE3B,IAAI;UACAqI,SAAS,GAAG,IAAI,CAAC4D,eAAe,CAAC5D,SAAS,CAAC;SAC9C,CACD,OAAO7F,CAAC,EAAE;UACN,IAAI,CAACgH,UAAU,CAACzP,KAAK,EAAE;UACvB,IAAI,CAACgG,IAAI,EAAE;UACX;;QAEJsI,SAAS,CAACtI,IAAI,EAAE;QAChB,IAAI,CAACqM,YAAY,CAAC/D,SAAS,CAAC;;;EAGpC;EACA;EACA;EACA;;MAJIvU,GAAA;MAAA6L,KAAA,WAAAyM,aAKa/D,SAAS,EAAE;QAAA,IAAA1E,MAAA;QACpB,IAAI,IAAI,CAAC0E,SAAS,EAAE;UAChB,IAAI,CAACA,SAAS,CAAC1M,kBAAkB,EAAE;;;QAGvC,IAAI,CAAC0M,SAAS,GAAGA,SAAS;;QAE1BA,SAAS,CACJpN,EAAE,CAAC,OAAO,EAAE,IAAI,CAACoR,OAAO,CAAC7O,IAAI,CAAC,IAAI,CAAC,CAAC,CACpCvC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAACwF,QAAQ,CAACjD,IAAI,CAAC,IAAI,CAAC,CAAC,CACtCvC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC2E,OAAO,CAACpC,IAAI,CAAC,IAAI,CAAC,CAAC,CACpCvC,EAAE,CAAC,OAAO,EAAE,UAAC0D,MAAM;UAAA,OAAKgF,MAAI,CAACvD,OAAO,CAAC,iBAAiB,EAAEzB,MAAM,CAAC;UAAC;;;EAG7E;EACA;EACA;EACA;EACA;;MALI7K,GAAA;MAAA6L,KAAA,WAAA2M,MAMM/D,IAAI,EAAE;QAAA,IAAA3E,MAAA;QACR,IAAIyE,SAAS,GAAG,IAAI,CAAC4D,eAAe,CAAC1D,IAAI,CAAC;QAC1C,IAAIgE,MAAM,GAAG,KAAK;QAClBzB,MAAM,CAACqB,qBAAqB,GAAG,KAAK;QACpC,IAAMK,eAAe,GAAG,SAAlBA,eAAeA,GAAS;UAC1B,IAAID,MAAM,EACN;UACJlE,SAAS,CAAChI,IAAI,CAAC,CAAC;YAAErM,IAAI,EAAE,MAAM;YAAEC,IAAI,EAAE;WAAS,CAAC,CAAC;UACjDoU,SAAS,CAAC/M,IAAI,CAAC,QAAQ,EAAE,UAACmR,GAAG,EAAK;YAC9B,IAAIF,MAAM,EACN;YACJ,IAAI,MAAM,KAAKE,GAAG,CAACzY,IAAI,IAAI,OAAO,KAAKyY,GAAG,CAACxY,IAAI,EAAE;cAC7C2P,MAAI,CAAC8I,SAAS,GAAG,IAAI;cACrB9I,MAAI,CAAC1H,YAAY,CAAC,WAAW,EAAEmM,SAAS,CAAC;cACzC,IAAI,CAACA,SAAS,EACV;cACJyC,MAAM,CAACqB,qBAAqB,GAAG,WAAW,KAAK9D,SAAS,CAACE,IAAI;cAC7D3E,MAAI,CAACyE,SAAS,CAAC1H,KAAK,CAAC,YAAM;gBACvB,IAAI4L,MAAM,EACN;gBACJ,IAAI,QAAQ,KAAK3I,MAAI,CAAC5D,UAAU,EAC5B;gBACJ+F,OAAO,EAAE;gBACTnC,MAAI,CAACwI,YAAY,CAAC/D,SAAS,CAAC;gBAC5BA,SAAS,CAAChI,IAAI,CAAC,CAAC;kBAAErM,IAAI,EAAE;iBAAW,CAAC,CAAC;gBACrC4P,MAAI,CAAC1H,YAAY,CAAC,SAAS,EAAEmM,SAAS,CAAC;gBACvCA,SAAS,GAAG,IAAI;gBAChBzE,MAAI,CAAC8I,SAAS,GAAG,KAAK;gBACtB9I,MAAI,CAAC+I,KAAK,EAAE;eACf,CAAC;aACL,MACI;cACD,IAAMvK,GAAG,GAAG,IAAInD,KAAK,CAAC,aAAa,CAAC;;cAEpCmD,GAAG,CAACiG,SAAS,GAAGA,SAAS,CAACE,IAAI;cAC9B3E,MAAI,CAAC1H,YAAY,CAAC,cAAc,EAAEkG,GAAG,CAAC;;WAE7C,CAAC;SACL;QACD,SAASwK,eAAeA,GAAG;UACvB,IAAIL,MAAM,EACN;;UAEJA,MAAM,GAAG,IAAI;UACbxG,OAAO,EAAE;UACTsC,SAAS,CAACnI,KAAK,EAAE;UACjBmI,SAAS,GAAG,IAAI;;;QAGpB,IAAML,OAAO,GAAG,SAAVA,OAAOA,CAAI5F,GAAG,EAAK;UACrB,IAAMyK,KAAK,GAAG,IAAI5N,KAAK,CAAC,eAAe,GAAGmD,GAAG,CAAC;;UAE9CyK,KAAK,CAACxE,SAAS,GAAGA,SAAS,CAACE,IAAI;UAChCqE,eAAe,EAAE;UACjBhJ,MAAI,CAAC1H,YAAY,CAAC,cAAc,EAAE2Q,KAAK,CAAC;SAC3C;QACD,SAASC,gBAAgBA,GAAG;UACxB9E,OAAO,CAAC,kBAAkB,CAAC;;;QAG/B,SAASJ,OAAOA,GAAG;UACfI,OAAO,CAAC,eAAe,CAAC;;;QAG5B,SAAS+E,SAASA,CAACC,EAAE,EAAE;UACnB,IAAI3E,SAAS,IAAI2E,EAAE,CAACzE,IAAI,KAAKF,SAAS,CAACE,IAAI,EAAE;YACzCqE,eAAe,EAAE;;;;QAIzB,IAAM7G,OAAO,GAAG,SAAVA,OAAOA,GAAS;UAClBsC,SAAS,CAAC3M,cAAc,CAAC,MAAM,EAAE8Q,eAAe,CAAC;UACjDnE,SAAS,CAAC3M,cAAc,CAAC,OAAO,EAAEsM,OAAO,CAAC;UAC1CK,SAAS,CAAC3M,cAAc,CAAC,OAAO,EAAEoR,gBAAgB,CAAC;UACnDlJ,MAAI,CAACrI,GAAG,CAAC,OAAO,EAAEqM,OAAO,CAAC;UAC1BhE,MAAI,CAACrI,GAAG,CAAC,WAAW,EAAEwR,SAAS,CAAC;SACnC;QACD1E,SAAS,CAAC/M,IAAI,CAAC,MAAM,EAAEkR,eAAe,CAAC;QACvCnE,SAAS,CAAC/M,IAAI,CAAC,OAAO,EAAE0M,OAAO,CAAC;QAChCK,SAAS,CAAC/M,IAAI,CAAC,OAAO,EAAEwR,gBAAgB,CAAC;QACzC,IAAI,CAACxR,IAAI,CAAC,OAAO,EAAEsM,OAAO,CAAC;QAC3B,IAAI,CAACtM,IAAI,CAAC,WAAW,EAAEyR,SAAS,CAAC;QACjC,IAAI,IAAI,CAACpB,QAAQ,CAACtK,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAC5CkH,IAAI,KAAK,cAAc,EAAE;;UAEzB,IAAI,CAAChL,YAAY,CAAC,YAAM;YACpB,IAAI,CAACgP,MAAM,EAAE;cACTlE,SAAS,CAACtI,IAAI,EAAE;;WAEvB,EAAE,GAAG,CAAC;SACV,MACI;UACDsI,SAAS,CAACtI,IAAI,EAAE;;;;EAI5B;EACA;EACA;EACA;;MAJIjM,GAAA;MAAA6L,KAAA,WAAAY,SAKS;QACL,IAAI,CAACP,UAAU,GAAG,MAAM;QACxB8K,MAAM,CAACqB,qBAAqB,GAAG,WAAW,KAAK,IAAI,CAAC9D,SAAS,CAACE,IAAI;QAClE,IAAI,CAACrM,YAAY,CAAC,MAAM,CAAC;QACzB,IAAI,CAACyQ,KAAK,EAAE;;;QAGZ,IAAI,MAAM,KAAK,IAAI,CAAC3M,UAAU,IAAI,IAAI,CAAC3C,IAAI,CAAC8N,OAAO,EAAE;UACjD,IAAI7U,CAAC,GAAG,CAAC;UACT,IAAMyH,CAAC,GAAG,IAAI,CAAC4N,QAAQ,CAACpV,MAAM;UAC9B,OAAOD,CAAC,GAAGyH,CAAC,EAAEzH,CAAC,EAAE,EAAE;YACf,IAAI,CAACgW,KAAK,CAAC,IAAI,CAACX,QAAQ,CAACrV,CAAC,CAAC,CAAC;;;;;EAK5C;EACA;EACA;EACA;;MAJIxC,GAAA;MAAA6L,KAAA,WAAAc,SAKS3K,MAAM,EAAE;QACb,IAAI,SAAS,KAAK,IAAI,CAACkK,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;UAC/B,IAAI,CAAC9D,YAAY,CAAC,QAAQ,EAAEpG,MAAM,CAAC;;UAEnC,IAAI,CAACoG,YAAY,CAAC,WAAW,CAAC;UAC9B,IAAI,CAAC+Q,gBAAgB,EAAE;UACvB,QAAQnX,MAAM,CAAC9B,IAAI;YACf,KAAK,MAAM;cACP,IAAI,CAACkZ,WAAW,CAACC,IAAI,CAACtD,KAAK,CAAC/T,MAAM,CAAC7B,IAAI,CAAC,CAAC;cACzC;YACJ,KAAK,MAAM;cACP,IAAI,CAACmZ,UAAU,CAAC,MAAM,CAAC;cACvB,IAAI,CAAClR,YAAY,CAAC,MAAM,CAAC;cACzB,IAAI,CAACA,YAAY,CAAC,MAAM,CAAC;cACzB;YACJ,KAAK,OAAO;cACR,IAAMkG,GAAG,GAAG,IAAInD,KAAK,CAAC,cAAc,CAAC;;cAErCmD,GAAG,CAACiL,IAAI,GAAGvX,MAAM,CAAC7B,IAAI;cACtB,IAAI,CAAC2L,OAAO,CAACwC,GAAG,CAAC;cACjB;YACJ,KAAK,SAAS;cACV,IAAI,CAAClG,YAAY,CAAC,MAAM,EAAEpG,MAAM,CAAC7B,IAAI,CAAC;cACtC,IAAI,CAACiI,YAAY,CAAC,SAAS,EAAEpG,MAAM,CAAC7B,IAAI,CAAC;cACzC;;;;;EAOpB;EACA;EACA;EACA;EACA;;MALIH,GAAA;MAAA6L,KAAA,WAAAuN,YAMYjZ,IAAI,EAAE;QACd,IAAI,CAACiI,YAAY,CAAC,WAAW,EAAEjI,IAAI,CAAC;QACpC,IAAI,CAACyX,EAAE,GAAGzX,IAAI,CAACiQ,GAAG;QAClB,IAAI,CAACmE,SAAS,CAAC7I,KAAK,CAAC0E,GAAG,GAAGjQ,IAAI,CAACiQ,GAAG;QACnC,IAAI,CAACyH,QAAQ,GAAG,IAAI,CAAC2B,cAAc,CAACrZ,IAAI,CAAC0X,QAAQ,CAAC;QAClD,IAAI,CAACC,YAAY,GAAG3X,IAAI,CAAC2X,YAAY;QACrC,IAAI,CAACC,WAAW,GAAG5X,IAAI,CAAC4X,WAAW;QACnC,IAAI,CAAC1R,UAAU,GAAGlG,IAAI,CAACkG,UAAU;QACjC,IAAI,CAACoG,MAAM,EAAE;;QAEb,IAAI,QAAQ,KAAK,IAAI,CAACP,UAAU,EAC5B;QACJ,IAAI,CAACiN,gBAAgB,EAAE;;;EAG/B;EACA;EACA;EACA;;MAJInZ,GAAA;MAAA6L,KAAA,WAAAsN,mBAKmB;QAAA,IAAApJ,MAAA;QACf,IAAI,CAACpG,cAAc,CAAC,IAAI,CAACqO,gBAAgB,CAAC;QAC1C,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACvO,YAAY,CAAC,YAAM;UAC5CsG,MAAI,CAACzD,OAAO,CAAC,cAAc,CAAC;SAC/B,EAAE,IAAI,CAACwL,YAAY,GAAG,IAAI,CAACC,WAAW,CAAC;QACxC,IAAI,IAAI,CAACxO,IAAI,CAACoK,SAAS,EAAE;UACrB,IAAI,CAACqE,gBAAgB,CAACnE,KAAK,EAAE;;;;EAIzC;EACA;EACA;EACA;;MAJI7T,GAAA;MAAA6L,KAAA,WAAA0M,UAKU;QACN,IAAI,CAACtB,WAAW,CAAChP,MAAM,CAAC,CAAC,EAAE,IAAI,CAACkP,aAAa,CAAC;;;;QAI9C,IAAI,CAACA,aAAa,GAAG,CAAC;QACtB,IAAI,CAAC,KAAK,IAAI,CAACF,WAAW,CAACxU,MAAM,EAAE;UAC/B,IAAI,CAAC2F,YAAY,CAAC,OAAO,CAAC;SAC7B,MACI;UACD,IAAI,CAACyQ,KAAK,EAAE;;;;EAIxB;EACA;EACA;EACA;;MAJI7Y,GAAA;MAAA6L,KAAA,WAAAgN,QAKQ;QACJ,IAAI,QAAQ,KAAK,IAAI,CAAC3M,UAAU,IAC5B,IAAI,CAACqI,SAAS,CAAC/I,QAAQ,IACvB,CAAC,IAAI,CAACoN,SAAS,IACf,IAAI,CAAC3B,WAAW,CAACxU,MAAM,EAAE;UACzB,IAAM0B,OAAO,GAAG,IAAI,CAACsV,kBAAkB,EAAE;UACzC,IAAI,CAAClF,SAAS,CAAChI,IAAI,CAACpI,OAAO,CAAC;;;UAG5B,IAAI,CAACgT,aAAa,GAAGhT,OAAO,CAAC1B,MAAM;UACnC,IAAI,CAAC2F,YAAY,CAAC,OAAO,CAAC;;;;EAItC;EACA;EACA;EACA;EACA;;MALIpI,GAAA;MAAA6L,KAAA,WAAA4N,qBAMqB;QACjB,IAAMC,sBAAsB,GAAG,IAAI,CAACrT,UAAU,IAC1C,IAAI,CAACkO,SAAS,CAACE,IAAI,KAAK,SAAS,IACjC,IAAI,CAACwC,WAAW,CAACxU,MAAM,GAAG,CAAC;QAC/B,IAAI,CAACiX,sBAAsB,EAAE;UACzB,OAAO,IAAI,CAACzC,WAAW;;QAE3B,IAAI0C,WAAW,GAAG,CAAC,CAAC;QACpB,KAAK,IAAInX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACyU,WAAW,CAACxU,MAAM,EAAED,CAAC,EAAE,EAAE;UAC9C,IAAMrC,IAAI,GAAG,IAAI,CAAC8W,WAAW,CAACzU,CAAC,CAAC,CAACrC,IAAI;UACrC,IAAIA,IAAI,EAAE;YACNwZ,WAAW,IAAI9X,UAAU,CAAC1B,IAAI,CAAC;;UAEnC,IAAIqC,CAAC,GAAG,CAAC,IAAImX,WAAW,GAAG,IAAI,CAACtT,UAAU,EAAE;YACxC,OAAO,IAAI,CAAC4Q,WAAW,CAAC9Q,KAAK,CAAC,CAAC,EAAE3D,CAAC,CAAC;;UAEvCmX,WAAW,IAAI,CAAC,CAAC;;;QAErB,OAAO,IAAI,CAAC1C,WAAW;;;EAG/B;EACA;EACA;EACA;EACA;EACA;EACA;;MAPIjX,GAAA;MAAA6L,KAAA,WAAAW,MAQMmM,GAAG,EAAEiB,OAAO,EAAEtS,EAAE,EAAE;QACpB,IAAI,CAACgS,UAAU,CAAC,SAAS,EAAEX,GAAG,EAAEiB,OAAO,EAAEtS,EAAE,CAAC;QAC5C,OAAO,IAAI;;;MACdtH,GAAA;MAAA6L,KAAA,WAAAU,KACIoM,GAAG,EAAEiB,OAAO,EAAEtS,EAAE,EAAE;QACnB,IAAI,CAACgS,UAAU,CAAC,SAAS,EAAEX,GAAG,EAAEiB,OAAO,EAAEtS,EAAE,CAAC;QAC5C,OAAO,IAAI;;;EAGnB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;MARItH,GAAA;MAAA6L,KAAA,WAAAyN,WASWpZ,IAAI,EAAEC,IAAI,EAAEyZ,OAAO,EAAEtS,EAAE,EAAE;QAChC,IAAI,UAAU,KAAK,OAAOnH,IAAI,EAAE;UAC5BmH,EAAE,GAAGnH,IAAI;UACTA,IAAI,GAAG8M,SAAS;;QAEpB,IAAI,UAAU,KAAK,OAAO2M,OAAO,EAAE;UAC/BtS,EAAE,GAAGsS,OAAO;UACZA,OAAO,GAAG,IAAI;;QAElB,IAAI,SAAS,KAAK,IAAI,CAAC1N,UAAU,IAAI,QAAQ,KAAK,IAAI,CAACA,UAAU,EAAE;UAC/D;;QAEJ0N,OAAO,GAAGA,OAAO,IAAI,EAAE;QACvBA,OAAO,CAACC,QAAQ,GAAG,KAAK,KAAKD,OAAO,CAACC,QAAQ;QAC7C,IAAM7X,MAAM,GAAG;UACX9B,IAAI,EAAEA,IAAI;UACVC,IAAI,EAAEA,IAAI;UACVyZ,OAAO,EAAEA;SACZ;QACD,IAAI,CAACxR,YAAY,CAAC,cAAc,EAAEpG,MAAM,CAAC;QACzC,IAAI,CAACiV,WAAW,CAACtS,IAAI,CAAC3C,MAAM,CAAC;QAC7B,IAAIsF,EAAE,EACF,IAAI,CAACE,IAAI,CAAC,OAAO,EAAEF,EAAE,CAAC;QAC1B,IAAI,CAACuR,KAAK,EAAE;;;EAGpB;EACA;;MAFI7Y,GAAA;MAAA6L,KAAA,WAAAO,QAGQ;QAAA,IAAAqE,MAAA;QACJ,IAAMrE,KAAK,GAAG,SAARA,KAAKA,GAAS;UAChBqE,MAAI,CAACnE,OAAO,CAAC,cAAc,CAAC;UAC5BmE,MAAI,CAAC8D,SAAS,CAACnI,KAAK,EAAE;SACzB;QACD,IAAM0N,eAAe,GAAG,SAAlBA,eAAeA,GAAS;UAC1BrJ,MAAI,CAAChJ,GAAG,CAAC,SAAS,EAAEqS,eAAe,CAAC;UACpCrJ,MAAI,CAAChJ,GAAG,CAAC,cAAc,EAAEqS,eAAe,CAAC;UACzC1N,KAAK,EAAE;SACV;QACD,IAAM2N,cAAc,GAAG,SAAjBA,cAAcA,GAAS;;UAEzBtJ,MAAI,CAACjJ,IAAI,CAAC,SAAS,EAAEsS,eAAe,CAAC;UACrCrJ,MAAI,CAACjJ,IAAI,CAAC,cAAc,EAAEsS,eAAe,CAAC;SAC7C;QACD,IAAI,SAAS,KAAK,IAAI,CAAC5N,UAAU,IAAI,MAAM,KAAK,IAAI,CAACA,UAAU,EAAE;UAC7D,IAAI,CAACA,UAAU,GAAG,SAAS;UAC3B,IAAI,IAAI,CAAC+K,WAAW,CAACxU,MAAM,EAAE;YACzB,IAAI,CAAC+E,IAAI,CAAC,OAAO,EAAE,YAAM;cACrB,IAAIiJ,MAAI,CAACmI,SAAS,EAAE;gBAChBmB,cAAc,EAAE;eACnB,MACI;gBACD3N,KAAK,EAAE;;aAEd,CAAC;WACL,MACI,IAAI,IAAI,CAACwM,SAAS,EAAE;YACrBmB,cAAc,EAAE;WACnB,MACI;YACD3N,KAAK,EAAE;;;QAGf,OAAO,IAAI;;;EAGnB;EACA;EACA;EACA;;MAJIpM,GAAA;MAAA6L,KAAA,WAAAC,QAKQwC,GAAG,EAAE;QACT0I,MAAM,CAACqB,qBAAqB,GAAG,KAAK;QACpC,IAAI,CAACjQ,YAAY,CAAC,OAAO,EAAEkG,GAAG,CAAC;QAC/B,IAAI,CAAChC,OAAO,CAAC,iBAAiB,EAAEgC,GAAG,CAAC;;;EAG5C;EACA;EACA;EACA;;MAJItO,GAAA;MAAA6L,KAAA,WAAAS,QAKQzB,MAAM,EAAEC,WAAW,EAAE;QACzB,IAAI,SAAS,KAAK,IAAI,CAACoB,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;;UAE/B,IAAI,CAACvC,cAAc,CAAC,IAAI,CAACqO,gBAAgB,CAAC;;UAE1C,IAAI,CAACzD,SAAS,CAAC1M,kBAAkB,CAAC,OAAO,CAAC;;UAE1C,IAAI,CAAC0M,SAAS,CAACnI,KAAK,EAAE;;UAEtB,IAAI,CAACmI,SAAS,CAAC1M,kBAAkB,EAAE;UACnC,IAAI,OAAOC,mBAAmB,KAAK,UAAU,EAAE;YAC3CA,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACmQ,yBAAyB,EAAE,KAAK,CAAC;YAC1EnQ,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACoQ,oBAAoB,EAAE,KAAK,CAAC;;;UAGpE,IAAI,CAAChM,UAAU,GAAG,QAAQ;;UAE1B,IAAI,CAAC0L,EAAE,GAAG,IAAI;;UAEd,IAAI,CAACxP,YAAY,CAAC,OAAO,EAAEyC,MAAM,EAAEC,WAAW,CAAC;;;UAG/C,IAAI,CAACmM,WAAW,GAAG,EAAE;UACrB,IAAI,CAACE,aAAa,GAAG,CAAC;;;;EAIlC;EACA;EACA;EACA;EACA;;MALInX,GAAA;MAAA6L,KAAA,WAAA2N,eAMe3B,QAAQ,EAAE;QACrB,IAAMmC,gBAAgB,GAAG,EAAE;QAC3B,IAAIxX,CAAC,GAAG,CAAC;QACT,IAAM0D,CAAC,GAAG2R,QAAQ,CAACpV,MAAM;QACzB,OAAOD,CAAC,GAAG0D,CAAC,EAAE1D,CAAC,EAAE,EAAE;UACf,IAAI,CAAC,IAAI,CAACkT,UAAU,CAACnI,OAAO,CAACsK,QAAQ,CAACrV,CAAC,CAAC,CAAC,EACrCwX,gBAAgB,CAACrV,IAAI,CAACkT,QAAQ,CAACrV,CAAC,CAAC,CAAC;;QAE1C,OAAOwX,gBAAgB;;;IAC1B,OAAAhD,MAAA;EAAA,EAxkBuB/P,OAAO;EA0kBnC+P,MAAM,CAAChQ,QAAQ,GAAGA,QAAQ;;AChlB1B,2BAAe,UAACiJ,GAAG,EAAE1G,IAAI;IAAA,OAAK,IAAIyN,MAAM,CAAC/G,GAAG,EAAE1G,IAAI,CAAC;EAAA;;;;;;;;"}