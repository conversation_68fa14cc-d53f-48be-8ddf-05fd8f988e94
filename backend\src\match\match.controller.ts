import { BadRequestException, Controller, Get, Param, Post, Query, Request } from '@nestjs/common';
import { MatchService } from './match.service';
import { RequestWithUser } from 'src/common/interfaces/request-with-user.interface';
import { MatchResponseDto } from './dto/match-response.dto';
import { MatchDocument, MatchStatus } from './schemas/match.schema';
import { GetMatchesRequestDto } from './dto/match-request.dto';
import { PaginatedResponseDto } from 'src/common/dto/pagination.dto';

@Controller('match')
export class MatchController {
  constructor(
    private readonly matchService: MatchService
  ) {}

  /**
   * Calculate age from birthdate
   * @param birthDate - The birthdate to calculate age from
   * @returns The calculated age as a number
   */
  private calculateAge(birthDate: Date): number {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }

  /**
   * Check if a user is connected to a match
   * 
   * @param userId - User ID
   * @param match - Match document
   * @returns boolean
   */
  private isConnected(userId: string, match: MatchDocument): boolean {
    return match.status === MatchStatus.ACCEPTED && (match.invitedBy?.id.toString() === userId || match.acceptedBy?.id.toString() === userId);
  }

  /**
   * Build match response DTO
   * 
   * @param userId - User ID
   * @param match - Match document
   * @returns Match response DTO
   */
  private buildMatchResponseDto(userId: string, match: MatchDocument): MatchResponseDto {
    let user: any;
    
    if (match.user1Id.id.toString() === userId) {
      user = match.user2Id as any;
    } else if (match.user2Id.id.toString() === userId) {
      user = match.user1Id as any;
    } else {
      throw new BadRequestException(`User with ID ${userId} is not part of this match`);
    }

    return {
      id: match.id,
      user: {
        id: match.user2Id.id.toString(),
        name: user.name,
        image: user.image?.url || null,
        religion: user.religion,
        gender: user.gender,
        age: this.calculateAge(user.dateOfBirth),
        occupation: user.occupation,
        isSmoker: user.isSmoker,
        acceptDifferentReligion: user.acceptDifferentReligion,
        about: user.about
      },
      matchScore: match.matchScore,
      matchDetails: match.matchDetails,
      interpretation: this.matchService.getMatchInterpretation(match.matchScore),
      invitedBy: match.invitedBy?._id.toString() || null,
      acceptedBy: match.acceptedBy?._id.toString() || null,
      rejectedBy: match.rejectedBy?._id.toString() || null,
      status: match.status,
      isConnected: this.isConnected(userId, match)
    }
  }

  /**
   * Get matches by user ID
   * 
   * @param req - Request object
   * @param query - Query object
   * @returns Array of match response DTOs
   */
  @Get()
  async getMatches(
    @Request() req: RequestWithUser,
    @Query() query: GetMatchesRequestDto
  ): Promise<PaginatedResponseDto<MatchResponseDto>> {
    const matches = await this.matchService.findMatchesByUserId(req.user.userId, query);

    return {
      data: matches.data.map(match => this.buildMatchResponseDto(req.user.userId, match)),
      totalItems: matches.totalItems,
      itemsPerPage: matches.itemsPerPage,
      totalPages: matches.totalPages,
      currentPage: matches.currentPage
    }
  }

  /**
   * Invite a user to a match
   * 
   * @param req - Request object
   * @param matchId - Match ID
   * @returns Promise<{ message: string }>
   */
  @Post('/:matchId/invite')
  async invite(@Request() req: RequestWithUser, @Param('matchId') matchId: string): Promise<{ message: string }> {
    try {
      await this.matchService.invite(matchId, req.user.userId);

      return {
        message: 'Match invited successfully'
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Accept a match invitation
   * 
   * @param req - Request object
   * @param matchId - Match ID
   * @returns Promise<{ message: string }>
   */
  @Post('/:matchId/accept')
  async accept(@Request() req: RequestWithUser, @Param('matchId') matchId: string): Promise<{ message: string }> {
    try {
      await this.matchService.accept(matchId, req.user.userId);

      return {
        message: 'Match accepted successfully'
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Reject a match invitation
   * 
   * @param req - Request object
   * @param matchId - Match ID
   * @returns Promise<{ message: string }>
   */
  @Post('/:matchId/reject')
  async reject(@Request() req: RequestWithUser, @Param('matchId') matchId: string): Promise<{ message: string }> {
    try {
      await this.matchService.reject(matchId, req.user.userId);

      return {
        message: 'Match rejected successfully'
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('/:matchId/accept/:userId')
  async acceptTest(@Param('userId') userId: string, @Param('matchId') matchId: string): Promise<{ message: string }> {
    try {
      await this.matchService.accept(matchId, userId);

      return {
        message: 'Match accepted successfully'
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('/:matchId/reject/:userId')
  async rejectTest(@Param('userId') userId: string, @Param('matchId') matchId: string): Promise<{ message: string }> {
    try {
      await this.matchService.reject(matchId, userId);

      return {
        message: 'Match rejected successfully'
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
