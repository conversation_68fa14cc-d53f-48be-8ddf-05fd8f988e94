{"version": 3, "file": "socket.io.js", "sources": ["../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../node_modules/engine.io-client/build/esm/globalThis.browser.js", "../node_modules/engine.io-client/build/esm/util.js", "../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../node_modules/engine.io-client/build/esm/transport.js", "../node_modules/engine.io-client/build/esm/contrib/yeast.js", "../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../node_modules/engine.io-client/build/esm/transports/xmlhttprequest.browser.js", "../node_modules/engine.io-client/build/esm/transports/polling.js", "../node_modules/engine.io-client/build/esm/transports/websocket-constructor.browser.js", "../node_modules/engine.io-client/build/esm/transports/websocket.js", "../node_modules/engine.io-client/build/esm/transports/webtransport.js", "../node_modules/engine.io-client/build/esm/transports/index.js", "../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../node_modules/engine.io-client/build/esm/socket.js", "../node_modules/engine.io-client/build/esm/index.js", "../build/esm/url.js", "../node_modules/socket.io-parser/build/esm/is-binary.js", "../node_modules/socket.io-parser/build/esm/binary.js", "../node_modules/socket.io-parser/build/esm/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data\n            .arrayBuffer()\n            .then(toArray)\n            .then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, encoded => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, encodedPacket => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        }\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else if (state === 2 /* READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        }\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\nexport function createCookieJar() { }\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { createCookieJar, XHR as XMLHttpRequest, } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n        if (this.opts.withCredentials) {\n            this.cookieJar = createCookieJar();\n        }\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, cookieJar: this.cookieJar }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        var _a;\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, true);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(xhr);\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"./websocket-constructor.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        // @ts-ignore\n        if (typeof WebTransport !== \"function\") {\n            return;\n        }\n        // @ts-ignore\n        this.transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        this.transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this.transport.ready.then(() => {\n            this.transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this.writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this.writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this.writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this.transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: Polling,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { defaultBinaryType } from \"./transports/websocket-constructor.js\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\n            \"polling\",\n            \"websocket\",\n            \"webtransport\",\n        ];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this.upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            this.resetPingTimeout();\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n        }\n        else if (this.connected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        this.acks[id] = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, [null, ...args]);\n        };\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        // the timeout flag is optional\n        const withErr = this.flags.timeout !== undefined || this._opts.ackTimeout !== undefined;\n        return new Promise((resolve, reject) => {\n            args.push((arg1, arg2) => {\n                if (withErr) {\n                    return arg1 ? reject(arg1) : resolve(arg2);\n                }\n                else {\n                    return resolve(arg1);\n                }\n            });\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "_ref", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "encodePacketToBinary", "packet", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "chars", "lookup", "i", "length", "charCodeAt", "decode", "base64", "bufferLength", "len", "p", "encoded1", "encoded2", "encoded3", "encoded4", "arraybuffer", "bytes", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "packetType", "decoded", "SEPARATOR", "String", "fromCharCode", "encodePayload", "packets", "encodedPackets", "Array", "count", "join", "decodePayload", "encodedPayload", "decodedPacket", "push", "createPacketEncoderStream", "TransformStream", "transform", "controller", "payloadLength", "header", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "TEXT_DECODER", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "createPacketDecoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "Math", "pow", "protocol", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "_callbacks", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "callbacks", "cb", "splice", "emit", "args", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "_len", "attr", "_key", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "BASE64_OVERHEAD", "utf8Length", "ceil", "str", "c", "l", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "TransportError", "_Error", "_inherits", "_super", "_createSuper", "reason", "description", "context", "_this", "_classCallCheck", "_createClass", "_wrapNativeSuper", "Error", "Transport", "_Emitter", "_super2", "_this2", "writable", "_assertThisInitialized", "query", "socket", "value", "onError", "_get", "_getPrototypeOf", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "write", "onOpen", "onData", "onPacket", "details", "pause", "onPause", "createUri", "schema", "undefined", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "alphabet", "map", "seed", "prev", "num", "floor", "yeast", "now", "Date", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "createCookieJar", "empty", "hasXHR2", "xhr", "responseType", "Polling", "_Transport", "polling", "location", "isSSL", "xd", "forceBase64", "withCredentials", "cookieJar", "get", "poll", "total", "doPoll", "_this3", "_this4", "_this5", "doWrite", "uri", "timestampRequests", "timestampParam", "sid", "b64", "request", "_extends", "Request", "_this6", "req", "method", "xhrStatus", "_this7", "pollXhr", "_this8", "_this9", "_a", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "addCookies", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "status", "onLoad", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "terminationEvent", "nextTick", "isPromiseAvailable", "Promise", "resolve", "WebSocket", "MozWebSocket", "usingBrowserWebSocket", "defaultBinaryType", "isReactNative", "navigator", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "_loop", "lastPacket", "WT", "WebTransport", "transport", "transportOptions", "name", "closed", "ready", "createBidirectionalStream", "stream", "decoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "writer", "getWriter", "read", "done", "transports", "websocket", "webtransport", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "query<PERSON><PERSON>", "regx", "names", "$0", "$1", "$2", "Socket", "writeBuffer", "_typeof", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "offlineEventListener", "createTransport", "EIO", "priorWebsocketSuccess", "setTransport", "onDrain", "probe", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "resetPingTimeout", "onHandshake", "JSON", "sendPacket", "code", "filterUpgrades", "getWritablePackets", "shouldCheckPayloadSize", "payloadSize", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "url", "loc", "test", "ipv6", "href", "withNativeFile", "File", "hasBinary", "toJSON", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "newData", "reconstructPacket", "_reconstructPacket", "isIndexValid", "RESERVED_EVENTS", "PacketType", "Encoder", "replacer", "EVENT", "ACK", "encodeAsBinary", "BINARY_EVENT", "BINARY_ACK", "nsp", "encodeAsString", "stringify", "deconstruction", "unshift", "isObject", "Decoder", "reviver", "add", "reconstructor", "decodeString", "isBinaryEvent", "BinaryReconstructor", "takeBinaryData", "start", "buf", "next", "payload", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "destroy", "finishedReconstruction", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "reconPack", "binData", "subDestroy", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_opts", "_autoConnect", "subEvents", "subs", "onpacket", "_readyState", "_len2", "_key2", "retries", "fromQueue", "_addToQueue", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "discardPacket", "notifyOutgoingListeners", "ackTimeout", "timer", "_len3", "_key3", "emitWithAck", "_len4", "_key4", "withErr", "reject", "arg1", "arg2", "tryCount", "pending", "<PERSON><PERSON><PERSON><PERSON>", "_len5", "responseArgs", "_key5", "_drainQueue", "force", "_packet", "_sendConnectPacket", "_pid", "pid", "offset", "_lastOffset", "sameNamespace", "onconnect", "onevent", "onack", "ondisconnect", "message", "emitEvent", "_anyListeners", "_iterator", "_createForOfIteratorHelper", "_step", "s", "listener", "f", "sent", "_len6", "_key6", "emitBuffered", "onAny", "prependAny", "offAny", "listenersAny", "onAnyOutgoing", "_anyOutgoingListeners", "prependAnyOutgoing", "offAnyOutgoing", "listenersAnyOutgoing", "_iterator2", "_step2", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "rand", "random", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "nsps", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "decoder", "autoConnect", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "maybeReconnectOnOpen", "_reconnecting", "reconnect", "Engine", "skipReconnect", "openSubDestroy", "errorSub", "onping", "ondata", "ondecoded", "active", "_destroy", "_i", "_nsps", "_close", "delay", "onreconnect", "attempt", "cache", "parsed", "newConnection", "forceNew", "multiplex"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA,IAAMA,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EACzCF,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1BA,YAAY,CAAC,OAAO,CAAC,GAAG,GAAG,CAAA;EAC3BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1BA,YAAY,CAAC,SAAS,CAAC,GAAG,GAAG,CAAA;EAC7BA,YAAY,CAAC,SAAS,CAAC,GAAG,GAAG,CAAA;EAC7BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;EAC1B,IAAMG,oBAAoB,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAA;EAChDD,MAAM,CAACG,IAAI,CAACJ,YAAY,CAAC,CAACK,OAAO,CAAC,UAAAC,GAAG,EAAI;EACrCH,EAAAA,oBAAoB,CAACH,YAAY,CAACM,GAAG,CAAC,CAAC,GAAGA,GAAG,CAAA;EACjD,CAAC,CAAC,CAAA;EACF,IAAMC,YAAY,GAAG;EAAEC,EAAAA,IAAI,EAAE,OAAO;EAAEC,EAAAA,IAAI,EAAE,cAAA;EAAe,CAAC;;ECX5D,IAAMC,gBAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBV,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACH,IAAI,CAAC,KAAK,0BAA2B,CAAA;EAC5E,IAAMI,uBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU,CAAA;EAC/D;EACA,IAAMC,QAAM,GAAG,SAATA,MAAMA,CAAGC,GAAG,EAAI;IAClB,OAAO,OAAOF,WAAW,CAACC,MAAM,KAAK,UAAU,GACzCD,WAAW,CAACC,MAAM,CAACC,GAAG,CAAC,GACvBA,GAAG,IAAIA,GAAG,CAACC,MAAM,YAAYH,WAAW,CAAA;EAClD,CAAC,CAAA;EACD,IAAMI,YAAY,GAAG,SAAfA,YAAYA,CAAAC,IAAA,EAAoBC,cAAc,EAAEC,QAAQ,EAAK;EAAA,EAAA,IAA3Cf,IAAI,GAAAa,IAAA,CAAJb,IAAI;MAAEC,IAAI,GAAAY,IAAA,CAAJZ,IAAI,CAAA;EAC9B,EAAA,IAAIC,gBAAc,IAAID,IAAI,YAAYE,IAAI,EAAE;EACxC,IAAA,IAAIW,cAAc,EAAE;QAChB,OAAOC,QAAQ,CAACd,IAAI,CAAC,CAAA;EACzB,KAAC,MACI;EACD,MAAA,OAAOe,kBAAkB,CAACf,IAAI,EAAEc,QAAQ,CAAC,CAAA;EAC7C,KAAA;EACJ,GAAC,MACI,IAAIR,uBAAqB,KACzBN,IAAI,YAAYO,WAAW,IAAIC,QAAM,CAACR,IAAI,CAAC,CAAC,EAAE;EAC/C,IAAA,IAAIa,cAAc,EAAE;QAChB,OAAOC,QAAQ,CAACd,IAAI,CAAC,CAAA;EACzB,KAAC,MACI;QACD,OAAOe,kBAAkB,CAAC,IAAIb,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,EAAEc,QAAQ,CAAC,CAAA;EACzD,KAAA;EACJ,GAAA;EACA;IACA,OAAOA,QAAQ,CAACvB,YAAY,CAACQ,IAAI,CAAC,IAAIC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAA;EACtD,CAAC,CAAA;EACD,IAAMe,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIf,IAAI,EAAEc,QAAQ,EAAK;EAC3C,EAAA,IAAME,UAAU,GAAG,IAAIC,UAAU,EAAE,CAAA;IACnCD,UAAU,CAACE,MAAM,GAAG,YAAY;EAC5B,IAAA,IAAMC,OAAO,GAAGH,UAAU,CAACI,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;EAC/CP,IAAAA,QAAQ,CAAC,GAAG,IAAIK,OAAO,IAAI,EAAE,CAAC,CAAC,CAAA;KAClC,CAAA;EACD,EAAA,OAAOH,UAAU,CAACM,aAAa,CAACtB,IAAI,CAAC,CAAA;EACzC,CAAC,CAAA;EACD,SAASuB,OAAOA,CAACvB,IAAI,EAAE;IACnB,IAAIA,IAAI,YAAYwB,UAAU,EAAE;EAC5B,IAAA,OAAOxB,IAAI,CAAA;EACf,GAAC,MACI,IAAIA,IAAI,YAAYO,WAAW,EAAE;EAClC,IAAA,OAAO,IAAIiB,UAAU,CAACxB,IAAI,CAAC,CAAA;EAC/B,GAAC,MACI;EACD,IAAA,OAAO,IAAIwB,UAAU,CAACxB,IAAI,CAACU,MAAM,EAAEV,IAAI,CAACyB,UAAU,EAAEzB,IAAI,CAAC0B,UAAU,CAAC,CAAA;EACxE,GAAA;EACJ,CAAA;EACA,IAAIC,YAAY,CAAA;EACT,SAASC,oBAAoBA,CAACC,MAAM,EAAEf,QAAQ,EAAE;EACnD,EAAA,IAAIb,gBAAc,IAAI4B,MAAM,CAAC7B,IAAI,YAAYE,IAAI,EAAE;EAC/C,IAAA,OAAO2B,MAAM,CAAC7B,IAAI,CACb8B,WAAW,EAAE,CACbC,IAAI,CAACR,OAAO,CAAC,CACbQ,IAAI,CAACjB,QAAQ,CAAC,CAAA;EACvB,GAAC,MACI,IAAIR,uBAAqB,KACzBuB,MAAM,CAAC7B,IAAI,YAAYO,WAAW,IAAIC,QAAM,CAACqB,MAAM,CAAC7B,IAAI,CAAC,CAAC,EAAE;MAC7D,OAAOc,QAAQ,CAACS,OAAO,CAACM,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAA;EACzC,GAAA;EACAW,EAAAA,YAAY,CAACkB,MAAM,EAAE,KAAK,EAAE,UAAAG,OAAO,EAAI;MACnC,IAAI,CAACL,YAAY,EAAE;EACfA,MAAAA,YAAY,GAAG,IAAIM,WAAW,EAAE,CAAA;EACpC,KAAA;EACAnB,IAAAA,QAAQ,CAACa,YAAY,CAACO,MAAM,CAACF,OAAO,CAAC,CAAC,CAAA;EAC1C,GAAC,CAAC,CAAA;EACN;;ECrEA;EACA,IAAMG,KAAK,GAAG,kEAAkE,CAAA;EAChF;EACA,IAAMC,QAAM,GAAG,OAAOZ,UAAU,KAAK,WAAW,GAAG,EAAE,GAAG,IAAIA,UAAU,CAAC,GAAG,CAAC,CAAA;EAC3E,KAAK,IAAIa,GAAC,GAAG,CAAC,EAAEA,GAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,GAAC,EAAE,EAAE;IACnCD,QAAM,CAACD,KAAK,CAACI,UAAU,CAACF,GAAC,CAAC,CAAC,GAAGA,GAAC,CAAA;EACnC,CAAA;EAiBO,IAAMG,QAAM,GAAG,SAATA,MAAMA,CAAIC,MAAM,EAAK;EAC9B,EAAA,IAAIC,YAAY,GAAGD,MAAM,CAACH,MAAM,GAAG,IAAI;MAAEK,GAAG,GAAGF,MAAM,CAACH,MAAM;MAAED,CAAC;EAAEO,IAAAA,CAAC,GAAG,CAAC;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC,QAAQ,CAAA;IAC9G,IAAIP,MAAM,CAACA,MAAM,CAACH,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;EACnCI,IAAAA,YAAY,EAAE,CAAA;MACd,IAAID,MAAM,CAACA,MAAM,CAACH,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;EACnCI,MAAAA,YAAY,EAAE,CAAA;EAClB,KAAA;EACJ,GAAA;EACA,EAAA,IAAMO,WAAW,GAAG,IAAI1C,WAAW,CAACmC,YAAY,CAAC;EAAEQ,IAAAA,KAAK,GAAG,IAAI1B,UAAU,CAACyB,WAAW,CAAC,CAAA;IACtF,KAAKZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,GAAG,EAAEN,CAAC,IAAI,CAAC,EAAE;MACzBQ,QAAQ,GAAGT,QAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,CAAC,CAAC,CAAA;MACvCS,QAAQ,GAAGV,QAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;MAC3CU,QAAQ,GAAGX,QAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;MAC3CW,QAAQ,GAAGZ,QAAM,CAACK,MAAM,CAACF,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;MAC3Ca,KAAK,CAACN,CAAC,EAAE,CAAC,GAAIC,QAAQ,IAAI,CAAC,GAAKC,QAAQ,IAAI,CAAE,CAAA;EAC9CI,IAAAA,KAAK,CAACN,CAAC,EAAE,CAAC,GAAI,CAACE,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAKC,QAAQ,IAAI,CAAE,CAAA;EACrDG,IAAAA,KAAK,CAACN,CAAC,EAAE,CAAC,GAAI,CAACG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAKC,QAAQ,GAAG,EAAG,CAAA;EACxD,GAAA;EACA,EAAA,OAAOC,WAAW,CAAA;EACtB,CAAC;;ECxCD,IAAM3C,uBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU,CAAA;EACxD,IAAM4C,YAAY,GAAG,SAAfA,YAAYA,CAAIC,aAAa,EAAEC,UAAU,EAAK;EACvD,EAAA,IAAI,OAAOD,aAAa,KAAK,QAAQ,EAAE;MACnC,OAAO;EACHrD,MAAAA,IAAI,EAAE,SAAS;EACfC,MAAAA,IAAI,EAAEsD,SAAS,CAACF,aAAa,EAAEC,UAAU,CAAA;OAC5C,CAAA;EACL,GAAA;EACA,EAAA,IAAMtD,IAAI,GAAGqD,aAAa,CAACG,MAAM,CAAC,CAAC,CAAC,CAAA;IACpC,IAAIxD,IAAI,KAAK,GAAG,EAAE;MACd,OAAO;EACHA,MAAAA,IAAI,EAAE,SAAS;QACfC,IAAI,EAAEwD,kBAAkB,CAACJ,aAAa,CAACK,SAAS,CAAC,CAAC,CAAC,EAAEJ,UAAU,CAAA;OAClE,CAAA;EACL,GAAA;EACA,EAAA,IAAMK,UAAU,GAAGhE,oBAAoB,CAACK,IAAI,CAAC,CAAA;IAC7C,IAAI,CAAC2D,UAAU,EAAE;EACb,IAAA,OAAO5D,YAAY,CAAA;EACvB,GAAA;EACA,EAAA,OAAOsD,aAAa,CAACd,MAAM,GAAG,CAAC,GACzB;EACEvC,IAAAA,IAAI,EAAEL,oBAAoB,CAACK,IAAI,CAAC;EAChCC,IAAAA,IAAI,EAAEoD,aAAa,CAACK,SAAS,CAAC,CAAC,CAAA;EACnC,GAAC,GACC;MACE1D,IAAI,EAAEL,oBAAoB,CAACK,IAAI,CAAA;KAClC,CAAA;EACT,CAAC,CAAA;EACD,IAAMyD,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIxD,IAAI,EAAEqD,UAAU,EAAK;EAC7C,EAAA,IAAI/C,uBAAqB,EAAE;EACvB,IAAA,IAAMqD,OAAO,GAAGnB,QAAM,CAACxC,IAAI,CAAC,CAAA;EAC5B,IAAA,OAAOsD,SAAS,CAACK,OAAO,EAAEN,UAAU,CAAC,CAAA;EACzC,GAAC,MACI;MACD,OAAO;EAAEZ,MAAAA,MAAM,EAAE,IAAI;EAAEzC,MAAAA,IAAI,EAAJA,IAAAA;EAAK,KAAC,CAAC;EAClC,GAAA;EACJ,CAAC,CAAA;;EACD,IAAMsD,SAAS,GAAG,SAAZA,SAASA,CAAItD,IAAI,EAAEqD,UAAU,EAAK;EACpC,EAAA,QAAQA,UAAU;EACd,IAAA,KAAK,MAAM;QACP,IAAIrD,IAAI,YAAYE,IAAI,EAAE;EACtB;EACA,QAAA,OAAOF,IAAI,CAAA;EACf,OAAC,MACI;EACD;EACA,QAAA,OAAO,IAAIE,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,CAAA;EAC3B,OAAA;EACJ,IAAA,KAAK,aAAa,CAAA;EAClB,IAAA;QACI,IAAIA,IAAI,YAAYO,WAAW,EAAE;EAC7B;EACA,QAAA,OAAOP,IAAI,CAAA;EACf,OAAC,MACI;EACD;UACA,OAAOA,IAAI,CAACU,MAAM,CAAA;EACtB,OAAA;EACR,GAAA;EACJ,CAAC;;EC1DD,IAAMkD,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,EAAE,CAAC,CAAC;EAC1C,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,OAAO,EAAElD,QAAQ,EAAK;EACzC;EACA,EAAA,IAAMwB,MAAM,GAAG0B,OAAO,CAAC1B,MAAM,CAAA;EAC7B,EAAA,IAAM2B,cAAc,GAAG,IAAIC,KAAK,CAAC5B,MAAM,CAAC,CAAA;IACxC,IAAI6B,KAAK,GAAG,CAAC,CAAA;EACbH,EAAAA,OAAO,CAACpE,OAAO,CAAC,UAACiC,MAAM,EAAEQ,CAAC,EAAK;EAC3B;EACA1B,IAAAA,YAAY,CAACkB,MAAM,EAAE,KAAK,EAAE,UAAAuB,aAAa,EAAI;EACzCa,MAAAA,cAAc,CAAC5B,CAAC,CAAC,GAAGe,aAAa,CAAA;EACjC,MAAA,IAAI,EAAEe,KAAK,KAAK7B,MAAM,EAAE;EACpBxB,QAAAA,QAAQ,CAACmD,cAAc,CAACG,IAAI,CAACR,SAAS,CAAC,CAAC,CAAA;EAC5C,OAAA;EACJ,KAAC,CAAC,CAAA;EACN,GAAC,CAAC,CAAA;EACN,CAAC,CAAA;EACD,IAAMS,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,cAAc,EAAEjB,UAAU,EAAK;EAClD,EAAA,IAAMY,cAAc,GAAGK,cAAc,CAACjD,KAAK,CAACuC,SAAS,CAAC,CAAA;IACtD,IAAMI,OAAO,GAAG,EAAE,CAAA;EAClB,EAAA,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,cAAc,CAAC3B,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAMkC,aAAa,GAAGpB,YAAY,CAACc,cAAc,CAAC5B,CAAC,CAAC,EAAEgB,UAAU,CAAC,CAAA;EACjEW,IAAAA,OAAO,CAACQ,IAAI,CAACD,aAAa,CAAC,CAAA;EAC3B,IAAA,IAAIA,aAAa,CAACxE,IAAI,KAAK,OAAO,EAAE;EAChC,MAAA,MAAA;EACJ,KAAA;EACJ,GAAA;EACA,EAAA,OAAOiE,OAAO,CAAA;EAClB,CAAC,CAAA;EACM,SAASS,yBAAyBA,GAAG;IACxC,OAAO,IAAIC,eAAe,CAAC;EACvBC,IAAAA,SAAS,EAAAA,SAAAA,SAAAA,CAAC9C,MAAM,EAAE+C,UAAU,EAAE;EAC1BhD,MAAAA,oBAAoB,CAACC,MAAM,EAAE,UAAAuB,aAAa,EAAI;EAC1C,QAAA,IAAMyB,aAAa,GAAGzB,aAAa,CAACd,MAAM,CAAA;EAC1C,QAAA,IAAIwC,MAAM,CAAA;EACV;UACA,IAAID,aAAa,GAAG,GAAG,EAAE;EACrBC,UAAAA,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC,CAAA;EAC1B,UAAA,IAAIuD,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC,CAACsE,QAAQ,CAAC,CAAC,EAAEH,aAAa,CAAC,CAAA;EAC1D,SAAC,MACI,IAAIA,aAAa,GAAG,KAAK,EAAE;EAC5BC,UAAAA,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC,CAAA;YAC1B,IAAMyD,IAAI,GAAG,IAAIF,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC,CAAA;EACxCuE,UAAAA,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;EACrBC,UAAAA,IAAI,CAACC,SAAS,CAAC,CAAC,EAAEL,aAAa,CAAC,CAAA;EACpC,SAAC,MACI;EACDC,UAAAA,MAAM,GAAG,IAAItD,UAAU,CAAC,CAAC,CAAC,CAAA;YAC1B,IAAMyD,KAAI,GAAG,IAAIF,QAAQ,CAACD,MAAM,CAACpE,MAAM,CAAC,CAAA;EACxCuE,UAAAA,KAAI,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;YACrBC,KAAI,CAACE,YAAY,CAAC,CAAC,EAAEC,MAAM,CAACP,aAAa,CAAC,CAAC,CAAA;EAC/C,SAAA;EACA;UACA,IAAIhD,MAAM,CAAC7B,IAAI,IAAI,OAAO6B,MAAM,CAAC7B,IAAI,KAAK,QAAQ,EAAE;EAChD8E,UAAAA,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAAA;EACrB,SAAA;EACAF,QAAAA,UAAU,CAACS,OAAO,CAACP,MAAM,CAAC,CAAA;EAC1BF,QAAAA,UAAU,CAACS,OAAO,CAACjC,aAAa,CAAC,CAAA;EACrC,OAAC,CAAC,CAAA;EACN,KAAA;EACJ,GAAC,CAAC,CAAA;EACN,CAAA;EACA,IAAIkC,YAAY,CAAA;EAChB,SAASC,WAAWA,CAACC,MAAM,EAAE;EACzB,EAAA,OAAOA,MAAM,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,KAAK,EAAA;EAAA,IAAA,OAAKD,GAAG,GAAGC,KAAK,CAACrD,MAAM,CAAA;EAAA,GAAA,EAAE,CAAC,CAAC,CAAA;EAC/D,CAAA;EACA,SAASsD,YAAYA,CAACJ,MAAM,EAAEK,IAAI,EAAE;IAChC,IAAIL,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,KAAKuD,IAAI,EAAE;EAC3B,IAAA,OAAOL,MAAM,CAACM,KAAK,EAAE,CAAA;EACzB,GAAA;EACA,EAAA,IAAMpF,MAAM,GAAG,IAAIc,UAAU,CAACqE,IAAI,CAAC,CAAA;IACnC,IAAIE,CAAC,GAAG,CAAC,CAAA;IACT,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,IAAI,EAAExD,CAAC,EAAE,EAAE;MAC3B3B,MAAM,CAAC2B,CAAC,CAAC,GAAGmD,MAAM,CAAC,CAAC,CAAC,CAACO,CAAC,EAAE,CAAC,CAAA;MAC1B,IAAIA,CAAC,KAAKP,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,EAAE;QACxBkD,MAAM,CAACM,KAAK,EAAE,CAAA;EACdC,MAAAA,CAAC,GAAG,CAAC,CAAA;EACT,KAAA;EACJ,GAAA;EACA,EAAA,IAAIP,MAAM,CAAClD,MAAM,IAAIyD,CAAC,GAAGP,MAAM,CAAC,CAAC,CAAC,CAAClD,MAAM,EAAE;EACvCkD,IAAAA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAACQ,KAAK,CAACD,CAAC,CAAC,CAAA;EAClC,GAAA;EACA,EAAA,OAAOrF,MAAM,CAAA;EACjB,CAAA;EACO,SAASuF,yBAAyBA,CAACC,UAAU,EAAE7C,UAAU,EAAE;IAC9D,IAAI,CAACiC,YAAY,EAAE;EACfA,IAAAA,YAAY,GAAG,IAAIa,WAAW,EAAE,CAAA;EACpC,GAAA;IACA,IAAMX,MAAM,GAAG,EAAE,CAAA;IACjB,IAAIY,KAAK,GAAG,CAAC,mBAAC;IACd,IAAIC,cAAc,GAAG,CAAC,CAAC,CAAA;IACvB,IAAIC,QAAQ,GAAG,KAAK,CAAA;IACpB,OAAO,IAAI5B,eAAe,CAAC;EACvBC,IAAAA,SAAS,EAAAA,SAAAA,SAAAA,CAACgB,KAAK,EAAEf,UAAU,EAAE;EACzBY,MAAAA,MAAM,CAAChB,IAAI,CAACmB,KAAK,CAAC,CAAA;EAClB,MAAA,OAAO,IAAI,EAAE;EACT,QAAA,IAAIS,KAAK,KAAK,CAAC,oBAAoB;EAC/B,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;EACzB,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMV,MAAM,GAAGc,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC,CAAA;YACtCc,QAAQ,GAAG,CAACxB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI,CAAA;EACtCuB,UAAAA,cAAc,GAAGvB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;YACjC,IAAIuB,cAAc,GAAG,GAAG,EAAE;cACtBD,KAAK,GAAG,CAAC,oBAAC;EACd,WAAC,MACI,IAAIC,cAAc,KAAK,GAAG,EAAE;cAC7BD,KAAK,GAAG,CAAC,+BAAC;EACd,WAAC,MACI;cACDA,KAAK,GAAG,CAAC,+BAAC;EACd,WAAA;EACJ,SAAC,MACI,IAAIA,KAAK,KAAK,CAAC,gCAAgC;EAChD,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;EACzB,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMe,WAAW,GAAGX,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC,CAAA;YAC3Ca,cAAc,GAAG,IAAItB,QAAQ,CAACwB,WAAW,CAAC7F,MAAM,EAAE6F,WAAW,CAAC9E,UAAU,EAAE8E,WAAW,CAACjE,MAAM,CAAC,CAACkE,SAAS,CAAC,CAAC,CAAC,CAAA;YAC1GJ,KAAK,GAAG,CAAC,oBAAC;EACd,SAAC,MACI,IAAIA,KAAK,KAAK,CAAC,gCAAgC;EAChD,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAG,CAAC,EAAE;EACzB,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMe,YAAW,GAAGX,YAAY,CAACJ,MAAM,EAAE,CAAC,CAAC,CAAA;EAC3C,UAAA,IAAMP,IAAI,GAAG,IAAIF,QAAQ,CAACwB,YAAW,CAAC7F,MAAM,EAAE6F,YAAW,CAAC9E,UAAU,EAAE8E,YAAW,CAACjE,MAAM,CAAC,CAAA;EACzF,UAAA,IAAMmE,CAAC,GAAGxB,IAAI,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAA;EAC3B,UAAA,IAAID,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE;EAC9B;EACAhC,YAAAA,UAAU,CAACS,OAAO,CAACvF,YAAY,CAAC,CAAA;EAChC,YAAA,MAAA;EACJ,WAAA;EACAuG,UAAAA,cAAc,GAAGI,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG3B,IAAI,CAACyB,SAAS,CAAC,CAAC,CAAC,CAAA;YACxDN,KAAK,GAAG,CAAC,oBAAC;EACd,SAAC,MACI;EACD,UAAA,IAAIb,WAAW,CAACC,MAAM,CAAC,GAAGa,cAAc,EAAE;EACtC,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAMrG,IAAI,GAAG4F,YAAY,CAACJ,MAAM,EAAEa,cAAc,CAAC,CAAA;EACjDzB,UAAAA,UAAU,CAACS,OAAO,CAAClC,YAAY,CAACmD,QAAQ,GAAGtG,IAAI,GAAGsF,YAAY,CAAC9C,MAAM,CAACxC,IAAI,CAAC,EAAEqD,UAAU,CAAC,CAAC,CAAA;YACzF+C,KAAK,GAAG,CAAC,mBAAC;EACd,SAAA;;EACA,QAAA,IAAIC,cAAc,KAAK,CAAC,IAAIA,cAAc,GAAGH,UAAU,EAAE;EACrDtB,UAAAA,UAAU,CAACS,OAAO,CAACvF,YAAY,CAAC,CAAA;EAChC,UAAA,MAAA;EACJ,SAAA;EACJ,OAAA;EACJ,KAAA;EACJ,GAAC,CAAC,CAAA;EACN,CAAA;EACO,IAAM+G,UAAQ,GAAG,CAAC;;EC1JzB;EACA;EACA;EACA;EACA;;EAEO,SAASC,OAAOA,CAACrG,GAAG,EAAE;EAC3B,EAAA,IAAIA,GAAG,EAAE,OAAOsG,KAAK,CAACtG,GAAG,CAAC,CAAA;EAC5B,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,SAASsG,KAAKA,CAACtG,GAAG,EAAE;EAClB,EAAA,KAAK,IAAIZ,GAAG,IAAIiH,OAAO,CAAC3G,SAAS,EAAE;MACjCM,GAAG,CAACZ,GAAG,CAAC,GAAGiH,OAAO,CAAC3G,SAAS,CAACN,GAAG,CAAC,CAAA;EACnC,GAAA;EACA,EAAA,OAAOY,GAAG,CAAA;EACZ,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAqG,OAAO,CAAC3G,SAAS,CAAC6G,EAAE,GACpBF,OAAO,CAAC3G,SAAS,CAAC8G,gBAAgB,GAAG,UAASC,KAAK,EAAEC,EAAE,EAAC;IACtD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;IACvC,CAAC,IAAI,CAACA,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,GAAG,IAAI,CAACE,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,IAAI,EAAE,EAC/D1C,IAAI,CAAC2C,EAAE,CAAC,CAAA;EACX,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAL,OAAO,CAAC3G,SAAS,CAACkH,IAAI,GAAG,UAASH,KAAK,EAAEC,EAAE,EAAC;IAC1C,SAASH,EAAEA,GAAG;EACZ,IAAA,IAAI,CAACM,GAAG,CAACJ,KAAK,EAAEF,EAAE,CAAC,CAAA;EACnBG,IAAAA,EAAE,CAACI,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAA;EAC3B,GAAA;IAEAR,EAAE,CAACG,EAAE,GAAGA,EAAE,CAAA;EACV,EAAA,IAAI,CAACH,EAAE,CAACE,KAAK,EAAEF,EAAE,CAAC,CAAA;EAClB,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAF,OAAO,CAAC3G,SAAS,CAACmH,GAAG,GACrBR,OAAO,CAAC3G,SAAS,CAACsH,cAAc,GAChCX,OAAO,CAAC3G,SAAS,CAACuH,kBAAkB,GACpCZ,OAAO,CAAC3G,SAAS,CAACwH,mBAAmB,GAAG,UAAST,KAAK,EAAEC,EAAE,EAAC;IACzD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;;EAEvC;EACA,EAAA,IAAI,CAAC,IAAII,SAAS,CAAClF,MAAM,EAAE;EACzB,IAAA,IAAI,CAAC8E,UAAU,GAAG,EAAE,CAAA;EACpB,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;IACA,IAAIQ,SAAS,GAAG,IAAI,CAACR,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EAC5C,EAAA,IAAI,CAACU,SAAS,EAAE,OAAO,IAAI,CAAA;;EAE3B;EACA,EAAA,IAAI,CAAC,IAAIJ,SAAS,CAAClF,MAAM,EAAE;EACzB,IAAA,OAAO,IAAI,CAAC8E,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EACnC,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;EACA,EAAA,IAAIW,EAAE,CAAA;EACN,EAAA,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuF,SAAS,CAACtF,MAAM,EAAED,CAAC,EAAE,EAAE;EACzCwF,IAAAA,EAAE,GAAGD,SAAS,CAACvF,CAAC,CAAC,CAAA;MACjB,IAAIwF,EAAE,KAAKV,EAAE,IAAIU,EAAE,CAACV,EAAE,KAAKA,EAAE,EAAE;EAC7BS,MAAAA,SAAS,CAACE,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC,CAAA;EACtB,MAAA,MAAA;EACF,KAAA;EACF,GAAA;;EAEA;EACA;EACA,EAAA,IAAIuF,SAAS,CAACtF,MAAM,KAAK,CAAC,EAAE;EAC1B,IAAA,OAAO,IAAI,CAAC8E,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EACrC,GAAA;EAEA,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAJ,OAAO,CAAC3G,SAAS,CAAC4H,IAAI,GAAG,UAASb,KAAK,EAAC;IACtC,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;IAEvC,IAAIY,IAAI,GAAG,IAAI9D,KAAK,CAACsD,SAAS,CAAClF,MAAM,GAAG,CAAC,CAAC;MACtCsF,SAAS,GAAG,IAAI,CAACR,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,CAAA;EAE5C,EAAA,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,SAAS,CAAClF,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC2F,IAAI,CAAC3F,CAAC,GAAG,CAAC,CAAC,GAAGmF,SAAS,CAACnF,CAAC,CAAC,CAAA;EAC5B,GAAA;EAEA,EAAA,IAAIuF,SAAS,EAAE;EACbA,IAAAA,SAAS,GAAGA,SAAS,CAAC5B,KAAK,CAAC,CAAC,CAAC,CAAA;EAC9B,IAAA,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAGiF,SAAS,CAACtF,MAAM,EAAED,CAAC,GAAGM,GAAG,EAAE,EAAEN,CAAC,EAAE;QACpDuF,SAAS,CAACvF,CAAC,CAAC,CAACkF,KAAK,CAAC,IAAI,EAAES,IAAI,CAAC,CAAA;EAChC,KAAA;EACF,GAAA;EAEA,EAAA,OAAO,IAAI,CAAA;EACb,CAAC,CAAA;;EAED;EACAlB,OAAO,CAAC3G,SAAS,CAAC8H,YAAY,GAAGnB,OAAO,CAAC3G,SAAS,CAAC4H,IAAI,CAAA;;EAEvD;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAjB,OAAO,CAAC3G,SAAS,CAAC+H,SAAS,GAAG,UAAShB,KAAK,EAAC;IAC3C,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,EAAE,CAAA;IACvC,OAAO,IAAI,CAACA,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,IAAI,EAAE,CAAA;EAC3C,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;;EAEAJ,OAAO,CAAC3G,SAAS,CAACgI,YAAY,GAAG,UAASjB,KAAK,EAAC;IAC9C,OAAO,CAAC,CAAE,IAAI,CAACgB,SAAS,CAAChB,KAAK,CAAC,CAAC5E,MAAM,CAAA;EACxC,CAAC;;ECxKM,IAAM8F,cAAc,GAAI,YAAM;EACjC,EAAA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;EAC7B,IAAA,OAAOA,IAAI,CAAA;EACf,GAAC,MACI,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACpC,IAAA,OAAOA,MAAM,CAAA;EACjB,GAAC,MACI;EACD,IAAA,OAAOC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAA;EACpC,GAAA;EACJ,CAAC,EAAG;;ECTG,SAASC,IAAIA,CAAC/H,GAAG,EAAW;IAAA,KAAAgI,IAAAA,IAAA,GAAAjB,SAAA,CAAAlF,MAAA,EAANoG,IAAI,OAAAxE,KAAA,CAAAuE,IAAA,GAAAA,CAAAA,GAAAA,IAAA,WAAAE,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA,EAAA,EAAA;EAAJD,IAAAA,IAAI,CAAAC,IAAA,GAAAnB,CAAAA,CAAAA,GAAAA,SAAA,CAAAmB,IAAA,CAAA,CAAA;EAAA,GAAA;IAC7B,OAAOD,IAAI,CAACjD,MAAM,CAAC,UAACC,GAAG,EAAEkD,CAAC,EAAK;EAC3B,IAAA,IAAInI,GAAG,CAACoI,cAAc,CAACD,CAAC,CAAC,EAAE;EACvBlD,MAAAA,GAAG,CAACkD,CAAC,CAAC,GAAGnI,GAAG,CAACmI,CAAC,CAAC,CAAA;EACnB,KAAA;EACA,IAAA,OAAOlD,GAAG,CAAA;KACb,EAAE,EAAE,CAAC,CAAA;EACV,CAAA;EACA;EACA,IAAMoD,kBAAkB,GAAGC,cAAU,CAACC,UAAU,CAAA;EAChD,IAAMC,oBAAoB,GAAGF,cAAU,CAACG,YAAY,CAAA;EAC7C,SAASC,qBAAqBA,CAAC1I,GAAG,EAAE2I,IAAI,EAAE;IAC7C,IAAIA,IAAI,CAACC,eAAe,EAAE;MACtB5I,GAAG,CAAC6I,YAAY,GAAGR,kBAAkB,CAACS,IAAI,CAACR,cAAU,CAAC,CAAA;MACtDtI,GAAG,CAAC+I,cAAc,GAAGP,oBAAoB,CAACM,IAAI,CAACR,cAAU,CAAC,CAAA;EAC9D,GAAC,MACI;MACDtI,GAAG,CAAC6I,YAAY,GAAGP,cAAU,CAACC,UAAU,CAACO,IAAI,CAACR,cAAU,CAAC,CAAA;MACzDtI,GAAG,CAAC+I,cAAc,GAAGT,cAAU,CAACG,YAAY,CAACK,IAAI,CAACR,cAAU,CAAC,CAAA;EACjE,GAAA;EACJ,CAAA;EACA;EACA,IAAMU,eAAe,GAAG,IAAI,CAAA;EAC5B;EACO,SAAS/H,UAAUA,CAACjB,GAAG,EAAE;EAC5B,EAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACzB,OAAOiJ,UAAU,CAACjJ,GAAG,CAAC,CAAA;EAC1B,GAAA;EACA;EACA,EAAA,OAAOkG,IAAI,CAACgD,IAAI,CAAC,CAAClJ,GAAG,CAACiB,UAAU,IAAIjB,GAAG,CAACoF,IAAI,IAAI4D,eAAe,CAAC,CAAA;EACpE,CAAA;EACA,SAASC,UAAUA,CAACE,GAAG,EAAE;IACrB,IAAIC,CAAC,GAAG,CAAC;EAAEvH,IAAAA,MAAM,GAAG,CAAC,CAAA;EACrB,EAAA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEyH,CAAC,GAAGF,GAAG,CAACtH,MAAM,EAAED,CAAC,GAAGyH,CAAC,EAAEzH,CAAC,EAAE,EAAE;EACxCwH,IAAAA,CAAC,GAAGD,GAAG,CAACrH,UAAU,CAACF,CAAC,CAAC,CAAA;MACrB,IAAIwH,CAAC,GAAG,IAAI,EAAE;EACVvH,MAAAA,MAAM,IAAI,CAAC,CAAA;EACf,KAAC,MACI,IAAIuH,CAAC,GAAG,KAAK,EAAE;EAChBvH,MAAAA,MAAM,IAAI,CAAC,CAAA;OACd,MACI,IAAIuH,CAAC,GAAG,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;EAChCvH,MAAAA,MAAM,IAAI,CAAC,CAAA;EACf,KAAC,MACI;EACDD,MAAAA,CAAC,EAAE,CAAA;EACHC,MAAAA,MAAM,IAAI,CAAC,CAAA;EACf,KAAA;EACJ,GAAA;EACA,EAAA,OAAOA,MAAM,CAAA;EACjB;;ECnDA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASJ,QAAMA,CAACzB,GAAG,EAAE;IACxB,IAAImJ,GAAG,GAAG,EAAE,CAAA;EACZ,EAAA,KAAK,IAAIvH,CAAC,IAAI5B,GAAG,EAAE;EACf,IAAA,IAAIA,GAAG,CAACoI,cAAc,CAACxG,CAAC,CAAC,EAAE;EACvB,MAAA,IAAIuH,GAAG,CAACtH,MAAM,EACVsH,GAAG,IAAI,GAAG,CAAA;EACdA,MAAAA,GAAG,IAAIG,kBAAkB,CAAC1H,CAAC,CAAC,GAAG,GAAG,GAAG0H,kBAAkB,CAACtJ,GAAG,CAAC4B,CAAC,CAAC,CAAC,CAAA;EACnE,KAAA;EACJ,GAAA;EACA,EAAA,OAAOuH,GAAG,CAAA;EACd,CAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASpH,MAAMA,CAACwH,EAAE,EAAE;IACvB,IAAIC,GAAG,GAAG,EAAE,CAAA;EACZ,EAAA,IAAIC,KAAK,GAAGF,EAAE,CAAC3I,KAAK,CAAC,GAAG,CAAC,CAAA;EACzB,EAAA,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEyH,CAAC,GAAGI,KAAK,CAAC5H,MAAM,EAAED,CAAC,GAAGyH,CAAC,EAAEzH,CAAC,EAAE,EAAE;MAC1C,IAAI8H,IAAI,GAAGD,KAAK,CAAC7H,CAAC,CAAC,CAAChB,KAAK,CAAC,GAAG,CAAC,CAAA;EAC9B4I,IAAAA,GAAG,CAACG,kBAAkB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,kBAAkB,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;EAClE,GAAA;EACA,EAAA,OAAOF,GAAG,CAAA;EACd;;EC9B8C,IACxCI,cAAc,0BAAAC,MAAA,EAAA;IAAAC,SAAA,CAAAF,cAAA,EAAAC,MAAA,CAAA,CAAA;EAAA,EAAA,IAAAE,MAAA,GAAAC,YAAA,CAAAJ,cAAA,CAAA,CAAA;EAChB,EAAA,SAAAA,eAAYK,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAAA,IAAA,IAAAC,KAAA,CAAA;EAAAC,IAAAA,eAAA,OAAAT,cAAA,CAAA,CAAA;EACtCQ,IAAAA,KAAA,GAAAL,MAAA,CAAAnK,IAAA,OAAMqK,MAAM,CAAA,CAAA;MACZG,KAAA,CAAKF,WAAW,GAAGA,WAAW,CAAA;MAC9BE,KAAA,CAAKD,OAAO,GAAGA,OAAO,CAAA;MACtBC,KAAA,CAAK9K,IAAI,GAAG,gBAAgB,CAAA;EAAC,IAAA,OAAA8K,KAAA,CAAA;EACjC,GAAA;IAAC,OAAAE,YAAA,CAAAV,cAAA,CAAA,CAAA;EAAA,CAAAW,eAAAA,gBAAA,CANwBC,KAAK,CAAA,CAAA,CAAA;EAQrBC,IAAAA,SAAS,0BAAAC,QAAA,EAAA;IAAAZ,SAAA,CAAAW,SAAA,EAAAC,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAC,OAAA,GAAAX,YAAA,CAAAS,SAAA,CAAA,CAAA;EAClB;EACJ;EACA;EACA;EACA;EACA;IACI,SAAAA,SAAAA,CAAY9B,IAAI,EAAE;EAAA,IAAA,IAAAiC,MAAA,CAAA;EAAAP,IAAAA,eAAA,OAAAI,SAAA,CAAA,CAAA;MACdG,MAAA,GAAAD,OAAA,CAAA/K,IAAA,CAAA,IAAA,CAAA,CAAA;MACAgL,MAAA,CAAKC,QAAQ,GAAG,KAAK,CAAA;EACrBnC,IAAAA,qBAAqB,CAAAoC,sBAAA,CAAAF,MAAA,CAAA,EAAOjC,IAAI,CAAC,CAAA;MACjCiC,MAAA,CAAKjC,IAAI,GAAGA,IAAI,CAAA;EAChBiC,IAAAA,MAAA,CAAKG,KAAK,GAAGpC,IAAI,CAACoC,KAAK,CAAA;EACvBH,IAAAA,MAAA,CAAKI,MAAM,GAAGrC,IAAI,CAACqC,MAAM,CAAA;EAAC,IAAA,OAAAJ,MAAA,CAAA;EAC9B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EARIN,EAAAA,YAAA,CAAAG,SAAA,EAAA,CAAA;MAAArL,GAAA,EAAA,SAAA;MAAA6L,KAAA,EASA,SAAAC,OAAQjB,CAAAA,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAClCgB,MAAAA,IAAA,CAAAC,eAAA,CAAAX,SAAA,CAAA/K,SAAA,yBAAAE,IAAA,CAAA,IAAA,EAAmB,OAAO,EAAE,IAAIgK,cAAc,CAACK,MAAM,EAAEC,WAAW,EAAEC,OAAO,CAAC,CAAA,CAAA;EAC5E,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EAFI,GAAA,EAAA;MAAA/K,GAAA,EAAA,MAAA;MAAA6L,KAAA,EAGA,SAAAI,IAAAA,GAAO;QACH,IAAI,CAACC,UAAU,GAAG,SAAS,CAAA;QAC3B,IAAI,CAACC,MAAM,EAAE,CAAA;EACb,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EAFI,GAAA,EAAA;MAAAnM,GAAA,EAAA,OAAA;MAAA6L,KAAA,EAGA,SAAAO,KAAAA,GAAQ;QACJ,IAAI,IAAI,CAACF,UAAU,KAAK,SAAS,IAAI,IAAI,CAACA,UAAU,KAAK,MAAM,EAAE;UAC7D,IAAI,CAACG,OAAO,EAAE,CAAA;UACd,IAAI,CAACC,OAAO,EAAE,CAAA;EAClB,OAAA;EACA,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAtM,GAAA,EAAA,MAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAU,IAAKpI,CAAAA,OAAO,EAAE;EACV,MAAA,IAAI,IAAI,CAAC+H,UAAU,KAAK,MAAM,EAAE;EAC5B,QAAA,IAAI,CAACM,KAAK,CAACrI,OAAO,CAAC,CAAA;EACvB,OAEI;EAER,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAnE,GAAA,EAAA,QAAA;MAAA6L,KAAA,EAKA,SAAAY,MAAAA,GAAS;QACL,IAAI,CAACP,UAAU,GAAG,MAAM,CAAA;QACxB,IAAI,CAACT,QAAQ,GAAG,IAAI,CAAA;QACpBM,IAAA,CAAAC,eAAA,CAAAX,SAAA,CAAA/K,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAAE,IAAA,CAAA,IAAA,EAAmB,MAAM,CAAA,CAAA;EAC7B,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAR,GAAA,EAAA,QAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAa,MAAOvM,CAAAA,IAAI,EAAE;QACT,IAAM6B,MAAM,GAAGsB,YAAY,CAACnD,IAAI,EAAE,IAAI,CAACyL,MAAM,CAACpI,UAAU,CAAC,CAAA;EACzD,MAAA,IAAI,CAACmJ,QAAQ,CAAC3K,MAAM,CAAC,CAAA;EACzB,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAhC,GAAA,EAAA,UAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAc,QAAS3K,CAAAA,MAAM,EAAE;QACb+J,IAAA,CAAAC,eAAA,CAAAX,SAAA,CAAA/K,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAAE,IAAA,CAAA,IAAA,EAAmB,QAAQ,EAAEwB,MAAM,CAAA,CAAA;EACvC,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAhC,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAS,OAAQM,CAAAA,OAAO,EAAE;QACb,IAAI,CAACV,UAAU,GAAG,QAAQ,CAAA;QAC1BH,IAAA,CAAAC,eAAA,CAAAX,SAAA,CAAA/K,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAAE,IAAA,CAAA,IAAA,EAAmB,OAAO,EAAEoM,OAAO,CAAA,CAAA;EACvC,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA5M,GAAA,EAAA,OAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAgB,KAAMC,CAAAA,OAAO,EAAE,EAAE;EAAC,GAAA,EAAA;MAAA9M,GAAA,EAAA,WAAA;EAAA6L,IAAAA,KAAA,EAClB,SAAAkB,SAAUC,CAAAA,MAAM,EAAc;EAAA,MAAA,IAAZrB,KAAK,GAAAhE,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAAsF,SAAA,GAAAtF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;QACxB,OAAQqF,MAAM,GACV,KAAK,GACL,IAAI,CAACE,SAAS,EAAE,GAChB,IAAI,CAACC,KAAK,EAAE,GACZ,IAAI,CAAC5D,IAAI,CAAC6D,IAAI,GACd,IAAI,CAACC,MAAM,CAAC1B,KAAK,CAAC,CAAA;EAC1B,KAAA;EAAC,GAAA,EAAA;MAAA3L,GAAA,EAAA,WAAA;MAAA6L,KAAA,EACD,SAAAqB,SAAAA,GAAY;EACR,MAAA,IAAMI,QAAQ,GAAG,IAAI,CAAC/D,IAAI,CAAC+D,QAAQ,CAAA;EACnC,MAAA,OAAOA,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAGD,QAAQ,GAAG,GAAG,GAAGA,QAAQ,GAAG,GAAG,CAAA;EACzE,KAAA;EAAC,GAAA,EAAA;MAAAtN,GAAA,EAAA,OAAA;MAAA6L,KAAA,EACD,SAAAsB,KAAAA,GAAQ;EACJ,MAAA,IAAI,IAAI,CAAC5D,IAAI,CAACiE,IAAI,KACZ,IAAI,CAACjE,IAAI,CAACkE,MAAM,IAAIC,MAAM,CAAC,IAAI,CAACnE,IAAI,CAACiE,IAAI,KAAK,GAAG,CAAC,IAC/C,CAAC,IAAI,CAACjE,IAAI,CAACkE,MAAM,IAAIC,MAAM,CAAC,IAAI,CAACnE,IAAI,CAACiE,IAAI,CAAC,KAAK,EAAG,CAAC,EAAE;EAC3D,QAAA,OAAO,GAAG,GAAG,IAAI,CAACjE,IAAI,CAACiE,IAAI,CAAA;EAC/B,OAAC,MACI;EACD,QAAA,OAAO,EAAE,CAAA;EACb,OAAA;EACJ,KAAA;EAAC,GAAA,EAAA;MAAAxN,GAAA,EAAA,QAAA;EAAA6L,IAAAA,KAAA,EACD,SAAAwB,MAAO1B,CAAAA,KAAK,EAAE;EACV,MAAA,IAAMgC,YAAY,GAAGtL,QAAM,CAACsJ,KAAK,CAAC,CAAA;QAClC,OAAOgC,YAAY,CAAClL,MAAM,GAAG,GAAG,GAAGkL,YAAY,GAAG,EAAE,CAAA;EACxD,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,EAAA,OAAAtC,SAAA,CAAA;EAAA,CAAA,CA/H0BpE,OAAO,CAAA;;ECZtC;;EAEA,IAAM2G,QAAQ,GAAG,kEAAkE,CAACpM,KAAK,CAAC,EAAE,CAAC;EAAEiB,EAAAA,MAAM,GAAG,EAAE;IAAEoL,GAAG,GAAG,EAAE,CAAA;EACpH,IAAIC,IAAI,GAAG,CAAC;EAAEtL,EAAAA,CAAC,GAAG,CAAC;IAAEuL,IAAI,CAAA;EACzB;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS1L,MAAMA,CAAC2L,GAAG,EAAE;IACxB,IAAI7L,OAAO,GAAG,EAAE,CAAA;IAChB,GAAG;MACCA,OAAO,GAAGyL,QAAQ,CAACI,GAAG,GAAGvL,MAAM,CAAC,GAAGN,OAAO,CAAA;MAC1C6L,GAAG,GAAGlH,IAAI,CAACmH,KAAK,CAACD,GAAG,GAAGvL,MAAM,CAAC,CAAA;KACjC,QAAQuL,GAAG,GAAG,CAAC,EAAA;EAChB,EAAA,OAAO7L,OAAO,CAAA;EAClB,CAAA;EAeA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS+L,KAAKA,GAAG;IACpB,IAAMC,GAAG,GAAG9L,MAAM,CAAC,CAAC,IAAI+L,IAAI,EAAE,CAAC,CAAA;IAC/B,IAAID,GAAG,KAAKJ,IAAI,EACZ,OAAOD,IAAI,GAAG,CAAC,EAAEC,IAAI,GAAGI,GAAG,CAAA;IAC/B,OAAOA,GAAG,GAAG,GAAG,GAAG9L,MAAM,CAACyL,IAAI,EAAE,CAAC,CAAA;EACrC,CAAA;EACA;EACA;EACA;EACA,OAAOtL,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAClBqL,GAAG,CAACD,QAAQ,CAACpL,CAAC,CAAC,CAAC,GAAGA,CAAC;;ECjDxB;EACA,IAAIqJ,KAAK,GAAG,KAAK,CAAA;EACjB,IAAI;IACAA,KAAK,GAAG,OAAOwC,cAAc,KAAK,WAAW,IACzC,iBAAiB,IAAI,IAAIA,cAAc,EAAE,CAAA;EACjD,CAAC,CACD,OAAOC,GAAG,EAAE;EACR;EACA;EAAA,CAAA;EAEG,IAAMC,OAAO,GAAG1C,KAAK;;ECV5B;EAGO,SAAS2C,GAAGA,CAACjF,IAAI,EAAE;EACtB,EAAA,IAAMkF,OAAO,GAAGlF,IAAI,CAACkF,OAAO,CAAA;EAC5B;IACA,IAAI;MACA,IAAI,WAAW,KAAK,OAAOJ,cAAc,KAAK,CAACI,OAAO,IAAIF,OAAO,CAAC,EAAE;QAChE,OAAO,IAAIF,cAAc,EAAE,CAAA;EAC/B,KAAA;EACJ,GAAC,CACD,OAAOK,CAAC,EAAE,EAAE;IACZ,IAAI,CAACD,OAAO,EAAE;MACV,IAAI;EACA,MAAA,OAAO,IAAIvF,cAAU,CAAC,CAAC,QAAQ,CAAC,CAACyF,MAAM,CAAC,QAAQ,CAAC,CAACpK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAA;EACrF,KAAC,CACD,OAAOmK,CAAC,EAAE,EAAE;EAChB,GAAA;EACJ,CAAA;EACO,SAASE,eAAeA,GAAG;;ECZlC,SAASC,KAAKA,GAAG,EAAE;EACnB,IAAMC,OAAO,GAAI,YAAY;EACzB,EAAA,IAAMC,GAAG,GAAG,IAAIV,GAAc,CAAC;EAC3BI,IAAAA,OAAO,EAAE,KAAA;EACb,GAAC,CAAC,CAAA;EACF,EAAA,OAAO,IAAI,IAAIM,GAAG,CAACC,YAAY,CAAA;EACnC,CAAC,EAAG,CAAA;EACSC,IAAAA,OAAO,0BAAAC,UAAA,EAAA;IAAAxE,SAAA,CAAAuE,OAAA,EAAAC,UAAA,CAAA,CAAA;EAAA,EAAA,IAAAvE,MAAA,GAAAC,YAAA,CAAAqE,OAAA,CAAA,CAAA;EAChB;EACJ;EACA;EACA;EACA;EACA;IACI,SAAAA,OAAAA,CAAY1F,IAAI,EAAE;EAAA,IAAA,IAAAyB,KAAA,CAAA;EAAAC,IAAAA,eAAA,OAAAgE,OAAA,CAAA,CAAA;EACdjE,IAAAA,KAAA,GAAAL,MAAA,CAAAnK,IAAA,OAAM+I,IAAI,CAAA,CAAA;MACVyB,KAAA,CAAKmE,OAAO,GAAG,KAAK,CAAA;EACpB,IAAA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACjC,MAAA,IAAMC,KAAK,GAAG,QAAQ,KAAKD,QAAQ,CAACpI,QAAQ,CAAA;EAC5C,MAAA,IAAIwG,IAAI,GAAG4B,QAAQ,CAAC5B,IAAI,CAAA;EACxB;QACA,IAAI,CAACA,IAAI,EAAE;EACPA,QAAAA,IAAI,GAAG6B,KAAK,GAAG,KAAK,GAAG,IAAI,CAAA;EAC/B,OAAA;QACArE,KAAA,CAAKsE,EAAE,GACF,OAAOF,QAAQ,KAAK,WAAW,IAC5B7F,IAAI,CAAC+D,QAAQ,KAAK8B,QAAQ,CAAC9B,QAAQ,IACnCE,IAAI,KAAKjE,IAAI,CAACiE,IAAI,CAAA;EAC9B,KAAA;EACA;EACR;EACA;EACQ,IAAA,IAAM+B,WAAW,GAAGhG,IAAI,IAAIA,IAAI,CAACgG,WAAW,CAAA;EAC5CvE,IAAAA,KAAA,CAAKhK,cAAc,GAAG8N,OAAO,IAAI,CAACS,WAAW,CAAA;EAC7C,IAAA,IAAIvE,KAAA,CAAKzB,IAAI,CAACiG,eAAe,EAAE;EAC3BxE,MAAAA,KAAA,CAAKyE,SAAS,GAAGb,eAAe,EAAE,CAAA;EACtC,KAAA;EAAC,IAAA,OAAA5D,KAAA,CAAA;EACL,GAAA;EAACE,EAAAA,YAAA,CAAA+D,OAAA,EAAA,CAAA;MAAAjP,GAAA,EAAA,MAAA;MAAA0P,GAAA,EACD,SAAAA,GAAAA,GAAW;EACP,MAAA,OAAO,SAAS,CAAA;EACpB,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAA1P,GAAA,EAAA,QAAA;MAAA6L,KAAA,EAMA,SAAAM,MAAAA,GAAS;QACL,IAAI,CAACwD,IAAI,EAAE,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAA3P,GAAA,EAAA,OAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAgB,KAAMC,CAAAA,OAAO,EAAE;EAAA,MAAA,IAAAtB,MAAA,GAAA,IAAA,CAAA;QACX,IAAI,CAACU,UAAU,GAAG,SAAS,CAAA;EAC3B,MAAA,IAAMW,KAAK,GAAG,SAARA,KAAKA,GAAS;UAChBrB,MAAI,CAACU,UAAU,GAAG,QAAQ,CAAA;EAC1BY,QAAAA,OAAO,EAAE,CAAA;SACZ,CAAA;QACD,IAAI,IAAI,CAACqC,OAAO,IAAI,CAAC,IAAI,CAAC1D,QAAQ,EAAE;UAChC,IAAImE,KAAK,GAAG,CAAC,CAAA;UACb,IAAI,IAAI,CAACT,OAAO,EAAE;EACdS,UAAAA,KAAK,EAAE,CAAA;EACP,UAAA,IAAI,CAACpI,IAAI,CAAC,cAAc,EAAE,YAAY;EAClC,YAAA,EAAEoI,KAAK,IAAI/C,KAAK,EAAE,CAAA;EACtB,WAAC,CAAC,CAAA;EACN,SAAA;EACA,QAAA,IAAI,CAAC,IAAI,CAACpB,QAAQ,EAAE;EAChBmE,UAAAA,KAAK,EAAE,CAAA;EACP,UAAA,IAAI,CAACpI,IAAI,CAAC,OAAO,EAAE,YAAY;EAC3B,YAAA,EAAEoI,KAAK,IAAI/C,KAAK,EAAE,CAAA;EACtB,WAAC,CAAC,CAAA;EACN,SAAA;EACJ,OAAC,MACI;EACDA,QAAAA,KAAK,EAAE,CAAA;EACX,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA7M,GAAA,EAAA,MAAA;MAAA6L,KAAA,EAKA,SAAA8D,IAAAA,GAAO;QACH,IAAI,CAACR,OAAO,GAAG,IAAI,CAAA;QACnB,IAAI,CAACU,MAAM,EAAE,CAAA;EACb,MAAA,IAAI,CAACzH,YAAY,CAAC,MAAM,CAAC,CAAA;EAC7B,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAApI,GAAA,EAAA,QAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAa,MAAOvM,CAAAA,IAAI,EAAE;EAAA,MAAA,IAAA2P,MAAA,GAAA,IAAA,CAAA;EACT,MAAA,IAAM7O,QAAQ,GAAG,SAAXA,QAAQA,CAAIe,MAAM,EAAK;EACzB;UACA,IAAI,SAAS,KAAK8N,MAAI,CAAC5D,UAAU,IAAIlK,MAAM,CAAC9B,IAAI,KAAK,MAAM,EAAE;YACzD4P,MAAI,CAACrD,MAAM,EAAE,CAAA;EACjB,SAAA;EACA;EACA,QAAA,IAAI,OAAO,KAAKzK,MAAM,CAAC9B,IAAI,EAAE;YACzB4P,MAAI,CAACxD,OAAO,CAAC;EAAExB,YAAAA,WAAW,EAAE,gCAAA;EAAiC,WAAC,CAAC,CAAA;EAC/D,UAAA,OAAO,KAAK,CAAA;EAChB,SAAA;EACA;EACAgF,QAAAA,MAAI,CAACnD,QAAQ,CAAC3K,MAAM,CAAC,CAAA;SACxB,CAAA;EACD;EACAwC,MAAAA,aAAa,CAACrE,IAAI,EAAE,IAAI,CAACyL,MAAM,CAACpI,UAAU,CAAC,CAACzD,OAAO,CAACkB,QAAQ,CAAC,CAAA;EAC7D;EACA,MAAA,IAAI,QAAQ,KAAK,IAAI,CAACiL,UAAU,EAAE;EAC9B;UACA,IAAI,CAACiD,OAAO,GAAG,KAAK,CAAA;EACpB,QAAA,IAAI,CAAC/G,YAAY,CAAC,cAAc,CAAC,CAAA;EACjC,QAAA,IAAI,MAAM,KAAK,IAAI,CAAC8D,UAAU,EAAE;YAC5B,IAAI,CAACyD,IAAI,EAAE,CAAA;EACf,SAEA;EACJ,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA3P,GAAA,EAAA,SAAA;MAAA6L,KAAA,EAKA,SAAAQ,OAAAA,GAAU;EAAA,MAAA,IAAA0D,MAAA,GAAA,IAAA,CAAA;EACN,MAAA,IAAM3D,KAAK,GAAG,SAARA,KAAKA,GAAS;UAChB2D,MAAI,CAACvD,KAAK,CAAC,CAAC;EAAEtM,UAAAA,IAAI,EAAE,OAAA;EAAQ,SAAC,CAAC,CAAC,CAAA;SAClC,CAAA;EACD,MAAA,IAAI,MAAM,KAAK,IAAI,CAACgM,UAAU,EAAE;EAC5BE,QAAAA,KAAK,EAAE,CAAA;EACX,OAAC,MACI;EACD;EACA;EACA,QAAA,IAAI,CAAC5E,IAAI,CAAC,MAAM,EAAE4E,KAAK,CAAC,CAAA;EAC5B,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAApM,GAAA,EAAA,OAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAW,KAAMrI,CAAAA,OAAO,EAAE;EAAA,MAAA,IAAA6L,MAAA,GAAA,IAAA,CAAA;QACX,IAAI,CAACvE,QAAQ,GAAG,KAAK,CAAA;EACrBvH,MAAAA,aAAa,CAACC,OAAO,EAAE,UAAChE,IAAI,EAAK;EAC7B6P,QAAAA,MAAI,CAACC,OAAO,CAAC9P,IAAI,EAAE,YAAM;YACrB6P,MAAI,CAACvE,QAAQ,GAAG,IAAI,CAAA;EACpBuE,UAAAA,MAAI,CAAC5H,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,SAAC,CAAC,CAAA;EACN,OAAC,CAAC,CAAA;EACN,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAApI,GAAA,EAAA,KAAA;MAAA6L,KAAA,EAKA,SAAAqE,GAAAA,GAAM;QACF,IAAMlD,MAAM,GAAG,IAAI,CAACzD,IAAI,CAACkE,MAAM,GAAG,OAAO,GAAG,MAAM,CAAA;EAClD,MAAA,IAAM9B,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,EAAE,CAAA;EAC9B;EACA,MAAA,IAAI,KAAK,KAAK,IAAI,CAACpC,IAAI,CAAC4G,iBAAiB,EAAE;UACvCxE,KAAK,CAAC,IAAI,CAACpC,IAAI,CAAC6G,cAAc,CAAC,GAAGlC,KAAK,EAAE,CAAA;EAC7C,OAAA;QACA,IAAI,CAAC,IAAI,CAAClN,cAAc,IAAI,CAAC2K,KAAK,CAAC0E,GAAG,EAAE;UACpC1E,KAAK,CAAC2E,GAAG,GAAG,CAAC,CAAA;EACjB,OAAA;EACA,MAAA,OAAO,IAAI,CAACvD,SAAS,CAACC,MAAM,EAAErB,KAAK,CAAC,CAAA;EACxC,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAA3L,GAAA,EAAA,SAAA;MAAA6L,KAAA,EAMA,SAAA0E,OAAAA,GAAmB;EAAA,MAAA,IAAXhH,IAAI,GAAA5B,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAAsF,SAAA,GAAAtF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;QACb6I,QAAA,CAAcjH,IAAI,EAAE;UAAE+F,EAAE,EAAE,IAAI,CAACA,EAAE;UAAEG,SAAS,EAAE,IAAI,CAACA,SAAAA;EAAU,OAAC,EAAE,IAAI,CAAClG,IAAI,CAAC,CAAA;QAC1E,OAAO,IAAIkH,OAAO,CAAC,IAAI,CAACP,GAAG,EAAE,EAAE3G,IAAI,CAAC,CAAA;EACxC,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EANI,GAAA,EAAA;MAAAvJ,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAOA,SAAAoE,OAAAA,CAAQ9P,IAAI,EAAEmH,EAAE,EAAE;EAAA,MAAA,IAAAoJ,MAAA,GAAA,IAAA,CAAA;EACd,MAAA,IAAMC,GAAG,GAAG,IAAI,CAACJ,OAAO,CAAC;EACrBK,QAAAA,MAAM,EAAE,MAAM;EACdzQ,QAAAA,IAAI,EAAEA,IAAAA;EACV,OAAC,CAAC,CAAA;EACFwQ,MAAAA,GAAG,CAACxJ,EAAE,CAAC,SAAS,EAAEG,EAAE,CAAC,CAAA;QACrBqJ,GAAG,CAACxJ,EAAE,CAAC,OAAO,EAAE,UAAC0J,SAAS,EAAE9F,OAAO,EAAK;UACpC2F,MAAI,CAAC5E,OAAO,CAAC,gBAAgB,EAAE+E,SAAS,EAAE9F,OAAO,CAAC,CAAA;EACtD,OAAC,CAAC,CAAA;EACN,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA/K,GAAA,EAAA,QAAA;MAAA6L,KAAA,EAKA,SAAAgE,MAAAA,GAAS;EAAA,MAAA,IAAAiB,MAAA,GAAA,IAAA,CAAA;EACL,MAAA,IAAMH,GAAG,GAAG,IAAI,CAACJ,OAAO,EAAE,CAAA;EAC1BI,MAAAA,GAAG,CAACxJ,EAAE,CAAC,MAAM,EAAE,IAAI,CAACuF,MAAM,CAAChD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QACtCiH,GAAG,CAACxJ,EAAE,CAAC,OAAO,EAAE,UAAC0J,SAAS,EAAE9F,OAAO,EAAK;UACpC+F,MAAI,CAAChF,OAAO,CAAC,gBAAgB,EAAE+E,SAAS,EAAE9F,OAAO,CAAC,CAAA;EACtD,OAAC,CAAC,CAAA;QACF,IAAI,CAACgG,OAAO,GAAGJ,GAAG,CAAA;EACtB,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,EAAA,OAAA1B,OAAA,CAAA;EAAA,CAAA,CA9MwB5D,SAAS,CAAA,CAAA;EAgNzBoF,IAAAA,OAAO,0BAAAnF,QAAA,EAAA;IAAAZ,SAAA,CAAA+F,OAAA,EAAAnF,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAC,OAAA,GAAAX,YAAA,CAAA6F,OAAA,CAAA,CAAA;EAChB;EACJ;EACA;EACA;EACA;EACA;EACI,EAAA,SAAAA,OAAYP,CAAAA,GAAG,EAAE3G,IAAI,EAAE;EAAA,IAAA,IAAAyH,MAAA,CAAA;EAAA/F,IAAAA,eAAA,OAAAwF,OAAA,CAAA,CAAA;MACnBO,MAAA,GAAAzF,OAAA,CAAA/K,IAAA,CAAA,IAAA,CAAA,CAAA;EACA8I,IAAAA,qBAAqB,CAAAoC,sBAAA,CAAAsF,MAAA,CAAA,EAAOzH,IAAI,CAAC,CAAA;MACjCyH,MAAA,CAAKzH,IAAI,GAAGA,IAAI,CAAA;EAChByH,IAAAA,MAAA,CAAKJ,MAAM,GAAGrH,IAAI,CAACqH,MAAM,IAAI,KAAK,CAAA;MAClCI,MAAA,CAAKd,GAAG,GAAGA,GAAG,CAAA;EACdc,IAAAA,MAAA,CAAK7Q,IAAI,GAAG8M,SAAS,KAAK1D,IAAI,CAACpJ,IAAI,GAAGoJ,IAAI,CAACpJ,IAAI,GAAG,IAAI,CAAA;MACtD6Q,MAAA,CAAKpR,MAAM,EAAE,CAAA;EAAC,IAAA,OAAAoR,MAAA,CAAA;EAClB,GAAA;EACA;EACJ;EACA;EACA;EACA;EAJI9F,EAAAA,YAAA,CAAAuF,OAAA,EAAA,CAAA;MAAAzQ,GAAA,EAAA,QAAA;MAAA6L,KAAA,EAKA,SAAAjM,MAAAA,GAAS;EAAA,MAAA,IAAAqR,MAAA,GAAA,IAAA,CAAA;EACL,MAAA,IAAIC,EAAE,CAAA;QACN,IAAM3H,IAAI,GAAGZ,IAAI,CAAC,IAAI,CAACY,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,WAAW,CAAC,CAAA;QAC7HA,IAAI,CAACkF,OAAO,GAAG,CAAC,CAAC,IAAI,CAAClF,IAAI,CAAC+F,EAAE,CAAA;QAC7B,IAAMP,GAAG,GAAI,IAAI,CAACA,GAAG,GAAG,IAAIV,GAAc,CAAC9E,IAAI,CAAE,CAAA;QACjD,IAAI;EACAwF,QAAAA,GAAG,CAAC9C,IAAI,CAAC,IAAI,CAAC2E,MAAM,EAAE,IAAI,CAACV,GAAG,EAAE,IAAI,CAAC,CAAA;UACrC,IAAI;EACA,UAAA,IAAI,IAAI,CAAC3G,IAAI,CAAC4H,YAAY,EAAE;cACxBpC,GAAG,CAACqC,qBAAqB,IAAIrC,GAAG,CAACqC,qBAAqB,CAAC,IAAI,CAAC,CAAA;cAC5D,KAAK,IAAI5O,CAAC,IAAI,IAAI,CAAC+G,IAAI,CAAC4H,YAAY,EAAE;gBAClC,IAAI,IAAI,CAAC5H,IAAI,CAAC4H,YAAY,CAACnI,cAAc,CAACxG,CAAC,CAAC,EAAE;EAC1CuM,gBAAAA,GAAG,CAACsC,gBAAgB,CAAC7O,CAAC,EAAE,IAAI,CAAC+G,IAAI,CAAC4H,YAAY,CAAC3O,CAAC,CAAC,CAAC,CAAA;EACtD,eAAA;EACJ,aAAA;EACJ,WAAA;EACJ,SAAC,CACD,OAAOkM,CAAC,EAAE,EAAE;EACZ,QAAA,IAAI,MAAM,KAAK,IAAI,CAACkC,MAAM,EAAE;YACxB,IAAI;EACA7B,YAAAA,GAAG,CAACsC,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC,CAAA;EACpE,WAAC,CACD,OAAO3C,CAAC,EAAE,EAAE;EAChB,SAAA;UACA,IAAI;EACAK,UAAAA,GAAG,CAACsC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAA;EACzC,SAAC,CACD,OAAO3C,CAAC,EAAE,EAAE;UACZ,CAACwC,EAAE,GAAG,IAAI,CAAC3H,IAAI,CAACkG,SAAS,MAAM,IAAI,IAAIyB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,UAAU,CAACvC,GAAG,CAAC,CAAA;EAClF;UACA,IAAI,iBAAiB,IAAIA,GAAG,EAAE;EAC1BA,UAAAA,GAAG,CAACS,eAAe,GAAG,IAAI,CAACjG,IAAI,CAACiG,eAAe,CAAA;EACnD,SAAA;EACA,QAAA,IAAI,IAAI,CAACjG,IAAI,CAACgI,cAAc,EAAE;EAC1BxC,UAAAA,GAAG,CAACyC,OAAO,GAAG,IAAI,CAACjI,IAAI,CAACgI,cAAc,CAAA;EAC1C,SAAA;UACAxC,GAAG,CAAC0C,kBAAkB,GAAG,YAAM;EAC3B,UAAA,IAAIP,EAAE,CAAA;EACN,UAAA,IAAInC,GAAG,CAAC7C,UAAU,KAAK,CAAC,EAAE;cACtB,CAACgF,EAAE,GAAGD,MAAI,CAAC1H,IAAI,CAACkG,SAAS,MAAM,IAAI,IAAIyB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,YAAY,CAAC3C,GAAG,CAAC,CAAA;EACxF,WAAA;EACA,UAAA,IAAI,CAAC,KAAKA,GAAG,CAAC7C,UAAU,EACpB,OAAA;YACJ,IAAI,GAAG,KAAK6C,GAAG,CAAC4C,MAAM,IAAI,IAAI,KAAK5C,GAAG,CAAC4C,MAAM,EAAE;cAC3CV,MAAI,CAACW,MAAM,EAAE,CAAA;EACjB,WAAC,MACI;EACD;EACA;cACAX,MAAI,CAACxH,YAAY,CAAC,YAAM;EACpBwH,cAAAA,MAAI,CAACnF,OAAO,CAAC,OAAOiD,GAAG,CAAC4C,MAAM,KAAK,QAAQ,GAAG5C,GAAG,CAAC4C,MAAM,GAAG,CAAC,CAAC,CAAA;eAChE,EAAE,CAAC,CAAC,CAAA;EACT,WAAA;WACH,CAAA;EACD5C,QAAAA,GAAG,CAACxC,IAAI,CAAC,IAAI,CAACpM,IAAI,CAAC,CAAA;SACtB,CACD,OAAOuO,CAAC,EAAE;EACN;EACA;EACA;UACA,IAAI,CAACjF,YAAY,CAAC,YAAM;EACpBwH,UAAAA,MAAI,CAACnF,OAAO,CAAC4C,CAAC,CAAC,CAAA;WAClB,EAAE,CAAC,CAAC,CAAA;EACL,QAAA,OAAA;EACJ,OAAA;EACA,MAAA,IAAI,OAAOmD,QAAQ,KAAK,WAAW,EAAE;EACjC,QAAA,IAAI,CAACC,KAAK,GAAGrB,OAAO,CAACsB,aAAa,EAAE,CAAA;UACpCtB,OAAO,CAACuB,QAAQ,CAAC,IAAI,CAACF,KAAK,CAAC,GAAG,IAAI,CAAA;EACvC,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA9R,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAC,OAAQwC,CAAAA,GAAG,EAAE;QACT,IAAI,CAAClG,YAAY,CAAC,OAAO,EAAEkG,GAAG,EAAE,IAAI,CAACS,GAAG,CAAC,CAAA;EACzC,MAAA,IAAI,CAACkD,OAAO,CAAC,IAAI,CAAC,CAAA;EACtB,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAjS,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAoG,OAAQC,CAAAA,SAAS,EAAE;EACf,MAAA,IAAI,WAAW,KAAK,OAAO,IAAI,CAACnD,GAAG,IAAI,IAAI,KAAK,IAAI,CAACA,GAAG,EAAE;EACtD,QAAA,OAAA;EACJ,OAAA;EACA,MAAA,IAAI,CAACA,GAAG,CAAC0C,kBAAkB,GAAG5C,KAAK,CAAA;EACnC,MAAA,IAAIqD,SAAS,EAAE;UACX,IAAI;EACA,UAAA,IAAI,CAACnD,GAAG,CAACoD,KAAK,EAAE,CAAA;EACpB,SAAC,CACD,OAAOzD,CAAC,EAAE,EAAE;EAChB,OAAA;EACA,MAAA,IAAI,OAAOmD,QAAQ,KAAK,WAAW,EAAE;EACjC,QAAA,OAAOpB,OAAO,CAACuB,QAAQ,CAAC,IAAI,CAACF,KAAK,CAAC,CAAA;EACvC,OAAA;QACA,IAAI,CAAC/C,GAAG,GAAG,IAAI,CAAA;EACnB,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA/O,GAAA,EAAA,QAAA;MAAA6L,KAAA,EAKA,SAAA+F,MAAAA,GAAS;EACL,MAAA,IAAMzR,IAAI,GAAG,IAAI,CAAC4O,GAAG,CAACqD,YAAY,CAAA;QAClC,IAAIjS,IAAI,KAAK,IAAI,EAAE;EACf,QAAA,IAAI,CAACiI,YAAY,CAAC,MAAM,EAAEjI,IAAI,CAAC,CAAA;EAC/B,QAAA,IAAI,CAACiI,YAAY,CAAC,SAAS,CAAC,CAAA;UAC5B,IAAI,CAAC6J,OAAO,EAAE,CAAA;EAClB,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAjS,GAAA,EAAA,OAAA;MAAA6L,KAAA,EAKA,SAAAsG,KAAAA,GAAQ;QACJ,IAAI,CAACF,OAAO,EAAE,CAAA;EAClB,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,EAAA,OAAAxB,OAAA,CAAA;EAAA,CAAA,CA7IwBxJ,OAAO,CAAA,CAAA;EA+IpCwJ,OAAO,CAACsB,aAAa,GAAG,CAAC,CAAA;EACzBtB,OAAO,CAACuB,QAAQ,GAAG,EAAE,CAAA;EACrB;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOH,QAAQ,KAAK,WAAW,EAAE;EACjC;EACA,EAAA,IAAI,OAAOQ,WAAW,KAAK,UAAU,EAAE;EACnC;EACAA,IAAAA,WAAW,CAAC,UAAU,EAAEC,aAAa,CAAC,CAAA;EAC1C,GAAC,MACI,IAAI,OAAOlL,gBAAgB,KAAK,UAAU,EAAE;MAC7C,IAAMmL,gBAAgB,GAAG,YAAY,IAAIrJ,cAAU,GAAG,UAAU,GAAG,QAAQ,CAAA;EAC3E9B,IAAAA,gBAAgB,CAACmL,gBAAgB,EAAED,aAAa,EAAE,KAAK,CAAC,CAAA;EAC5D,GAAA;EACJ,CAAA;EACA,SAASA,aAAaA,GAAG;EACrB,EAAA,KAAK,IAAI9P,CAAC,IAAIiO,OAAO,CAACuB,QAAQ,EAAE;MAC5B,IAAIvB,OAAO,CAACuB,QAAQ,CAAChJ,cAAc,CAACxG,CAAC,CAAC,EAAE;QACpCiO,OAAO,CAACuB,QAAQ,CAACxP,CAAC,CAAC,CAAC2P,KAAK,EAAE,CAAA;EAC/B,KAAA;EACJ,GAAA;EACJ;;ECpYO,IAAMK,QAAQ,GAAI,YAAM;EAC3B,EAAA,IAAMC,kBAAkB,GAAG,OAAOC,OAAO,KAAK,UAAU,IAAI,OAAOA,OAAO,CAACC,OAAO,KAAK,UAAU,CAAA;EACjG,EAAA,IAAIF,kBAAkB,EAAE;EACpB,IAAA,OAAO,UAACzK,EAAE,EAAA;QAAA,OAAK0K,OAAO,CAACC,OAAO,EAAE,CAACzQ,IAAI,CAAC8F,EAAE,CAAC,CAAA;EAAA,KAAA,CAAA;EAC7C,GAAC,MACI;MACD,OAAO,UAACA,EAAE,EAAEyB,YAAY,EAAA;EAAA,MAAA,OAAKA,YAAY,CAACzB,EAAE,EAAE,CAAC,CAAC,CAAA;EAAA,KAAA,CAAA;EACpD,GAAA;EACJ,CAAC,EAAG,CAAA;EACG,IAAM4K,SAAS,GAAG1J,cAAU,CAAC0J,SAAS,IAAI1J,cAAU,CAAC2J,YAAY,CAAA;EACjE,IAAMC,qBAAqB,GAAG,IAAI,CAAA;EAClC,IAAMC,iBAAiB,GAAG,aAAa;;ECP9C;EACA,IAAMC,aAAa,GAAG,OAAOC,SAAS,KAAK,WAAW,IAClD,OAAOA,SAAS,CAACC,OAAO,KAAK,QAAQ,IACrCD,SAAS,CAACC,OAAO,CAACC,WAAW,EAAE,KAAK,aAAa,CAAA;EACxCC,IAAAA,EAAE,0BAAAlE,UAAA,EAAA;IAAAxE,SAAA,CAAA0I,EAAA,EAAAlE,UAAA,CAAA,CAAA;EAAA,EAAA,IAAAvE,MAAA,GAAAC,YAAA,CAAAwI,EAAA,CAAA,CAAA;EACX;EACJ;EACA;EACA;EACA;EACA;IACI,SAAAA,EAAAA,CAAY7J,IAAI,EAAE;EAAA,IAAA,IAAAyB,KAAA,CAAA;EAAAC,IAAAA,eAAA,OAAAmI,EAAA,CAAA,CAAA;EACdpI,IAAAA,KAAA,GAAAL,MAAA,CAAAnK,IAAA,OAAM+I,IAAI,CAAA,CAAA;EACVyB,IAAAA,KAAA,CAAKhK,cAAc,GAAG,CAACuI,IAAI,CAACgG,WAAW,CAAA;EAAC,IAAA,OAAAvE,KAAA,CAAA;EAC5C,GAAA;EAACE,EAAAA,YAAA,CAAAkI,EAAA,EAAA,CAAA;MAAApT,GAAA,EAAA,MAAA;MAAA0P,GAAA,EACD,SAAAA,GAAAA,GAAW;EACP,MAAA,OAAO,WAAW,CAAA;EACtB,KAAA;EAAC,GAAA,EAAA;MAAA1P,GAAA,EAAA,QAAA;MAAA6L,KAAA,EACD,SAAAM,MAAAA,GAAS;EACL,MAAA,IAAI,CAAC,IAAI,CAACkH,KAAK,EAAE,EAAE;EACf;EACA,QAAA,OAAA;EACJ,OAAA;EACA,MAAA,IAAMnD,GAAG,GAAG,IAAI,CAACA,GAAG,EAAE,CAAA;EACtB,MAAA,IAAMoD,SAAS,GAAG,IAAI,CAAC/J,IAAI,CAAC+J,SAAS,CAAA;EACrC;EACA,MAAA,IAAM/J,IAAI,GAAGyJ,aAAa,GACpB,EAAE,GACFrK,IAAI,CAAC,IAAI,CAACY,IAAI,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,cAAc,EAAE,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAA;EAC1N,MAAA,IAAI,IAAI,CAACA,IAAI,CAAC4H,YAAY,EAAE;EACxB5H,QAAAA,IAAI,CAACgK,OAAO,GAAG,IAAI,CAAChK,IAAI,CAAC4H,YAAY,CAAA;EACzC,OAAA;QACA,IAAI;EACA,QAAA,IAAI,CAACqC,EAAE,GACHV,qBAAqB,IAAI,CAACE,aAAa,GACjCM,SAAS,GACL,IAAIV,SAAS,CAAC1C,GAAG,EAAEoD,SAAS,CAAC,GAC7B,IAAIV,SAAS,CAAC1C,GAAG,CAAC,GACtB,IAAI0C,SAAS,CAAC1C,GAAG,EAAEoD,SAAS,EAAE/J,IAAI,CAAC,CAAA;SAChD,CACD,OAAO+E,GAAG,EAAE;EACR,QAAA,OAAO,IAAI,CAAClG,YAAY,CAAC,OAAO,EAAEkG,GAAG,CAAC,CAAA;EAC1C,OAAA;QACA,IAAI,CAACkF,EAAE,CAAChQ,UAAU,GAAG,IAAI,CAACoI,MAAM,CAACpI,UAAU,CAAA;QAC3C,IAAI,CAACiQ,iBAAiB,EAAE,CAAA;EAC5B,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAzT,GAAA,EAAA,mBAAA;MAAA6L,KAAA,EAKA,SAAA4H,iBAAAA,GAAoB;EAAA,MAAA,IAAAjI,MAAA,GAAA,IAAA,CAAA;EAChB,MAAA,IAAI,CAACgI,EAAE,CAACE,MAAM,GAAG,YAAM;EACnB,QAAA,IAAIlI,MAAI,CAACjC,IAAI,CAACoK,SAAS,EAAE;EACrBnI,UAAAA,MAAI,CAACgI,EAAE,CAACI,OAAO,CAACC,KAAK,EAAE,CAAA;EAC3B,SAAA;UACArI,MAAI,CAACiB,MAAM,EAAE,CAAA;SAChB,CAAA;EACD,MAAA,IAAI,CAAC+G,EAAE,CAACM,OAAO,GAAG,UAACC,UAAU,EAAA;UAAA,OAAKvI,MAAI,CAACc,OAAO,CAAC;EAC3CxB,UAAAA,WAAW,EAAE,6BAA6B;EAC1CC,UAAAA,OAAO,EAAEgJ,UAAAA;EACb,SAAC,CAAC,CAAA;EAAA,OAAA,CAAA;EACF,MAAA,IAAI,CAACP,EAAE,CAACQ,SAAS,GAAG,UAACC,EAAE,EAAA;EAAA,QAAA,OAAKzI,MAAI,CAACkB,MAAM,CAACuH,EAAE,CAAC9T,IAAI,CAAC,CAAA;EAAA,OAAA,CAAA;EAChD,MAAA,IAAI,CAACqT,EAAE,CAACU,OAAO,GAAG,UAACxF,CAAC,EAAA;EAAA,QAAA,OAAKlD,MAAI,CAACM,OAAO,CAAC,iBAAiB,EAAE4C,CAAC,CAAC,CAAA;EAAA,OAAA,CAAA;EAC/D,KAAA;EAAC,GAAA,EAAA;MAAA1O,GAAA,EAAA,OAAA;EAAA6L,IAAAA,KAAA,EACD,SAAAW,KAAMrI,CAAAA,OAAO,EAAE;EAAA,MAAA,IAAA2L,MAAA,GAAA,IAAA,CAAA;QACX,IAAI,CAACrE,QAAQ,GAAG,KAAK,CAAA;EACrB;EACA;QAAA,IAAA0I,KAAA,GAAAA,SAAAA,KAAAA,GACyC;EACrC,QAAA,IAAMnS,MAAM,GAAGmC,OAAO,CAAC3B,CAAC,CAAC,CAAA;UACzB,IAAM4R,UAAU,GAAG5R,CAAC,KAAK2B,OAAO,CAAC1B,MAAM,GAAG,CAAC,CAAA;UAC3C3B,YAAY,CAACkB,MAAM,EAAE8N,MAAI,CAAC9O,cAAc,EAAE,UAACb,IAAI,EAAK;EAChD;YACA,IAAMoJ,IAAI,GAAG,EAAE,CAAA;EAcf;EACA;EACA;YACA,IAAI;EACA,YAAA,IAAIuJ,qBAAqB,EAAE;EACvB;EACAhD,cAAAA,MAAI,CAAC0D,EAAE,CAACjH,IAAI,CAACpM,IAAI,CAAC,CAAA;EACtB,aAGA;EACJ,WAAC,CACD,OAAOuO,CAAC,EAAE,EACV;EACA,UAAA,IAAI0F,UAAU,EAAE;EACZ;EACA;EACA5B,YAAAA,QAAQ,CAAC,YAAM;gBACX1C,MAAI,CAACrE,QAAQ,GAAG,IAAI,CAAA;EACpBqE,cAAAA,MAAI,CAAC1H,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,aAAC,EAAE0H,MAAI,CAACrG,YAAY,CAAC,CAAA;EACzB,WAAA;EACJ,SAAC,CAAC,CAAA;SACL,CAAA;EA1CD,MAAA,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,OAAO,CAAC1B,MAAM,EAAED,CAAC,EAAE,EAAA;UAAA2R,KAAA,EAAA,CAAA;EAAA,OAAA;EA2C3C,KAAA;EAAC,GAAA,EAAA;MAAAnU,GAAA,EAAA,SAAA;MAAA6L,KAAA,EACD,SAAAQ,OAAAA,GAAU;EACN,MAAA,IAAI,OAAO,IAAI,CAACmH,EAAE,KAAK,WAAW,EAAE;EAChC,QAAA,IAAI,CAACA,EAAE,CAACpH,KAAK,EAAE,CAAA;UACf,IAAI,CAACoH,EAAE,GAAG,IAAI,CAAA;EAClB,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAxT,GAAA,EAAA,KAAA;MAAA6L,KAAA,EAKA,SAAAqE,GAAAA,GAAM;QACF,IAAMlD,MAAM,GAAG,IAAI,CAACzD,IAAI,CAACkE,MAAM,GAAG,KAAK,GAAG,IAAI,CAAA;EAC9C,MAAA,IAAM9B,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,EAAE,CAAA;EAC9B;EACA,MAAA,IAAI,IAAI,CAACpC,IAAI,CAAC4G,iBAAiB,EAAE;UAC7BxE,KAAK,CAAC,IAAI,CAACpC,IAAI,CAAC6G,cAAc,CAAC,GAAGlC,KAAK,EAAE,CAAA;EAC7C,OAAA;EACA;EACA,MAAA,IAAI,CAAC,IAAI,CAAClN,cAAc,EAAE;UACtB2K,KAAK,CAAC2E,GAAG,GAAG,CAAC,CAAA;EACjB,OAAA;EACA,MAAA,OAAO,IAAI,CAACvD,SAAS,CAACC,MAAM,EAAErB,KAAK,CAAC,CAAA;EACxC,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAA3L,GAAA,EAAA,OAAA;MAAA6L,KAAA,EAMA,SAAAwH,KAAAA,GAAQ;QACJ,OAAO,CAAC,CAACT,SAAS,CAAA;EACtB,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,EAAA,OAAAQ,EAAA,CAAA;EAAA,CAAA,CA7ImB/H,SAAS,CAAA;;ECNpBgJ,IAAAA,EAAE,0BAAAnF,UAAA,EAAA;IAAAxE,SAAA,CAAA2J,EAAA,EAAAnF,UAAA,CAAA,CAAA;EAAA,EAAA,IAAAvE,MAAA,GAAAC,YAAA,CAAAyJ,EAAA,CAAA,CAAA;EAAA,EAAA,SAAAA,EAAA,GAAA;EAAApJ,IAAAA,eAAA,OAAAoJ,EAAA,CAAA,CAAA;EAAA,IAAA,OAAA1J,MAAA,CAAAjD,KAAA,CAAA,IAAA,EAAAC,SAAA,CAAA,CAAA;EAAA,GAAA;EAAAuD,EAAAA,YAAA,CAAAmJ,EAAA,EAAA,CAAA;MAAArU,GAAA,EAAA,MAAA;MAAA0P,GAAA,EACX,SAAAA,GAAAA,GAAW;EACP,MAAA,OAAO,cAAc,CAAA;EACzB,KAAA;EAAC,GAAA,EAAA;MAAA1P,GAAA,EAAA,QAAA;MAAA6L,KAAA,EACD,SAAAM,MAAAA,GAAS;EAAA,MAAA,IAAAnB,KAAA,GAAA,IAAA,CAAA;EACL;EACA,MAAA,IAAI,OAAOsJ,YAAY,KAAK,UAAU,EAAE;EACpC,QAAA,OAAA;EACJ,OAAA;EACA;QACA,IAAI,CAACC,SAAS,GAAG,IAAID,YAAY,CAAC,IAAI,CAACvH,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,CAACxD,IAAI,CAACiL,gBAAgB,CAAC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAA;EACjG,MAAA,IAAI,CAACF,SAAS,CAACG,MAAM,CAChBxS,IAAI,CAAC,YAAM;UACZ8I,KAAI,CAACsB,OAAO,EAAE,CAAA;EAClB,OAAC,CAAC,CAAA,OAAA,CACQ,CAAC,UAACgC,GAAG,EAAK;EAChBtD,QAAAA,KAAI,CAACc,OAAO,CAAC,oBAAoB,EAAEwC,GAAG,CAAC,CAAA;EAC3C,OAAC,CAAC,CAAA;EACF;EACA,MAAA,IAAI,CAACiG,SAAS,CAACI,KAAK,CAACzS,IAAI,CAAC,YAAM;UAC5B8I,KAAI,CAACuJ,SAAS,CAACK,yBAAyB,EAAE,CAAC1S,IAAI,CAAC,UAAC2S,MAAM,EAAK;EACxD,UAAA,IAAMC,aAAa,GAAG1O,yBAAyB,CAACsH,MAAM,CAACqH,gBAAgB,EAAE/J,KAAI,CAACY,MAAM,CAACpI,UAAU,CAAC,CAAA;EAChG,UAAA,IAAMwR,MAAM,GAAGH,MAAM,CAACI,QAAQ,CAACC,WAAW,CAACJ,aAAa,CAAC,CAACK,SAAS,EAAE,CAAA;EACrE,UAAA,IAAMC,aAAa,GAAGxQ,yBAAyB,EAAE,CAAA;YACjDwQ,aAAa,CAACH,QAAQ,CAACI,MAAM,CAACR,MAAM,CAACpJ,QAAQ,CAAC,CAAA;YAC9CT,KAAI,CAACsK,MAAM,GAAGF,aAAa,CAAC3J,QAAQ,CAAC8J,SAAS,EAAE,CAAA;EAChD,UAAA,IAAMC,IAAI,GAAG,SAAPA,IAAIA,GAAS;cACfR,MAAM,CACDQ,IAAI,EAAE,CACNtT,IAAI,CAAC,UAAAnB,IAAA,EAAqB;EAAA,cAAA,IAAlB0U,IAAI,GAAA1U,IAAA,CAAJ0U,IAAI;kBAAE5J,KAAK,GAAA9K,IAAA,CAAL8K,KAAK,CAAA;EACpB,cAAA,IAAI4J,IAAI,EAAE;EACN,gBAAA,OAAA;EACJ,eAAA;EACAzK,cAAAA,KAAI,CAAC2B,QAAQ,CAACd,KAAK,CAAC,CAAA;EACpB2J,cAAAA,IAAI,EAAE,CAAA;eACT,CAAC,SACQ,CAAC,UAAClH,GAAG,EAAK,EACnB,CAAC,CAAA;aACL,CAAA;EACDkH,UAAAA,IAAI,EAAE,CAAA;EACN,UAAA,IAAMxT,MAAM,GAAG;EAAE9B,YAAAA,IAAI,EAAE,MAAA;aAAQ,CAAA;EAC/B,UAAA,IAAI8K,KAAI,CAACW,KAAK,CAAC0E,GAAG,EAAE;cAChBrO,MAAM,CAAC7B,IAAI,GAAA,aAAA,CAAAwO,MAAA,CAAc3D,KAAI,CAACW,KAAK,CAAC0E,GAAG,EAAI,KAAA,CAAA,CAAA;EAC/C,WAAA;YACArF,KAAI,CAACsK,MAAM,CAAC9I,KAAK,CAACxK,MAAM,CAAC,CAACE,IAAI,CAAC,YAAA;EAAA,YAAA,OAAM8I,KAAI,CAACyB,MAAM,EAAE,CAAA;aAAC,CAAA,CAAA;EACvD,SAAC,CAAC,CAAA;EACN,OAAC,CAAC,CAAA;EACN,KAAA;EAAC,GAAA,EAAA;MAAAzM,GAAA,EAAA,OAAA;EAAA6L,IAAAA,KAAA,EACD,SAAAW,KAAMrI,CAAAA,OAAO,EAAE;EAAA,MAAA,IAAAqH,MAAA,GAAA,IAAA,CAAA;QACX,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAA;QAAC,IAAA0I,KAAA,GAAAA,SAAAA,KAAAA,GACmB;EACrC,QAAA,IAAMnS,MAAM,GAAGmC,OAAO,CAAC3B,CAAC,CAAC,CAAA;UACzB,IAAM4R,UAAU,GAAG5R,CAAC,KAAK2B,OAAO,CAAC1B,MAAM,GAAG,CAAC,CAAA;UAC3C+I,MAAI,CAAC8J,MAAM,CAAC9I,KAAK,CAACxK,MAAM,CAAC,CAACE,IAAI,CAAC,YAAM;EACjC,UAAA,IAAIkS,UAAU,EAAE;EACZ5B,YAAAA,QAAQ,CAAC,YAAM;gBACXhH,MAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;EACpBD,cAAAA,MAAI,CAACpD,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,aAAC,EAAEoD,MAAI,CAAC/B,YAAY,CAAC,CAAA;EACzB,WAAA;EACJ,SAAC,CAAC,CAAA;SACL,CAAA;EAXD,MAAA,KAAK,IAAIjH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,OAAO,CAAC1B,MAAM,EAAED,CAAC,EAAE,EAAA;UAAA2R,KAAA,EAAA,CAAA;EAAA,OAAA;EAY3C,KAAA;EAAC,GAAA,EAAA;MAAAnU,GAAA,EAAA,SAAA;MAAA6L,KAAA,EACD,SAAAQ,OAAAA,GAAU;EACN,MAAA,IAAI6E,EAAE,CAAA;QACN,CAACA,EAAE,GAAG,IAAI,CAACqD,SAAS,MAAM,IAAI,IAAIrD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC9E,KAAK,EAAE,CAAA;EACzE,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,EAAA,OAAAiI,EAAA,CAAA;EAAA,CAAA,CAlEmBhJ,SAAS,CAAA;;ECA1B,IAAMqK,UAAU,GAAG;EACtBC,EAAAA,SAAS,EAAEvC,EAAE;EACbwC,EAAAA,YAAY,EAAEvB,EAAE;EAChBlF,EAAAA,OAAO,EAAEF,OAAAA;EACb,CAAC;;ECPD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAM4G,EAAE,GAAG,qPAAqP,CAAA;EAChQ,IAAMC,KAAK,GAAG,CACV,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAChJ,CAAA;EACM,SAASC,KAAKA,CAAChM,GAAG,EAAE;IACvB,IAAMiM,GAAG,GAAGjM,GAAG;EAAEkM,IAAAA,CAAC,GAAGlM,GAAG,CAACwD,OAAO,CAAC,GAAG,CAAC;EAAEmB,IAAAA,CAAC,GAAG3E,GAAG,CAACwD,OAAO,CAAC,GAAG,CAAC,CAAA;IAC3D,IAAI0I,CAAC,IAAI,CAAC,CAAC,IAAIvH,CAAC,IAAI,CAAC,CAAC,EAAE;EACpB3E,IAAAA,GAAG,GAAGA,GAAG,CAACnG,SAAS,CAAC,CAAC,EAAEqS,CAAC,CAAC,GAAGlM,GAAG,CAACnG,SAAS,CAACqS,CAAC,EAAEvH,CAAC,CAAC,CAACwH,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGnM,GAAG,CAACnG,SAAS,CAAC8K,CAAC,EAAE3E,GAAG,CAACtH,MAAM,CAAC,CAAA;EACrG,GAAA;IACA,IAAI0T,CAAC,GAAGN,EAAE,CAACO,IAAI,CAACrM,GAAG,IAAI,EAAE,CAAC;MAAEmG,GAAG,GAAG,EAAE;EAAE1N,IAAAA,CAAC,GAAG,EAAE,CAAA;IAC5C,OAAOA,CAAC,EAAE,EAAE;EACR0N,IAAAA,GAAG,CAAC4F,KAAK,CAACtT,CAAC,CAAC,CAAC,GAAG2T,CAAC,CAAC3T,CAAC,CAAC,IAAI,EAAE,CAAA;EAC9B,GAAA;IACA,IAAIyT,CAAC,IAAI,CAAC,CAAC,IAAIvH,CAAC,IAAI,CAAC,CAAC,EAAE;MACpBwB,GAAG,CAACmG,MAAM,GAAGL,GAAG,CAAA;MAChB9F,GAAG,CAACoG,IAAI,GAAGpG,GAAG,CAACoG,IAAI,CAAC1S,SAAS,CAAC,CAAC,EAAEsM,GAAG,CAACoG,IAAI,CAAC7T,MAAM,GAAG,CAAC,CAAC,CAACyT,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;MACxEhG,GAAG,CAACqG,SAAS,GAAGrG,GAAG,CAACqG,SAAS,CAACL,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;MAClFhG,GAAG,CAACsG,OAAO,GAAG,IAAI,CAAA;EACtB,GAAA;IACAtG,GAAG,CAACuG,SAAS,GAAGA,SAAS,CAACvG,GAAG,EAAEA,GAAG,CAAC,MAAM,CAAC,CAAC,CAAA;IAC3CA,GAAG,CAACwG,QAAQ,GAAGA,QAAQ,CAACxG,GAAG,EAAEA,GAAG,CAAC,OAAO,CAAC,CAAC,CAAA;EAC1C,EAAA,OAAOA,GAAG,CAAA;EACd,CAAA;EACA,SAASuG,SAASA,CAAC7V,GAAG,EAAEwM,IAAI,EAAE;IAC1B,IAAMuJ,IAAI,GAAG,UAAU;EAAEC,IAAAA,KAAK,GAAGxJ,IAAI,CAAC8I,OAAO,CAACS,IAAI,EAAE,GAAG,CAAC,CAACnV,KAAK,CAAC,GAAG,CAAC,CAAA;EACnE,EAAA,IAAI4L,IAAI,CAACjH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,IAAIiH,IAAI,CAAC3K,MAAM,KAAK,CAAC,EAAE;EAC9CmU,IAAAA,KAAK,CAAC3O,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;EACtB,GAAA;IACA,IAAImF,IAAI,CAACjH,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MACvByQ,KAAK,CAAC3O,MAAM,CAAC2O,KAAK,CAACnU,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;EACrC,GAAA;EACA,EAAA,OAAOmU,KAAK,CAAA;EAChB,CAAA;EACA,SAASF,QAAQA,CAACxG,GAAG,EAAEvE,KAAK,EAAE;IAC1B,IAAMxL,IAAI,GAAG,EAAE,CAAA;IACfwL,KAAK,CAACuK,OAAO,CAAC,2BAA2B,EAAE,UAAUW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC7D,IAAA,IAAID,EAAE,EAAE;EACJ3W,MAAAA,IAAI,CAAC2W,EAAE,CAAC,GAAGC,EAAE,CAAA;EACjB,KAAA;EACJ,GAAC,CAAC,CAAA;EACF,EAAA,OAAO5W,IAAI,CAAA;EACf;;ECrDa6W,IAAAA,QAAM,0BAAA1L,QAAA,EAAA;IAAAZ,SAAA,CAAAsM,MAAA,EAAA1L,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAX,MAAA,GAAAC,YAAA,CAAAoM,MAAA,CAAA,CAAA;EACf;EACJ;EACA;EACA;EACA;EACA;IACI,SAAAA,MAAAA,CAAY9G,GAAG,EAAa;EAAA,IAAA,IAAAlF,KAAA,CAAA;EAAA,IAAA,IAAXzB,IAAI,GAAA5B,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAAsF,SAAA,GAAAtF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;EAAAsD,IAAAA,eAAA,OAAA+L,MAAA,CAAA,CAAA;MACtBhM,KAAA,GAAAL,MAAA,CAAAnK,IAAA,CAAA,IAAA,CAAA,CAAA;MACAwK,KAAA,CAAKxH,UAAU,GAAGuP,iBAAiB,CAAA;MACnC/H,KAAA,CAAKiM,WAAW,GAAG,EAAE,CAAA;EACrB,IAAA,IAAI/G,GAAG,IAAI,QAAQ,KAAAgH,OAAA,CAAYhH,GAAG,CAAE,EAAA;EAChC3G,MAAAA,IAAI,GAAG2G,GAAG,CAAA;EACVA,MAAAA,GAAG,GAAG,IAAI,CAAA;EACd,KAAA;EACA,IAAA,IAAIA,GAAG,EAAE;EACLA,MAAAA,GAAG,GAAG6F,KAAK,CAAC7F,GAAG,CAAC,CAAA;EAChB3G,MAAAA,IAAI,CAAC+D,QAAQ,GAAG4C,GAAG,CAACoG,IAAI,CAAA;EACxB/M,MAAAA,IAAI,CAACkE,MAAM,GAAGyC,GAAG,CAAClJ,QAAQ,KAAK,OAAO,IAAIkJ,GAAG,CAAClJ,QAAQ,KAAK,KAAK,CAAA;EAChEuC,MAAAA,IAAI,CAACiE,IAAI,GAAG0C,GAAG,CAAC1C,IAAI,CAAA;QACpB,IAAI0C,GAAG,CAACvE,KAAK,EACTpC,IAAI,CAACoC,KAAK,GAAGuE,GAAG,CAACvE,KAAK,CAAA;EAC9B,KAAC,MACI,IAAIpC,IAAI,CAAC+M,IAAI,EAAE;QAChB/M,IAAI,CAAC+D,QAAQ,GAAGyI,KAAK,CAACxM,IAAI,CAAC+M,IAAI,CAAC,CAACA,IAAI,CAAA;EACzC,KAAA;EACAhN,IAAAA,qBAAqB,CAAAoC,sBAAA,CAAAV,KAAA,CAAA,EAAOzB,IAAI,CAAC,CAAA;MACjCyB,KAAA,CAAKyC,MAAM,GACP,IAAI,IAAIlE,IAAI,CAACkE,MAAM,GACblE,IAAI,CAACkE,MAAM,GACX,OAAO2B,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAKA,QAAQ,CAACpI,QAAQ,CAAA;MAC3E,IAAIuC,IAAI,CAAC+D,QAAQ,IAAI,CAAC/D,IAAI,CAACiE,IAAI,EAAE;EAC7B;QACAjE,IAAI,CAACiE,IAAI,GAAGxC,KAAA,CAAKyC,MAAM,GAAG,KAAK,GAAG,IAAI,CAAA;EAC1C,KAAA;EACAzC,IAAAA,KAAA,CAAKsC,QAAQ,GACT/D,IAAI,CAAC+D,QAAQ,KACR,OAAO8B,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAAC9B,QAAQ,GAAG,WAAW,CAAC,CAAA;MAC3EtC,KAAA,CAAKwC,IAAI,GACLjE,IAAI,CAACiE,IAAI,KACJ,OAAO4B,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAAC5B,IAAI,GAC3C4B,QAAQ,CAAC5B,IAAI,GACbxC,KAAA,CAAKyC,MAAM,GACP,KAAK,GACL,IAAI,CAAC,CAAA;EACvBzC,IAAAA,KAAA,CAAK0K,UAAU,GAAGnM,IAAI,CAACmM,UAAU,IAAI,CACjC,SAAS,EACT,WAAW,EACX,cAAc,CACjB,CAAA;MACD1K,KAAA,CAAKiM,WAAW,GAAG,EAAE,CAAA;MACrBjM,KAAA,CAAKmM,aAAa,GAAG,CAAC,CAAA;EACtBnM,IAAAA,KAAA,CAAKzB,IAAI,GAAGiH,QAAA,CAAc;EACtBpD,MAAAA,IAAI,EAAE,YAAY;EAClBgK,MAAAA,KAAK,EAAE,KAAK;EACZ5H,MAAAA,eAAe,EAAE,KAAK;EACtB6H,MAAAA,OAAO,EAAE,IAAI;EACbjH,MAAAA,cAAc,EAAE,GAAG;EACnBkH,MAAAA,eAAe,EAAE,KAAK;EACtBC,MAAAA,gBAAgB,EAAE,IAAI;EACtBC,MAAAA,kBAAkB,EAAE,IAAI;EACxBC,MAAAA,iBAAiB,EAAE;EACfC,QAAAA,SAAS,EAAE,IAAA;SACd;QACDlD,gBAAgB,EAAE,EAAE;EACpBmD,MAAAA,mBAAmB,EAAE,KAAA;OACxB,EAAEpO,IAAI,CAAC,CAAA;MACRyB,KAAA,CAAKzB,IAAI,CAAC6D,IAAI,GACVpC,KAAA,CAAKzB,IAAI,CAAC6D,IAAI,CAAC8I,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAC5BlL,KAAA,CAAKzB,IAAI,CAACgO,gBAAgB,GAAG,GAAG,GAAG,EAAE,CAAC,CAAA;MAC/C,IAAI,OAAOvM,KAAA,CAAKzB,IAAI,CAACoC,KAAK,KAAK,QAAQ,EAAE;EACrCX,MAAAA,KAAA,CAAKzB,IAAI,CAACoC,KAAK,GAAGhJ,MAAM,CAACqI,KAAA,CAAKzB,IAAI,CAACoC,KAAK,CAAC,CAAA;EAC7C,KAAA;EACA;MACAX,KAAA,CAAK4M,EAAE,GAAG,IAAI,CAAA;MACd5M,KAAA,CAAK6M,QAAQ,GAAG,IAAI,CAAA;MACpB7M,KAAA,CAAK8M,YAAY,GAAG,IAAI,CAAA;MACxB9M,KAAA,CAAK+M,WAAW,GAAG,IAAI,CAAA;EACvB;MACA/M,KAAA,CAAKgN,gBAAgB,GAAG,IAAI,CAAA;EAC5B,IAAA,IAAI,OAAO5Q,gBAAgB,KAAK,UAAU,EAAE;EACxC,MAAA,IAAI4D,KAAA,CAAKzB,IAAI,CAACoO,mBAAmB,EAAE;EAC/B;EACA;EACA;UACA3M,KAAA,CAAKiN,yBAAyB,GAAG,YAAM;YACnC,IAAIjN,KAAA,CAAKuJ,SAAS,EAAE;EAChB;EACAvJ,YAAAA,KAAA,CAAKuJ,SAAS,CAAC1M,kBAAkB,EAAE,CAAA;EACnCmD,YAAAA,KAAA,CAAKuJ,SAAS,CAACnI,KAAK,EAAE,CAAA;EAC1B,WAAA;WACH,CAAA;UACDhF,gBAAgB,CAAC,cAAc,EAAE4D,KAAA,CAAKiN,yBAAyB,EAAE,KAAK,CAAC,CAAA;EAC3E,OAAA;EACA,MAAA,IAAIjN,KAAA,CAAKsC,QAAQ,KAAK,WAAW,EAAE;UAC/BtC,KAAA,CAAKkN,oBAAoB,GAAG,YAAM;EAC9BlN,UAAAA,KAAA,CAAKsB,OAAO,CAAC,iBAAiB,EAAE;EAC5BxB,YAAAA,WAAW,EAAE,yBAAA;EACjB,WAAC,CAAC,CAAA;WACL,CAAA;UACD1D,gBAAgB,CAAC,SAAS,EAAE4D,KAAA,CAAKkN,oBAAoB,EAAE,KAAK,CAAC,CAAA;EACjE,OAAA;EACJ,KAAA;MACAlN,KAAA,CAAKiB,IAAI,EAAE,CAAA;EAAC,IAAA,OAAAjB,KAAA,CAAA;EAChB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EANIE,EAAAA,YAAA,CAAA8L,MAAA,EAAA,CAAA;MAAAhX,GAAA,EAAA,iBAAA;EAAA6L,IAAAA,KAAA,EAOA,SAAAsM,eAAgB1D,CAAAA,IAAI,EAAE;EAClB,MAAA,IAAM9I,KAAK,GAAG6E,QAAA,CAAc,EAAE,EAAE,IAAI,CAACjH,IAAI,CAACoC,KAAK,CAAC,CAAA;EAChD;QACAA,KAAK,CAACyM,GAAG,GAAGpR,UAAQ,CAAA;EACpB;QACA2E,KAAK,CAAC4I,SAAS,GAAGE,IAAI,CAAA;EACtB;QACA,IAAI,IAAI,CAACmD,EAAE,EACPjM,KAAK,CAAC0E,GAAG,GAAG,IAAI,CAACuH,EAAE,CAAA;QACvB,IAAMrO,IAAI,GAAGiH,QAAA,CAAc,EAAE,EAAE,IAAI,CAACjH,IAAI,EAAE;EACtCoC,QAAAA,KAAK,EAALA,KAAK;EACLC,QAAAA,MAAM,EAAE,IAAI;UACZ0B,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBG,MAAM,EAAE,IAAI,CAACA,MAAM;UACnBD,IAAI,EAAE,IAAI,CAACA,IAAAA;SACd,EAAE,IAAI,CAACjE,IAAI,CAACiL,gBAAgB,CAACC,IAAI,CAAC,CAAC,CAAA;EACpC,MAAA,OAAO,IAAIiB,UAAU,CAACjB,IAAI,CAAC,CAAClL,IAAI,CAAC,CAAA;EACrC,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAvJ,GAAA,EAAA,MAAA;MAAA6L,KAAA,EAKA,SAAAI,IAAAA,GAAO;EAAA,MAAA,IAAAT,MAAA,GAAA,IAAA,CAAA;EACH,MAAA,IAAI+I,SAAS,CAAA;QACb,IAAI,IAAI,CAAChL,IAAI,CAAC+N,eAAe,IACzBN,MAAM,CAACqB,qBAAqB,IAC5B,IAAI,CAAC3C,UAAU,CAACnI,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;EAC7CgH,QAAAA,SAAS,GAAG,WAAW,CAAA;SAC1B,MACI,IAAI,CAAC,KAAK,IAAI,CAACmB,UAAU,CAACjT,MAAM,EAAE;EACnC;UACA,IAAI,CAACgH,YAAY,CAAC,YAAM;EACpB+B,UAAAA,MAAI,CAACpD,YAAY,CAAC,OAAO,EAAE,yBAAyB,CAAC,CAAA;WACxD,EAAE,CAAC,CAAC,CAAA;EACL,QAAA,OAAA;EACJ,OAAC,MACI;EACDmM,QAAAA,SAAS,GAAG,IAAI,CAACmB,UAAU,CAAC,CAAC,CAAC,CAAA;EAClC,OAAA;QACA,IAAI,CAACxJ,UAAU,GAAG,SAAS,CAAA;EAC3B;QACA,IAAI;EACAqI,QAAAA,SAAS,GAAG,IAAI,CAAC4D,eAAe,CAAC5D,SAAS,CAAC,CAAA;SAC9C,CACD,OAAO7F,CAAC,EAAE;EACN,QAAA,IAAI,CAACgH,UAAU,CAACzP,KAAK,EAAE,CAAA;UACvB,IAAI,CAACgG,IAAI,EAAE,CAAA;EACX,QAAA,OAAA;EACJ,OAAA;QACAsI,SAAS,CAACtI,IAAI,EAAE,CAAA;EAChB,MAAA,IAAI,CAACqM,YAAY,CAAC/D,SAAS,CAAC,CAAA;EAChC,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAvU,GAAA,EAAA,cAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAyM,YAAa/D,CAAAA,SAAS,EAAE;EAAA,MAAA,IAAAzE,MAAA,GAAA,IAAA,CAAA;QACpB,IAAI,IAAI,CAACyE,SAAS,EAAE;EAChB,QAAA,IAAI,CAACA,SAAS,CAAC1M,kBAAkB,EAAE,CAAA;EACvC,OAAA;EACA;QACA,IAAI,CAAC0M,SAAS,GAAGA,SAAS,CAAA;EAC1B;QACAA,SAAS,CACJpN,EAAE,CAAC,OAAO,EAAE,IAAI,CAACoR,OAAO,CAAC7O,IAAI,CAAC,IAAI,CAAC,CAAC,CACpCvC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAACwF,QAAQ,CAACjD,IAAI,CAAC,IAAI,CAAC,CAAC,CACtCvC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC2E,OAAO,CAACpC,IAAI,CAAC,IAAI,CAAC,CAAC,CACpCvC,EAAE,CAAC,OAAO,EAAE,UAAC0D,MAAM,EAAA;EAAA,QAAA,OAAKiF,MAAI,CAACxD,OAAO,CAAC,iBAAiB,EAAEzB,MAAM,CAAC,CAAA;SAAC,CAAA,CAAA;EACzE,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAA7K,GAAA,EAAA,OAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAA2M,KAAM/D,CAAAA,IAAI,EAAE;EAAA,MAAA,IAAA1E,MAAA,GAAA,IAAA,CAAA;EACR,MAAA,IAAIwE,SAAS,GAAG,IAAI,CAAC4D,eAAe,CAAC1D,IAAI,CAAC,CAAA;QAC1C,IAAIgE,MAAM,GAAG,KAAK,CAAA;QAClBzB,MAAM,CAACqB,qBAAqB,GAAG,KAAK,CAAA;EACpC,MAAA,IAAMK,eAAe,GAAG,SAAlBA,eAAeA,GAAS;EAC1B,QAAA,IAAID,MAAM,EACN,OAAA;UACJlE,SAAS,CAAChI,IAAI,CAAC,CAAC;EAAErM,UAAAA,IAAI,EAAE,MAAM;EAAEC,UAAAA,IAAI,EAAE,OAAA;EAAQ,SAAC,CAAC,CAAC,CAAA;EACjDoU,QAAAA,SAAS,CAAC/M,IAAI,CAAC,QAAQ,EAAE,UAACmR,GAAG,EAAK;EAC9B,UAAA,IAAIF,MAAM,EACN,OAAA;YACJ,IAAI,MAAM,KAAKE,GAAG,CAACzY,IAAI,IAAI,OAAO,KAAKyY,GAAG,CAACxY,IAAI,EAAE;cAC7C4P,MAAI,CAAC6I,SAAS,GAAG,IAAI,CAAA;EACrB7I,YAAAA,MAAI,CAAC3H,YAAY,CAAC,WAAW,EAAEmM,SAAS,CAAC,CAAA;cACzC,IAAI,CAACA,SAAS,EACV,OAAA;EACJyC,YAAAA,MAAM,CAACqB,qBAAqB,GAAG,WAAW,KAAK9D,SAAS,CAACE,IAAI,CAAA;EAC7D1E,YAAAA,MAAI,CAACwE,SAAS,CAAC1H,KAAK,CAAC,YAAM;EACvB,cAAA,IAAI4L,MAAM,EACN,OAAA;EACJ,cAAA,IAAI,QAAQ,KAAK1I,MAAI,CAAC7D,UAAU,EAC5B,OAAA;EACJ+F,cAAAA,OAAO,EAAE,CAAA;EACTlC,cAAAA,MAAI,CAACuI,YAAY,CAAC/D,SAAS,CAAC,CAAA;gBAC5BA,SAAS,CAAChI,IAAI,CAAC,CAAC;EAAErM,gBAAAA,IAAI,EAAE,SAAA;EAAU,eAAC,CAAC,CAAC,CAAA;EACrC6P,cAAAA,MAAI,CAAC3H,YAAY,CAAC,SAAS,EAAEmM,SAAS,CAAC,CAAA;EACvCA,cAAAA,SAAS,GAAG,IAAI,CAAA;gBAChBxE,MAAI,CAAC6I,SAAS,GAAG,KAAK,CAAA;gBACtB7I,MAAI,CAAC8I,KAAK,EAAE,CAAA;EAChB,aAAC,CAAC,CAAA;EACN,WAAC,MACI;EACD,YAAA,IAAMvK,GAAG,GAAG,IAAIlD,KAAK,CAAC,aAAa,CAAC,CAAA;EACpC;EACAkD,YAAAA,GAAG,CAACiG,SAAS,GAAGA,SAAS,CAACE,IAAI,CAAA;EAC9B1E,YAAAA,MAAI,CAAC3H,YAAY,CAAC,cAAc,EAAEkG,GAAG,CAAC,CAAA;EAC1C,WAAA;EACJ,SAAC,CAAC,CAAA;SACL,CAAA;QACD,SAASwK,eAAeA,GAAG;EACvB,QAAA,IAAIL,MAAM,EACN,OAAA;EACJ;EACAA,QAAAA,MAAM,GAAG,IAAI,CAAA;EACbxG,QAAAA,OAAO,EAAE,CAAA;UACTsC,SAAS,CAACnI,KAAK,EAAE,CAAA;EACjBmI,QAAAA,SAAS,GAAG,IAAI,CAAA;EACpB,OAAA;EACA;EACA,MAAA,IAAML,OAAO,GAAG,SAAVA,OAAOA,CAAI5F,GAAG,EAAK;UACrB,IAAMyK,KAAK,GAAG,IAAI3N,KAAK,CAAC,eAAe,GAAGkD,GAAG,CAAC,CAAA;EAC9C;EACAyK,QAAAA,KAAK,CAACxE,SAAS,GAAGA,SAAS,CAACE,IAAI,CAAA;EAChCqE,QAAAA,eAAe,EAAE,CAAA;EACjB/I,QAAAA,MAAI,CAAC3H,YAAY,CAAC,cAAc,EAAE2Q,KAAK,CAAC,CAAA;SAC3C,CAAA;QACD,SAASC,gBAAgBA,GAAG;UACxB9E,OAAO,CAAC,kBAAkB,CAAC,CAAA;EAC/B,OAAA;EACA;QACA,SAASJ,OAAOA,GAAG;UACfI,OAAO,CAAC,eAAe,CAAC,CAAA;EAC5B,OAAA;EACA;QACA,SAAS+E,SAASA,CAACC,EAAE,EAAE;UACnB,IAAI3E,SAAS,IAAI2E,EAAE,CAACzE,IAAI,KAAKF,SAAS,CAACE,IAAI,EAAE;EACzCqE,UAAAA,eAAe,EAAE,CAAA;EACrB,SAAA;EACJ,OAAA;EACA;EACA,MAAA,IAAM7G,OAAO,GAAG,SAAVA,OAAOA,GAAS;EAClBsC,QAAAA,SAAS,CAAC3M,cAAc,CAAC,MAAM,EAAE8Q,eAAe,CAAC,CAAA;EACjDnE,QAAAA,SAAS,CAAC3M,cAAc,CAAC,OAAO,EAAEsM,OAAO,CAAC,CAAA;EAC1CK,QAAAA,SAAS,CAAC3M,cAAc,CAAC,OAAO,EAAEoR,gBAAgB,CAAC,CAAA;EACnDjJ,QAAAA,MAAI,CAACtI,GAAG,CAAC,OAAO,EAAEqM,OAAO,CAAC,CAAA;EAC1B/D,QAAAA,MAAI,CAACtI,GAAG,CAAC,WAAW,EAAEwR,SAAS,CAAC,CAAA;SACnC,CAAA;EACD1E,MAAAA,SAAS,CAAC/M,IAAI,CAAC,MAAM,EAAEkR,eAAe,CAAC,CAAA;EACvCnE,MAAAA,SAAS,CAAC/M,IAAI,CAAC,OAAO,EAAE0M,OAAO,CAAC,CAAA;EAChCK,MAAAA,SAAS,CAAC/M,IAAI,CAAC,OAAO,EAAEwR,gBAAgB,CAAC,CAAA;EACzC,MAAA,IAAI,CAACxR,IAAI,CAAC,OAAO,EAAEsM,OAAO,CAAC,CAAA;EAC3B,MAAA,IAAI,CAACtM,IAAI,CAAC,WAAW,EAAEyR,SAAS,CAAC,CAAA;EACjC,MAAA,IAAI,IAAI,CAACpB,QAAQ,CAACtK,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAC5CkH,IAAI,KAAK,cAAc,EAAE;EACzB;UACA,IAAI,CAAChL,YAAY,CAAC,YAAM;YACpB,IAAI,CAACgP,MAAM,EAAE;cACTlE,SAAS,CAACtI,IAAI,EAAE,CAAA;EACpB,WAAA;WACH,EAAE,GAAG,CAAC,CAAA;EACX,OAAC,MACI;UACDsI,SAAS,CAACtI,IAAI,EAAE,CAAA;EACpB,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAjM,GAAA,EAAA,QAAA;MAAA6L,KAAA,EAKA,SAAAY,MAAAA,GAAS;QACL,IAAI,CAACP,UAAU,GAAG,MAAM,CAAA;QACxB8K,MAAM,CAACqB,qBAAqB,GAAG,WAAW,KAAK,IAAI,CAAC9D,SAAS,CAACE,IAAI,CAAA;EAClE,MAAA,IAAI,CAACrM,YAAY,CAAC,MAAM,CAAC,CAAA;QACzB,IAAI,CAACyQ,KAAK,EAAE,CAAA;EACZ;EACA;QACA,IAAI,MAAM,KAAK,IAAI,CAAC3M,UAAU,IAAI,IAAI,CAAC3C,IAAI,CAAC8N,OAAO,EAAE;UACjD,IAAI7U,CAAC,GAAG,CAAC,CAAA;EACT,QAAA,IAAMyH,CAAC,GAAG,IAAI,CAAC4N,QAAQ,CAACpV,MAAM,CAAA;EAC9B,QAAA,OAAOD,CAAC,GAAGyH,CAAC,EAAEzH,CAAC,EAAE,EAAE;YACf,IAAI,CAACgW,KAAK,CAAC,IAAI,CAACX,QAAQ,CAACrV,CAAC,CAAC,CAAC,CAAA;EAChC,SAAA;EACJ,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAxC,GAAA,EAAA,UAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAc,QAAS3K,CAAAA,MAAM,EAAE;EACb,MAAA,IAAI,SAAS,KAAK,IAAI,CAACkK,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;EAC/B,QAAA,IAAI,CAAC9D,YAAY,CAAC,QAAQ,EAAEpG,MAAM,CAAC,CAAA;EACnC;EACA,QAAA,IAAI,CAACoG,YAAY,CAAC,WAAW,CAAC,CAAA;UAC9B,IAAI,CAAC+Q,gBAAgB,EAAE,CAAA;UACvB,QAAQnX,MAAM,CAAC9B,IAAI;EACf,UAAA,KAAK,MAAM;cACP,IAAI,CAACkZ,WAAW,CAACC,IAAI,CAACtD,KAAK,CAAC/T,MAAM,CAAC7B,IAAI,CAAC,CAAC,CAAA;EACzC,YAAA,MAAA;EACJ,UAAA,KAAK,MAAM;EACP,YAAA,IAAI,CAACmZ,UAAU,CAAC,MAAM,CAAC,CAAA;EACvB,YAAA,IAAI,CAAClR,YAAY,CAAC,MAAM,CAAC,CAAA;EACzB,YAAA,IAAI,CAACA,YAAY,CAAC,MAAM,CAAC,CAAA;EACzB,YAAA,MAAA;EACJ,UAAA,KAAK,OAAO;EACR,YAAA,IAAMkG,GAAG,GAAG,IAAIlD,KAAK,CAAC,cAAc,CAAC,CAAA;EACrC;EACAkD,YAAAA,GAAG,CAACiL,IAAI,GAAGvX,MAAM,CAAC7B,IAAI,CAAA;EACtB,YAAA,IAAI,CAAC2L,OAAO,CAACwC,GAAG,CAAC,CAAA;EACjB,YAAA,MAAA;EACJ,UAAA,KAAK,SAAS;cACV,IAAI,CAAClG,YAAY,CAAC,MAAM,EAAEpG,MAAM,CAAC7B,IAAI,CAAC,CAAA;cACtC,IAAI,CAACiI,YAAY,CAAC,SAAS,EAAEpG,MAAM,CAAC7B,IAAI,CAAC,CAAA;EACzC,YAAA,MAAA;EACR,SAAA;EACJ,OAEA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAH,GAAA,EAAA,aAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAuN,WAAYjZ,CAAAA,IAAI,EAAE;EACd,MAAA,IAAI,CAACiI,YAAY,CAAC,WAAW,EAAEjI,IAAI,CAAC,CAAA;EACpC,MAAA,IAAI,CAACyX,EAAE,GAAGzX,IAAI,CAACkQ,GAAG,CAAA;QAClB,IAAI,CAACkE,SAAS,CAAC5I,KAAK,CAAC0E,GAAG,GAAGlQ,IAAI,CAACkQ,GAAG,CAAA;QACnC,IAAI,CAACwH,QAAQ,GAAG,IAAI,CAAC2B,cAAc,CAACrZ,IAAI,CAAC0X,QAAQ,CAAC,CAAA;EAClD,MAAA,IAAI,CAACC,YAAY,GAAG3X,IAAI,CAAC2X,YAAY,CAAA;EACrC,MAAA,IAAI,CAACC,WAAW,GAAG5X,IAAI,CAAC4X,WAAW,CAAA;EACnC,MAAA,IAAI,CAAC1R,UAAU,GAAGlG,IAAI,CAACkG,UAAU,CAAA;QACjC,IAAI,CAACoG,MAAM,EAAE,CAAA;EACb;EACA,MAAA,IAAI,QAAQ,KAAK,IAAI,CAACP,UAAU,EAC5B,OAAA;QACJ,IAAI,CAACiN,gBAAgB,EAAE,CAAA;EAC3B,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAnZ,GAAA,EAAA,kBAAA;MAAA6L,KAAA,EAKA,SAAAsN,gBAAAA,GAAmB;EAAA,MAAA,IAAAnJ,MAAA,GAAA,IAAA,CAAA;EACf,MAAA,IAAI,CAACrG,cAAc,CAAC,IAAI,CAACqO,gBAAgB,CAAC,CAAA;EAC1C,MAAA,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACvO,YAAY,CAAC,YAAM;EAC5CuG,QAAAA,MAAI,CAAC1D,OAAO,CAAC,cAAc,CAAC,CAAA;SAC/B,EAAE,IAAI,CAACwL,YAAY,GAAG,IAAI,CAACC,WAAW,CAAC,CAAA;EACxC,MAAA,IAAI,IAAI,CAACxO,IAAI,CAACoK,SAAS,EAAE;EACrB,QAAA,IAAI,CAACqE,gBAAgB,CAACnE,KAAK,EAAE,CAAA;EACjC,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA7T,GAAA,EAAA,SAAA;MAAA6L,KAAA,EAKA,SAAA0M,OAAAA,GAAU;QACN,IAAI,CAACtB,WAAW,CAAChP,MAAM,CAAC,CAAC,EAAE,IAAI,CAACkP,aAAa,CAAC,CAAA;EAC9C;EACA;EACA;QACA,IAAI,CAACA,aAAa,GAAG,CAAC,CAAA;EACtB,MAAA,IAAI,CAAC,KAAK,IAAI,CAACF,WAAW,CAACxU,MAAM,EAAE;EAC/B,QAAA,IAAI,CAAC2F,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,OAAC,MACI;UACD,IAAI,CAACyQ,KAAK,EAAE,CAAA;EAChB,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA7Y,GAAA,EAAA,OAAA;MAAA6L,KAAA,EAKA,SAAAgN,KAAAA,GAAQ;QACJ,IAAI,QAAQ,KAAK,IAAI,CAAC3M,UAAU,IAC5B,IAAI,CAACqI,SAAS,CAAC9I,QAAQ,IACvB,CAAC,IAAI,CAACmN,SAAS,IACf,IAAI,CAAC3B,WAAW,CAACxU,MAAM,EAAE;EACzB,QAAA,IAAM0B,OAAO,GAAG,IAAI,CAACsV,kBAAkB,EAAE,CAAA;EACzC,QAAA,IAAI,CAAClF,SAAS,CAAChI,IAAI,CAACpI,OAAO,CAAC,CAAA;EAC5B;EACA;EACA,QAAA,IAAI,CAACgT,aAAa,GAAGhT,OAAO,CAAC1B,MAAM,CAAA;EACnC,QAAA,IAAI,CAAC2F,YAAY,CAAC,OAAO,CAAC,CAAA;EAC9B,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAApI,GAAA,EAAA,oBAAA;MAAA6L,KAAA,EAMA,SAAA4N,kBAAAA,GAAqB;QACjB,IAAMC,sBAAsB,GAAG,IAAI,CAACrT,UAAU,IAC1C,IAAI,CAACkO,SAAS,CAACE,IAAI,KAAK,SAAS,IACjC,IAAI,CAACwC,WAAW,CAACxU,MAAM,GAAG,CAAC,CAAA;QAC/B,IAAI,CAACiX,sBAAsB,EAAE;UACzB,OAAO,IAAI,CAACzC,WAAW,CAAA;EAC3B,OAAA;EACA,MAAA,IAAI0C,WAAW,GAAG,CAAC,CAAC;EACpB,MAAA,KAAK,IAAInX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACyU,WAAW,CAACxU,MAAM,EAAED,CAAC,EAAE,EAAE;UAC9C,IAAMrC,IAAI,GAAG,IAAI,CAAC8W,WAAW,CAACzU,CAAC,CAAC,CAACrC,IAAI,CAAA;EACrC,QAAA,IAAIA,IAAI,EAAE;EACNwZ,UAAAA,WAAW,IAAI9X,UAAU,CAAC1B,IAAI,CAAC,CAAA;EACnC,SAAA;UACA,IAAIqC,CAAC,GAAG,CAAC,IAAImX,WAAW,GAAG,IAAI,CAACtT,UAAU,EAAE;YACxC,OAAO,IAAI,CAAC4Q,WAAW,CAAC9Q,KAAK,CAAC,CAAC,EAAE3D,CAAC,CAAC,CAAA;EACvC,SAAA;UACAmX,WAAW,IAAI,CAAC,CAAC;EACrB,OAAA;;QACA,OAAO,IAAI,CAAC1C,WAAW,CAAA;EAC3B,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EAPI,GAAA,EAAA;MAAAjX,GAAA,EAAA,OAAA;MAAA6L,KAAA,EAQA,SAAAW,KAAMmM,CAAAA,GAAG,EAAEiB,OAAO,EAAEtS,EAAE,EAAE;QACpB,IAAI,CAACgS,UAAU,CAAC,SAAS,EAAEX,GAAG,EAAEiB,OAAO,EAAEtS,EAAE,CAAC,CAAA;EAC5C,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EAAC,GAAA,EAAA;MAAAtH,GAAA,EAAA,MAAA;MAAA6L,KAAA,EACD,SAAAU,IAAKoM,CAAAA,GAAG,EAAEiB,OAAO,EAAEtS,EAAE,EAAE;QACnB,IAAI,CAACgS,UAAU,CAAC,SAAS,EAAEX,GAAG,EAAEiB,OAAO,EAAEtS,EAAE,CAAC,CAAA;EAC5C,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EARI,GAAA,EAAA;MAAAtH,GAAA,EAAA,YAAA;MAAA6L,KAAA,EASA,SAAAyN,UAAAA,CAAWpZ,IAAI,EAAEC,IAAI,EAAEyZ,OAAO,EAAEtS,EAAE,EAAE;EAChC,MAAA,IAAI,UAAU,KAAK,OAAOnH,IAAI,EAAE;EAC5BmH,QAAAA,EAAE,GAAGnH,IAAI,CAAA;EACTA,QAAAA,IAAI,GAAG8M,SAAS,CAAA;EACpB,OAAA;EACA,MAAA,IAAI,UAAU,KAAK,OAAO2M,OAAO,EAAE;EAC/BtS,QAAAA,EAAE,GAAGsS,OAAO,CAAA;EACZA,QAAAA,OAAO,GAAG,IAAI,CAAA;EAClB,OAAA;QACA,IAAI,SAAS,KAAK,IAAI,CAAC1N,UAAU,IAAI,QAAQ,KAAK,IAAI,CAACA,UAAU,EAAE;EAC/D,QAAA,OAAA;EACJ,OAAA;EACA0N,MAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;EACvBA,MAAAA,OAAO,CAACC,QAAQ,GAAG,KAAK,KAAKD,OAAO,CAACC,QAAQ,CAAA;EAC7C,MAAA,IAAM7X,MAAM,GAAG;EACX9B,QAAAA,IAAI,EAAEA,IAAI;EACVC,QAAAA,IAAI,EAAEA,IAAI;EACVyZ,QAAAA,OAAO,EAAEA,OAAAA;SACZ,CAAA;EACD,MAAA,IAAI,CAACxR,YAAY,CAAC,cAAc,EAAEpG,MAAM,CAAC,CAAA;EACzC,MAAA,IAAI,CAACiV,WAAW,CAACtS,IAAI,CAAC3C,MAAM,CAAC,CAAA;QAC7B,IAAIsF,EAAE,EACF,IAAI,CAACE,IAAI,CAAC,OAAO,EAAEF,EAAE,CAAC,CAAA;QAC1B,IAAI,CAACuR,KAAK,EAAE,CAAA;EAChB,KAAA;EACA;EACJ;EACA;EAFI,GAAA,EAAA;MAAA7Y,GAAA,EAAA,OAAA;MAAA6L,KAAA,EAGA,SAAAO,KAAAA,GAAQ;EAAA,MAAA,IAAAsE,MAAA,GAAA,IAAA,CAAA;EACJ,MAAA,IAAMtE,KAAK,GAAG,SAARA,KAAKA,GAAS;EAChBsE,QAAAA,MAAI,CAACpE,OAAO,CAAC,cAAc,CAAC,CAAA;EAC5BoE,QAAAA,MAAI,CAAC6D,SAAS,CAACnI,KAAK,EAAE,CAAA;SACzB,CAAA;EACD,MAAA,IAAM0N,eAAe,GAAG,SAAlBA,eAAeA,GAAS;EAC1BpJ,QAAAA,MAAI,CAACjJ,GAAG,CAAC,SAAS,EAAEqS,eAAe,CAAC,CAAA;EACpCpJ,QAAAA,MAAI,CAACjJ,GAAG,CAAC,cAAc,EAAEqS,eAAe,CAAC,CAAA;EACzC1N,QAAAA,KAAK,EAAE,CAAA;SACV,CAAA;EACD,MAAA,IAAM2N,cAAc,GAAG,SAAjBA,cAAcA,GAAS;EACzB;EACArJ,QAAAA,MAAI,CAAClJ,IAAI,CAAC,SAAS,EAAEsS,eAAe,CAAC,CAAA;EACrCpJ,QAAAA,MAAI,CAAClJ,IAAI,CAAC,cAAc,EAAEsS,eAAe,CAAC,CAAA;SAC7C,CAAA;QACD,IAAI,SAAS,KAAK,IAAI,CAAC5N,UAAU,IAAI,MAAM,KAAK,IAAI,CAACA,UAAU,EAAE;UAC7D,IAAI,CAACA,UAAU,GAAG,SAAS,CAAA;EAC3B,QAAA,IAAI,IAAI,CAAC+K,WAAW,CAACxU,MAAM,EAAE;EACzB,UAAA,IAAI,CAAC+E,IAAI,CAAC,OAAO,EAAE,YAAM;cACrB,IAAIkJ,MAAI,CAACkI,SAAS,EAAE;EAChBmB,cAAAA,cAAc,EAAE,CAAA;EACpB,aAAC,MACI;EACD3N,cAAAA,KAAK,EAAE,CAAA;EACX,aAAA;EACJ,WAAC,CAAC,CAAA;EACN,SAAC,MACI,IAAI,IAAI,CAACwM,SAAS,EAAE;EACrBmB,UAAAA,cAAc,EAAE,CAAA;EACpB,SAAC,MACI;EACD3N,UAAAA,KAAK,EAAE,CAAA;EACX,SAAA;EACJ,OAAA;EACA,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAApM,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAC,OAAQwC,CAAAA,GAAG,EAAE;QACT0I,MAAM,CAACqB,qBAAqB,GAAG,KAAK,CAAA;EACpC,MAAA,IAAI,CAACjQ,YAAY,CAAC,OAAO,EAAEkG,GAAG,CAAC,CAAA;EAC/B,MAAA,IAAI,CAAChC,OAAO,CAAC,iBAAiB,EAAEgC,GAAG,CAAC,CAAA;EACxC,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAtO,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAS,OAAAA,CAAQzB,MAAM,EAAEC,WAAW,EAAE;EACzB,MAAA,IAAI,SAAS,KAAK,IAAI,CAACoB,UAAU,IAC7B,MAAM,KAAK,IAAI,CAACA,UAAU,IAC1B,SAAS,KAAK,IAAI,CAACA,UAAU,EAAE;EAC/B;EACA,QAAA,IAAI,CAACvC,cAAc,CAAC,IAAI,CAACqO,gBAAgB,CAAC,CAAA;EAC1C;EACA,QAAA,IAAI,CAACzD,SAAS,CAAC1M,kBAAkB,CAAC,OAAO,CAAC,CAAA;EAC1C;EACA,QAAA,IAAI,CAAC0M,SAAS,CAACnI,KAAK,EAAE,CAAA;EACtB;EACA,QAAA,IAAI,CAACmI,SAAS,CAAC1M,kBAAkB,EAAE,CAAA;EACnC,QAAA,IAAI,OAAOC,mBAAmB,KAAK,UAAU,EAAE;YAC3CA,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACmQ,yBAAyB,EAAE,KAAK,CAAC,CAAA;YAC1EnQ,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACoQ,oBAAoB,EAAE,KAAK,CAAC,CAAA;EACpE,SAAA;EACA;UACA,IAAI,CAAChM,UAAU,GAAG,QAAQ,CAAA;EAC1B;UACA,IAAI,CAAC0L,EAAE,GAAG,IAAI,CAAA;EACd;UACA,IAAI,CAACxP,YAAY,CAAC,OAAO,EAAEyC,MAAM,EAAEC,WAAW,CAAC,CAAA;EAC/C;EACA;UACA,IAAI,CAACmM,WAAW,GAAG,EAAE,CAAA;UACrB,IAAI,CAACE,aAAa,GAAG,CAAC,CAAA;EAC1B,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAnX,GAAA,EAAA,gBAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAA2N,cAAe3B,CAAAA,QAAQ,EAAE;QACrB,IAAMmC,gBAAgB,GAAG,EAAE,CAAA;QAC3B,IAAIxX,CAAC,GAAG,CAAC,CAAA;EACT,MAAA,IAAM0D,CAAC,GAAG2R,QAAQ,CAACpV,MAAM,CAAA;EACzB,MAAA,OAAOD,CAAC,GAAG0D,CAAC,EAAE1D,CAAC,EAAE,EAAE;UACf,IAAI,CAAC,IAAI,CAACkT,UAAU,CAACnI,OAAO,CAACsK,QAAQ,CAACrV,CAAC,CAAC,CAAC,EACrCwX,gBAAgB,CAACrV,IAAI,CAACkT,QAAQ,CAACrV,CAAC,CAAC,CAAC,CAAA;EAC1C,OAAA;EACA,MAAA,OAAOwX,gBAAgB,CAAA;EAC3B,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,EAAA,OAAAhD,MAAA,CAAA;EAAA,CAAA,CAxkBuB/P,OAAO,CAAA,CAAA;AA0kBnC+P,UAAM,CAAChQ,QAAQ,GAAGA,UAAQ;;AC/kBFgQ,UAAM,CAAChQ;;ECD/B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASiT,GAAGA,CAAC/J,GAAG,EAAkB;EAAA,EAAA,IAAhB9C,IAAI,GAAAzF,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAAsF,SAAA,GAAAtF,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE,CAAA;IAAA,IAAEuS,GAAG,GAAAvS,SAAA,CAAAlF,MAAA,GAAAkF,CAAAA,GAAAA,SAAA,MAAAsF,SAAA,CAAA;IACnC,IAAIrM,GAAG,GAAGsP,GAAG,CAAA;EACb;IACAgK,GAAG,GAAGA,GAAG,IAAK,OAAO9K,QAAQ,KAAK,WAAW,IAAIA,QAAS,CAAA;EAC1D,EAAA,IAAI,IAAI,IAAIc,GAAG,EACXA,GAAG,GAAGgK,GAAG,CAAClT,QAAQ,GAAG,IAAI,GAAGkT,GAAG,CAAC5D,IAAI,CAAA;EACxC;EACA,EAAA,IAAI,OAAOpG,GAAG,KAAK,QAAQ,EAAE;MACzB,IAAI,GAAG,KAAKA,GAAG,CAACxM,MAAM,CAAC,CAAC,CAAC,EAAE;QACvB,IAAI,GAAG,KAAKwM,GAAG,CAACxM,MAAM,CAAC,CAAC,CAAC,EAAE;EACvBwM,QAAAA,GAAG,GAAGgK,GAAG,CAAClT,QAAQ,GAAGkJ,GAAG,CAAA;EAC5B,OAAC,MACI;EACDA,QAAAA,GAAG,GAAGgK,GAAG,CAAC5D,IAAI,GAAGpG,GAAG,CAAA;EACxB,OAAA;EACJ,KAAA;EACA,IAAA,IAAI,CAAC,qBAAqB,CAACiK,IAAI,CAACjK,GAAG,CAAC,EAAE;EAClC,MAAA,IAAI,WAAW,KAAK,OAAOgK,GAAG,EAAE;EAC5BhK,QAAAA,GAAG,GAAGgK,GAAG,CAAClT,QAAQ,GAAG,IAAI,GAAGkJ,GAAG,CAAA;EACnC,OAAC,MACI;UACDA,GAAG,GAAG,UAAU,GAAGA,GAAG,CAAA;EAC1B,OAAA;EACJ,KAAA;EACA;EACAtP,IAAAA,GAAG,GAAGmV,KAAK,CAAC7F,GAAG,CAAC,CAAA;EACpB,GAAA;EACA;EACA,EAAA,IAAI,CAACtP,GAAG,CAAC4M,IAAI,EAAE;MACX,IAAI,aAAa,CAAC2M,IAAI,CAACvZ,GAAG,CAACoG,QAAQ,CAAC,EAAE;QAClCpG,GAAG,CAAC4M,IAAI,GAAG,IAAI,CAAA;OAClB,MACI,IAAI,cAAc,CAAC2M,IAAI,CAACvZ,GAAG,CAACoG,QAAQ,CAAC,EAAE;QACxCpG,GAAG,CAAC4M,IAAI,GAAG,KAAK,CAAA;EACpB,KAAA;EACJ,GAAA;EACA5M,EAAAA,GAAG,CAACwM,IAAI,GAAGxM,GAAG,CAACwM,IAAI,IAAI,GAAG,CAAA;EAC1B,EAAA,IAAMgN,IAAI,GAAGxZ,GAAG,CAAC0V,IAAI,CAAC/I,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAA;EACzC,EAAA,IAAM+I,IAAI,GAAG8D,IAAI,GAAG,GAAG,GAAGxZ,GAAG,CAAC0V,IAAI,GAAG,GAAG,GAAG1V,GAAG,CAAC0V,IAAI,CAAA;EACnD;EACA1V,EAAAA,GAAG,CAACgX,EAAE,GAAGhX,GAAG,CAACoG,QAAQ,GAAG,KAAK,GAAGsP,IAAI,GAAG,GAAG,GAAG1V,GAAG,CAAC4M,IAAI,GAAGJ,IAAI,CAAA;EAC5D;EACAxM,EAAAA,GAAG,CAACyZ,IAAI,GACJzZ,GAAG,CAACoG,QAAQ,GACR,KAAK,GACLsP,IAAI,IACH4D,GAAG,IAAIA,GAAG,CAAC1M,IAAI,KAAK5M,GAAG,CAAC4M,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG5M,GAAG,CAAC4M,IAAI,CAAC,CAAA;EAC5D,EAAA,OAAO5M,GAAG,CAAA;EACd;;EC1DA,IAAMH,qBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU,CAAA;EAC/D,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAIC,GAAG,EAAK;EACpB,EAAA,OAAO,OAAOF,WAAW,CAACC,MAAM,KAAK,UAAU,GACzCD,WAAW,CAACC,MAAM,CAACC,GAAG,CAAC,GACvBA,GAAG,CAACC,MAAM,YAAYH,WAAW,CAAA;EAC3C,CAAC,CAAA;EACD,IAAMH,QAAQ,GAAGZ,MAAM,CAACW,SAAS,CAACC,QAAQ,CAAA;EAC1C,IAAMH,cAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBE,QAAQ,CAACC,IAAI,CAACH,IAAI,CAAC,KAAK,0BAA2B,CAAA;EAC3D,IAAMia,cAAc,GAAG,OAAOC,IAAI,KAAK,UAAU,IAC5C,OAAOA,IAAI,KAAK,WAAW,IACxBha,QAAQ,CAACC,IAAI,CAAC+Z,IAAI,CAAC,KAAK,0BAA2B,CAAA;EAC3D;EACA;EACA;EACA;EACA;EACO,SAAS9T,QAAQA,CAAC7F,GAAG,EAAE;IAC1B,OAASH,qBAAqB,KAAKG,GAAG,YAAYF,WAAW,IAAIC,MAAM,CAACC,GAAG,CAAC,CAAC,IACxER,cAAc,IAAIQ,GAAG,YAAYP,IAAK,IACtCia,cAAc,IAAI1Z,GAAG,YAAY2Z,IAAK,CAAA;EAC/C,CAAA;EACO,SAASC,SAASA,CAAC5Z,GAAG,EAAE6Z,MAAM,EAAE;IACnC,IAAI,CAAC7Z,GAAG,IAAIsW,OAAA,CAAOtW,GAAG,CAAA,KAAK,QAAQ,EAAE;EACjC,IAAA,OAAO,KAAK,CAAA;EAChB,GAAA;EACA,EAAA,IAAIyD,KAAK,CAACqW,OAAO,CAAC9Z,GAAG,CAAC,EAAE;EACpB,IAAA,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEyH,CAAC,GAAGrJ,GAAG,CAAC6B,MAAM,EAAED,CAAC,GAAGyH,CAAC,EAAEzH,CAAC,EAAE,EAAE;EACxC,MAAA,IAAIgY,SAAS,CAAC5Z,GAAG,CAAC4B,CAAC,CAAC,CAAC,EAAE;EACnB,QAAA,OAAO,IAAI,CAAA;EACf,OAAA;EACJ,KAAA;EACA,IAAA,OAAO,KAAK,CAAA;EAChB,GAAA;EACA,EAAA,IAAIiE,QAAQ,CAAC7F,GAAG,CAAC,EAAE;EACf,IAAA,OAAO,IAAI,CAAA;EACf,GAAA;EACA,EAAA,IAAIA,GAAG,CAAC6Z,MAAM,IACV,OAAO7Z,GAAG,CAAC6Z,MAAM,KAAK,UAAU,IAChC9S,SAAS,CAAClF,MAAM,KAAK,CAAC,EAAE;MACxB,OAAO+X,SAAS,CAAC5Z,GAAG,CAAC6Z,MAAM,EAAE,EAAE,IAAI,CAAC,CAAA;EACxC,GAAA;EACA,EAAA,KAAK,IAAMza,GAAG,IAAIY,GAAG,EAAE;MACnB,IAAIjB,MAAM,CAACW,SAAS,CAAC0I,cAAc,CAACxI,IAAI,CAACI,GAAG,EAAEZ,GAAG,CAAC,IAAIwa,SAAS,CAAC5Z,GAAG,CAACZ,GAAG,CAAC,CAAC,EAAE;EACvE,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACJ,GAAA;EACA,EAAA,OAAO,KAAK,CAAA;EAChB;;EChDA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS2a,iBAAiBA,CAAC3Y,MAAM,EAAE;IACtC,IAAM4Y,OAAO,GAAG,EAAE,CAAA;EAClB,EAAA,IAAMC,UAAU,GAAG7Y,MAAM,CAAC7B,IAAI,CAAA;IAC9B,IAAM2a,IAAI,GAAG9Y,MAAM,CAAA;IACnB8Y,IAAI,CAAC3a,IAAI,GAAG4a,kBAAkB,CAACF,UAAU,EAAED,OAAO,CAAC,CAAA;EACnDE,EAAAA,IAAI,CAACE,WAAW,GAAGJ,OAAO,CAACnY,MAAM,CAAC;IAClC,OAAO;EAAET,IAAAA,MAAM,EAAE8Y,IAAI;EAAEF,IAAAA,OAAO,EAAEA,OAAAA;KAAS,CAAA;EAC7C,CAAA;EACA,SAASG,kBAAkBA,CAAC5a,IAAI,EAAEya,OAAO,EAAE;EACvC,EAAA,IAAI,CAACza,IAAI,EACL,OAAOA,IAAI,CAAA;EACf,EAAA,IAAIsG,QAAQ,CAACtG,IAAI,CAAC,EAAE;EAChB,IAAA,IAAM8a,WAAW,GAAG;EAAEC,MAAAA,YAAY,EAAE,IAAI;QAAElN,GAAG,EAAE4M,OAAO,CAACnY,MAAAA;OAAQ,CAAA;EAC/DmY,IAAAA,OAAO,CAACjW,IAAI,CAACxE,IAAI,CAAC,CAAA;EAClB,IAAA,OAAO8a,WAAW,CAAA;KACrB,MACI,IAAI5W,KAAK,CAACqW,OAAO,CAACva,IAAI,CAAC,EAAE;MAC1B,IAAMgb,OAAO,GAAG,IAAI9W,KAAK,CAAClE,IAAI,CAACsC,MAAM,CAAC,CAAA;EACtC,IAAA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,IAAI,CAACsC,MAAM,EAAED,CAAC,EAAE,EAAE;EAClC2Y,MAAAA,OAAO,CAAC3Y,CAAC,CAAC,GAAGuY,kBAAkB,CAAC5a,IAAI,CAACqC,CAAC,CAAC,EAAEoY,OAAO,CAAC,CAAA;EACrD,KAAA;EACA,IAAA,OAAOO,OAAO,CAAA;EAClB,GAAC,MACI,IAAIjE,OAAA,CAAO/W,IAAI,CAAA,KAAK,QAAQ,IAAI,EAAEA,IAAI,YAAYiO,IAAI,CAAC,EAAE;MAC1D,IAAM+M,QAAO,GAAG,EAAE,CAAA;EAClB,IAAA,KAAK,IAAMnb,GAAG,IAAIG,IAAI,EAAE;EACpB,MAAA,IAAIR,MAAM,CAACW,SAAS,CAAC0I,cAAc,CAACxI,IAAI,CAACL,IAAI,EAAEH,GAAG,CAAC,EAAE;EACjDmb,QAAAA,QAAO,CAACnb,GAAG,CAAC,GAAG+a,kBAAkB,CAAC5a,IAAI,CAACH,GAAG,CAAC,EAAE4a,OAAO,CAAC,CAAA;EACzD,OAAA;EACJ,KAAA;EACA,IAAA,OAAOO,QAAO,CAAA;EAClB,GAAA;EACA,EAAA,OAAOhb,IAAI,CAAA;EACf,CAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASib,iBAAiBA,CAACpZ,MAAM,EAAE4Y,OAAO,EAAE;IAC/C5Y,MAAM,CAAC7B,IAAI,GAAGkb,kBAAkB,CAACrZ,MAAM,CAAC7B,IAAI,EAAEya,OAAO,CAAC,CAAA;EACtD,EAAA,OAAO5Y,MAAM,CAACgZ,WAAW,CAAC;EAC1B,EAAA,OAAOhZ,MAAM,CAAA;EACjB,CAAA;EACA,SAASqZ,kBAAkBA,CAAClb,IAAI,EAAEya,OAAO,EAAE;EACvC,EAAA,IAAI,CAACza,IAAI,EACL,OAAOA,IAAI,CAAA;EACf,EAAA,IAAIA,IAAI,IAAIA,IAAI,CAAC+a,YAAY,KAAK,IAAI,EAAE;MACpC,IAAMI,YAAY,GAAG,OAAOnb,IAAI,CAAC6N,GAAG,KAAK,QAAQ,IAC7C7N,IAAI,CAAC6N,GAAG,IAAI,CAAC,IACb7N,IAAI,CAAC6N,GAAG,GAAG4M,OAAO,CAACnY,MAAM,CAAA;EAC7B,IAAA,IAAI6Y,YAAY,EAAE;EACd,MAAA,OAAOV,OAAO,CAACza,IAAI,CAAC6N,GAAG,CAAC,CAAC;EAC7B,KAAC,MACI;EACD,MAAA,MAAM,IAAI5C,KAAK,CAAC,qBAAqB,CAAC,CAAA;EAC1C,KAAA;KACH,MACI,IAAI/G,KAAK,CAACqW,OAAO,CAACva,IAAI,CAAC,EAAE;EAC1B,IAAA,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,IAAI,CAACsC,MAAM,EAAED,CAAC,EAAE,EAAE;EAClCrC,MAAAA,IAAI,CAACqC,CAAC,CAAC,GAAG6Y,kBAAkB,CAAClb,IAAI,CAACqC,CAAC,CAAC,EAAEoY,OAAO,CAAC,CAAA;EAClD,KAAA;EACJ,GAAC,MACI,IAAI1D,OAAA,CAAO/W,IAAI,CAAA,KAAK,QAAQ,EAAE;EAC/B,IAAA,KAAK,IAAMH,GAAG,IAAIG,IAAI,EAAE;EACpB,MAAA,IAAIR,MAAM,CAACW,SAAS,CAAC0I,cAAc,CAACxI,IAAI,CAACL,IAAI,EAAEH,GAAG,CAAC,EAAE;EACjDG,QAAAA,IAAI,CAACH,GAAG,CAAC,GAAGqb,kBAAkB,CAAClb,IAAI,CAACH,GAAG,CAAC,EAAE4a,OAAO,CAAC,CAAA;EACtD,OAAA;EACJ,KAAA;EACJ,GAAA;EACA,EAAA,OAAOza,IAAI,CAAA;EACf;;EC/EA;EACA;EACA;EACA,IAAMob,iBAAe,GAAG,CACpB,SAAS,EACT,eAAe,EACf,YAAY,EACZ,eAAe,EACf,aAAa,EACb,gBAAgB;EAAE,CACrB,CAAA;EACD;EACA;EACA;EACA;EACA;EACO,IAAMvU,QAAQ,GAAG,CAAC,CAAA;EAClB,IAAIwU,UAAU,CAAA;EACrB,CAAC,UAAUA,UAAU,EAAE;IACnBA,UAAU,CAACA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAA;IACjDA,UAAU,CAACA,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAA;IACvDA,UAAU,CAACA,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAA;IAC7CA,UAAU,CAACA,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAA;IACzCA,UAAU,CAACA,UAAU,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe,CAAA;IAC7DA,UAAU,CAACA,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAA;IAC3DA,UAAU,CAACA,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAA;EAC3D,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,EAAE,CAAC,CAAC,CAAA;EACnC;EACA;EACA;EACA,IAAaC,OAAO,gBAAA,YAAA;EAChB;EACJ;EACA;EACA;EACA;IACI,SAAAA,OAAAA,CAAYC,QAAQ,EAAE;EAAAzQ,IAAAA,eAAA,OAAAwQ,OAAA,CAAA,CAAA;MAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ,CAAA;EAC5B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALIxQ,EAAAA,YAAA,CAAAuQ,OAAA,EAAA,CAAA;MAAAzb,GAAA,EAAA,QAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAxJ,MAAOzB,CAAAA,GAAG,EAAE;EACR,MAAA,IAAIA,GAAG,CAACV,IAAI,KAAKsb,UAAU,CAACG,KAAK,IAAI/a,GAAG,CAACV,IAAI,KAAKsb,UAAU,CAACI,GAAG,EAAE;EAC9D,QAAA,IAAIpB,SAAS,CAAC5Z,GAAG,CAAC,EAAE;YAChB,OAAO,IAAI,CAACib,cAAc,CAAC;EACvB3b,YAAAA,IAAI,EAAEU,GAAG,CAACV,IAAI,KAAKsb,UAAU,CAACG,KAAK,GAC7BH,UAAU,CAACM,YAAY,GACvBN,UAAU,CAACO,UAAU;cAC3BC,GAAG,EAAEpb,GAAG,CAACob,GAAG;cACZ7b,IAAI,EAAES,GAAG,CAACT,IAAI;cACdyX,EAAE,EAAEhX,GAAG,CAACgX,EAAAA;EACZ,WAAC,CAAC,CAAA;EACN,SAAA;EACJ,OAAA;EACA,MAAA,OAAO,CAAC,IAAI,CAACqE,cAAc,CAACrb,GAAG,CAAC,CAAC,CAAA;EACrC,KAAA;EACA;EACJ;EACA;EAFI,GAAA,EAAA;MAAAZ,GAAA,EAAA,gBAAA;EAAA6L,IAAAA,KAAA,EAGA,SAAAoQ,cAAerb,CAAAA,GAAG,EAAE;EAChB;EACA,MAAA,IAAImJ,GAAG,GAAG,EAAE,GAAGnJ,GAAG,CAACV,IAAI,CAAA;EACvB;EACA,MAAA,IAAIU,GAAG,CAACV,IAAI,KAAKsb,UAAU,CAACM,YAAY,IACpClb,GAAG,CAACV,IAAI,KAAKsb,UAAU,CAACO,UAAU,EAAE;EACpChS,QAAAA,GAAG,IAAInJ,GAAG,CAACoa,WAAW,GAAG,GAAG,CAAA;EAChC,OAAA;EACA;EACA;QACA,IAAIpa,GAAG,CAACob,GAAG,IAAI,GAAG,KAAKpb,GAAG,CAACob,GAAG,EAAE;EAC5BjS,QAAAA,GAAG,IAAInJ,GAAG,CAACob,GAAG,GAAG,GAAG,CAAA;EACxB,OAAA;EACA;EACA,MAAA,IAAI,IAAI,IAAIpb,GAAG,CAACgX,EAAE,EAAE;UAChB7N,GAAG,IAAInJ,GAAG,CAACgX,EAAE,CAAA;EACjB,OAAA;EACA;EACA,MAAA,IAAI,IAAI,IAAIhX,GAAG,CAACT,IAAI,EAAE;EAClB4J,QAAAA,GAAG,IAAIsP,IAAI,CAAC6C,SAAS,CAACtb,GAAG,CAACT,IAAI,EAAE,IAAI,CAACub,QAAQ,CAAC,CAAA;EAClD,OAAA;EACA,MAAA,OAAO3R,GAAG,CAAA;EACd,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA/J,GAAA,EAAA,gBAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAgQ,cAAejb,CAAAA,GAAG,EAAE;EAChB,MAAA,IAAMub,cAAc,GAAGxB,iBAAiB,CAAC/Z,GAAG,CAAC,CAAA;QAC7C,IAAMka,IAAI,GAAG,IAAI,CAACmB,cAAc,CAACE,cAAc,CAACna,MAAM,CAAC,CAAA;EACvD,MAAA,IAAM4Y,OAAO,GAAGuB,cAAc,CAACvB,OAAO,CAAA;EACtCA,MAAAA,OAAO,CAACwB,OAAO,CAACtB,IAAI,CAAC,CAAC;QACtB,OAAOF,OAAO,CAAC;EACnB,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,EAAA,OAAAa,OAAA,CAAA;EAAA,CAAA,EAAA,CAAA;EAEL;EACA,SAASY,QAAQA,CAACxQ,KAAK,EAAE;IACrB,OAAOlM,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACqL,KAAK,CAAC,KAAK,iBAAiB,CAAA;EACtE,CAAA;EACA;EACA;EACA;EACA;EACA;EACayQ,IAAAA,OAAO,0BAAAhR,QAAA,EAAA;IAAAZ,SAAA,CAAA4R,OAAA,EAAAhR,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAX,MAAA,GAAAC,YAAA,CAAA0R,OAAA,CAAA,CAAA;EAChB;EACJ;EACA;EACA;EACA;IACI,SAAAA,OAAAA,CAAYC,OAAO,EAAE;EAAA,IAAA,IAAAvR,KAAA,CAAA;EAAAC,IAAAA,eAAA,OAAAqR,OAAA,CAAA,CAAA;MACjBtR,KAAA,GAAAL,MAAA,CAAAnK,IAAA,CAAA,IAAA,CAAA,CAAA;MACAwK,KAAA,CAAKuR,OAAO,GAAGA,OAAO,CAAA;EAAC,IAAA,OAAAvR,KAAA,CAAA;EAC3B,GAAA;EACA;EACJ;EACA;EACA;EACA;EAJIE,EAAAA,YAAA,CAAAoR,OAAA,EAAA,CAAA;MAAAtc,GAAA,EAAA,KAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAA2Q,GAAI5b,CAAAA,GAAG,EAAE;EACL,MAAA,IAAIoB,MAAM,CAAA;EACV,MAAA,IAAI,OAAOpB,GAAG,KAAK,QAAQ,EAAE;UACzB,IAAI,IAAI,CAAC6b,aAAa,EAAE;EACpB,UAAA,MAAM,IAAIrR,KAAK,CAAC,iDAAiD,CAAC,CAAA;EACtE,SAAA;EACApJ,QAAAA,MAAM,GAAG,IAAI,CAAC0a,YAAY,CAAC9b,GAAG,CAAC,CAAA;UAC/B,IAAM+b,aAAa,GAAG3a,MAAM,CAAC9B,IAAI,KAAKsb,UAAU,CAACM,YAAY,CAAA;UAC7D,IAAIa,aAAa,IAAI3a,MAAM,CAAC9B,IAAI,KAAKsb,UAAU,CAACO,UAAU,EAAE;YACxD/Z,MAAM,CAAC9B,IAAI,GAAGyc,aAAa,GAAGnB,UAAU,CAACG,KAAK,GAAGH,UAAU,CAACI,GAAG,CAAA;EAC/D;EACA,UAAA,IAAI,CAACa,aAAa,GAAG,IAAIG,mBAAmB,CAAC5a,MAAM,CAAC,CAAA;EACpD;EACA,UAAA,IAAIA,MAAM,CAACgZ,WAAW,KAAK,CAAC,EAAE;cAC1BjP,IAAA,CAAAC,eAAA,CAAAsQ,OAAA,CAAAhc,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAAE,IAAA,CAAA,IAAA,EAAmB,SAAS,EAAEwB,MAAM,CAAA,CAAA;EACxC,WAAA;EACJ,SAAC,MACI;EACD;YACA+J,IAAA,CAAAC,eAAA,CAAAsQ,OAAA,CAAAhc,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAAE,IAAA,CAAA,IAAA,EAAmB,SAAS,EAAEwB,MAAM,CAAA,CAAA;EACxC,SAAA;SACH,MACI,IAAIyE,QAAQ,CAAC7F,GAAG,CAAC,IAAIA,GAAG,CAACgC,MAAM,EAAE;EAClC;EACA,QAAA,IAAI,CAAC,IAAI,CAAC6Z,aAAa,EAAE;EACrB,UAAA,MAAM,IAAIrR,KAAK,CAAC,kDAAkD,CAAC,CAAA;EACvE,SAAC,MACI;YACDpJ,MAAM,GAAG,IAAI,CAACya,aAAa,CAACI,cAAc,CAACjc,GAAG,CAAC,CAAA;EAC/C,UAAA,IAAIoB,MAAM,EAAE;EACR;cACA,IAAI,CAACya,aAAa,GAAG,IAAI,CAAA;cACzB1Q,IAAA,CAAAC,eAAA,CAAAsQ,OAAA,CAAAhc,SAAA,CAAA,EAAA,cAAA,EAAA,IAAA,CAAA,CAAAE,IAAA,CAAA,IAAA,EAAmB,SAAS,EAAEwB,MAAM,CAAA,CAAA;EACxC,WAAA;EACJ,SAAA;EACJ,OAAC,MACI;EACD,QAAA,MAAM,IAAIoJ,KAAK,CAAC,gBAAgB,GAAGxK,GAAG,CAAC,CAAA;EAC3C,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAZ,GAAA,EAAA,cAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAA6Q,YAAa3S,CAAAA,GAAG,EAAE;QACd,IAAIvH,CAAC,GAAG,CAAC,CAAA;EACT;EACA,MAAA,IAAMO,CAAC,GAAG;UACN7C,IAAI,EAAEwN,MAAM,CAAC3D,GAAG,CAACrG,MAAM,CAAC,CAAC,CAAC,CAAA;SAC7B,CAAA;QACD,IAAI8X,UAAU,CAACzY,CAAC,CAAC7C,IAAI,CAAC,KAAK+M,SAAS,EAAE;UAClC,MAAM,IAAI7B,KAAK,CAAC,sBAAsB,GAAGrI,CAAC,CAAC7C,IAAI,CAAC,CAAA;EACpD,OAAA;EACA;EACA,MAAA,IAAI6C,CAAC,CAAC7C,IAAI,KAAKsb,UAAU,CAACM,YAAY,IAClC/Y,CAAC,CAAC7C,IAAI,KAAKsb,UAAU,CAACO,UAAU,EAAE;EAClC,QAAA,IAAMe,KAAK,GAAGta,CAAC,GAAG,CAAC,CAAA;EACnB,QAAA,OAAOuH,GAAG,CAACrG,MAAM,CAAC,EAAElB,CAAC,CAAC,KAAK,GAAG,IAAIA,CAAC,IAAIuH,GAAG,CAACtH,MAAM,EAAE,EAAE;UACrD,IAAMsa,GAAG,GAAGhT,GAAG,CAACnG,SAAS,CAACkZ,KAAK,EAAEta,CAAC,CAAC,CAAA;EACnC,QAAA,IAAIua,GAAG,IAAIrP,MAAM,CAACqP,GAAG,CAAC,IAAIhT,GAAG,CAACrG,MAAM,CAAClB,CAAC,CAAC,KAAK,GAAG,EAAE;EAC7C,UAAA,MAAM,IAAI4I,KAAK,CAAC,qBAAqB,CAAC,CAAA;EAC1C,SAAA;EACArI,QAAAA,CAAC,CAACiY,WAAW,GAAGtN,MAAM,CAACqP,GAAG,CAAC,CAAA;EAC/B,OAAA;EACA;QACA,IAAI,GAAG,KAAKhT,GAAG,CAACrG,MAAM,CAAClB,CAAC,GAAG,CAAC,CAAC,EAAE;EAC3B,QAAA,IAAMsa,MAAK,GAAGta,CAAC,GAAG,CAAC,CAAA;UACnB,OAAO,EAAEA,CAAC,EAAE;EACR,UAAA,IAAMwH,CAAC,GAAGD,GAAG,CAACrG,MAAM,CAAClB,CAAC,CAAC,CAAA;YACvB,IAAI,GAAG,KAAKwH,CAAC,EACT,MAAA;EACJ,UAAA,IAAIxH,CAAC,KAAKuH,GAAG,CAACtH,MAAM,EAChB,MAAA;EACR,SAAA;UACAM,CAAC,CAACiZ,GAAG,GAAGjS,GAAG,CAACnG,SAAS,CAACkZ,MAAK,EAAEta,CAAC,CAAC,CAAA;EACnC,OAAC,MACI;UACDO,CAAC,CAACiZ,GAAG,GAAG,GAAG,CAAA;EACf,OAAA;EACA;QACA,IAAMgB,IAAI,GAAGjT,GAAG,CAACrG,MAAM,CAAClB,CAAC,GAAG,CAAC,CAAC,CAAA;QAC9B,IAAI,EAAE,KAAKwa,IAAI,IAAItP,MAAM,CAACsP,IAAI,CAAC,IAAIA,IAAI,EAAE;EACrC,QAAA,IAAMF,OAAK,GAAGta,CAAC,GAAG,CAAC,CAAA;UACnB,OAAO,EAAEA,CAAC,EAAE;EACR,UAAA,IAAMwH,EAAC,GAAGD,GAAG,CAACrG,MAAM,CAAClB,CAAC,CAAC,CAAA;YACvB,IAAI,IAAI,IAAIwH,EAAC,IAAI0D,MAAM,CAAC1D,EAAC,CAAC,IAAIA,EAAC,EAAE;EAC7B,YAAA,EAAExH,CAAC,CAAA;EACH,YAAA,MAAA;EACJ,WAAA;EACA,UAAA,IAAIA,CAAC,KAAKuH,GAAG,CAACtH,MAAM,EAChB,MAAA;EACR,SAAA;EACAM,QAAAA,CAAC,CAAC6U,EAAE,GAAGlK,MAAM,CAAC3D,GAAG,CAACnG,SAAS,CAACkZ,OAAK,EAAEta,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;EAC9C,OAAA;EACA;EACA,MAAA,IAAIuH,GAAG,CAACrG,MAAM,CAAC,EAAElB,CAAC,CAAC,EAAE;EACjB,QAAA,IAAMya,OAAO,GAAG,IAAI,CAACC,QAAQ,CAACnT,GAAG,CAACoT,MAAM,CAAC3a,CAAC,CAAC,CAAC,CAAA;UAC5C,IAAI8Z,OAAO,CAACc,cAAc,CAACra,CAAC,CAAC7C,IAAI,EAAE+c,OAAO,CAAC,EAAE;YACzCla,CAAC,CAAC5C,IAAI,GAAG8c,OAAO,CAAA;EACpB,SAAC,MACI;EACD,UAAA,MAAM,IAAI7R,KAAK,CAAC,iBAAiB,CAAC,CAAA;EACtC,SAAA;EACJ,OAAA;EACA,MAAA,OAAOrI,CAAC,CAAA;EACZ,KAAA;EAAC,GAAA,EAAA;MAAA/C,GAAA,EAAA,UAAA;EAAA6L,IAAAA,KAAA,EACD,SAAAqR,QAASnT,CAAAA,GAAG,EAAE;QACV,IAAI;UACA,OAAOsP,IAAI,CAACtD,KAAK,CAAChM,GAAG,EAAE,IAAI,CAACwS,OAAO,CAAC,CAAA;SACvC,CACD,OAAO7N,CAAC,EAAE;EACN,QAAA,OAAO,KAAK,CAAA;EAChB,OAAA;EACJ,KAAA;EAAC,GAAA,EAAA;MAAA1O,GAAA,EAAA,SAAA;MAAA6L,KAAA;EAoBD;EACJ;EACA;EACI,IAAA,SAAAwR,UAAU;QACN,IAAI,IAAI,CAACZ,aAAa,EAAE;EACpB,QAAA,IAAI,CAACA,aAAa,CAACa,sBAAsB,EAAE,CAAA;UAC3C,IAAI,CAACb,aAAa,GAAG,IAAI,CAAA;EAC7B,OAAA;EACJ,KAAA;EAAC,GAAA,CAAA,EAAA,CAAA;MAAAzc,GAAA,EAAA,gBAAA;EAAA6L,IAAAA,KAAA,EA3BD,SAAAuR,cAAAA,CAAsBld,IAAI,EAAE+c,OAAO,EAAE;EACjC,MAAA,QAAQ/c,IAAI;UACR,KAAKsb,UAAU,CAAC+B,OAAO;YACnB,OAAOlB,QAAQ,CAACY,OAAO,CAAC,CAAA;UAC5B,KAAKzB,UAAU,CAACgC,UAAU;YACtB,OAAOP,OAAO,KAAKhQ,SAAS,CAAA;UAChC,KAAKuO,UAAU,CAACiC,aAAa;YACzB,OAAO,OAAOR,OAAO,KAAK,QAAQ,IAAIZ,QAAQ,CAACY,OAAO,CAAC,CAAA;UAC3D,KAAKzB,UAAU,CAACG,KAAK,CAAA;UACrB,KAAKH,UAAU,CAACM,YAAY;EACxB,UAAA,OAAQzX,KAAK,CAACqW,OAAO,CAACuC,OAAO,CAAC,KACzB,OAAOA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC1B,OAAOA,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAC3B1B,iBAAe,CAAChO,OAAO,CAAC0P,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAE,CAAC,CAAA;UAC5D,KAAKzB,UAAU,CAACI,GAAG,CAAA;UACnB,KAAKJ,UAAU,CAACO,UAAU;EACtB,UAAA,OAAO1X,KAAK,CAACqW,OAAO,CAACuC,OAAO,CAAC,CAAA;EACrC,OAAA;EACJ,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,EAAA,OAAAX,OAAA,CAAA;EAAA,CAAA,CArJwBrV,OAAO,CAAA,CAAA;EAgKpC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAPA,IAQM2V,mBAAmB,gBAAA,YAAA;IACrB,SAAAA,mBAAAA,CAAY5a,MAAM,EAAE;EAAAiJ,IAAAA,eAAA,OAAA2R,mBAAA,CAAA,CAAA;MAChB,IAAI,CAAC5a,MAAM,GAAGA,MAAM,CAAA;MACpB,IAAI,CAAC4Y,OAAO,GAAG,EAAE,CAAA;MACjB,IAAI,CAAC8C,SAAS,GAAG1b,MAAM,CAAA;EAC3B,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EAPIkJ,EAAAA,YAAA,CAAA0R,mBAAA,EAAA,CAAA;MAAA5c,GAAA,EAAA,gBAAA;EAAA6L,IAAAA,KAAA,EAQA,SAAAgR,cAAec,CAAAA,OAAO,EAAE;EACpB,MAAA,IAAI,CAAC/C,OAAO,CAACjW,IAAI,CAACgZ,OAAO,CAAC,CAAA;QAC1B,IAAI,IAAI,CAAC/C,OAAO,CAACnY,MAAM,KAAK,IAAI,CAACib,SAAS,CAAC1C,WAAW,EAAE;EACpD;UACA,IAAMhZ,MAAM,GAAGoZ,iBAAiB,CAAC,IAAI,CAACsC,SAAS,EAAE,IAAI,CAAC9C,OAAO,CAAC,CAAA;UAC9D,IAAI,CAAC0C,sBAAsB,EAAE,CAAA;EAC7B,QAAA,OAAOtb,MAAM,CAAA;EACjB,OAAA;EACA,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EAFI,GAAA,EAAA;MAAAhC,GAAA,EAAA,wBAAA;MAAA6L,KAAA,EAGA,SAAAyR,sBAAAA,GAAyB;QACrB,IAAI,CAACI,SAAS,GAAG,IAAI,CAAA;QACrB,IAAI,CAAC9C,OAAO,GAAG,EAAE,CAAA;EACrB,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,EAAA,OAAAgC,mBAAA,CAAA;EAAA,CAAA,EAAA;;;;;;;;;;ECrTE,SAASzV,EAAEA,CAACvG,GAAG,EAAEqT,EAAE,EAAE3M,EAAE,EAAE;EAC5B1G,EAAAA,GAAG,CAACuG,EAAE,CAAC8M,EAAE,EAAE3M,EAAE,CAAC,CAAA;IACd,OAAO,SAASsW,UAAUA,GAAG;EACzBhd,IAAAA,GAAG,CAAC6G,GAAG,CAACwM,EAAE,EAAE3M,EAAE,CAAC,CAAA;KAClB,CAAA;EACL;;ECFA;EACA;EACA;EACA;EACA,IAAMiU,eAAe,GAAG5b,MAAM,CAACke,MAAM,CAAC;EAClCC,EAAAA,OAAO,EAAE,CAAC;EACVC,EAAAA,aAAa,EAAE,CAAC;EAChBC,EAAAA,UAAU,EAAE,CAAC;EACbC,EAAAA,aAAa,EAAE,CAAC;EAChB;EACAC,EAAAA,WAAW,EAAE,CAAC;EACdtW,EAAAA,cAAc,EAAE,CAAA;EACpB,CAAC,CAAC,CAAA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACaoP,IAAAA,MAAM,0BAAA1L,QAAA,EAAA;IAAAZ,SAAA,CAAAsM,MAAA,EAAA1L,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAX,MAAA,GAAAC,YAAA,CAAAoM,MAAA,CAAA,CAAA;EACf;EACJ;EACA;EACI,EAAA,SAAAA,OAAYmH,EAAE,EAAEnC,GAAG,EAAEzS,IAAI,EAAE;EAAA,IAAA,IAAAyB,KAAA,CAAA;EAAAC,IAAAA,eAAA,OAAA+L,MAAA,CAAA,CAAA;MACvBhM,KAAA,GAAAL,MAAA,CAAAnK,IAAA,CAAA,IAAA,CAAA,CAAA;EACA;EACR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;MACQwK,KAAA,CAAKoT,SAAS,GAAG,KAAK,CAAA;EACtB;EACR;EACA;EACA;MACQpT,KAAA,CAAKqT,SAAS,GAAG,KAAK,CAAA;EACtB;EACR;EACA;MACQrT,KAAA,CAAKsT,aAAa,GAAG,EAAE,CAAA;EACvB;EACR;EACA;MACQtT,KAAA,CAAKuT,UAAU,GAAG,EAAE,CAAA;EACpB;EACR;EACA;EACA;EACA;EACA;MACQvT,KAAA,CAAKwT,MAAM,GAAG,EAAE,CAAA;EAChB;EACR;EACA;EACA;MACQxT,KAAA,CAAKyT,SAAS,GAAG,CAAC,CAAA;MAClBzT,KAAA,CAAK0T,GAAG,GAAG,CAAC,CAAA;EACZ1T,IAAAA,KAAA,CAAK2T,IAAI,GAAG,EAAE,CAAA;EACd3T,IAAAA,KAAA,CAAK4T,KAAK,GAAG,EAAE,CAAA;MACf5T,KAAA,CAAKmT,EAAE,GAAGA,EAAE,CAAA;MACZnT,KAAA,CAAKgR,GAAG,GAAGA,GAAG,CAAA;EACd,IAAA,IAAIzS,IAAI,IAAIA,IAAI,CAACsV,IAAI,EAAE;EACnB7T,MAAAA,KAAA,CAAK6T,IAAI,GAAGtV,IAAI,CAACsV,IAAI,CAAA;EACzB,KAAA;MACA7T,KAAA,CAAK8T,KAAK,GAAGtO,QAAA,CAAc,EAAE,EAAEjH,IAAI,CAAC,CAAA;MACpC,IAAIyB,KAAA,CAAKmT,EAAE,CAACY,YAAY,EACpB/T,KAAA,CAAKiB,IAAI,EAAE,CAAA;EAAC,IAAA,OAAAjB,KAAA,CAAA;EACpB,GAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAbIE,EAAAA,YAAA,CAAA8L,MAAA,EAAA,CAAA;MAAAhX,GAAA,EAAA,cAAA;MAAA0P,GAAA,EAcA,SAAAA,GAAAA,GAAmB;QACf,OAAO,CAAC,IAAI,CAAC0O,SAAS,CAAA;EAC1B,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAApe,GAAA,EAAA,WAAA;MAAA6L,KAAA,EAKA,SAAAmT,SAAAA,GAAY;QACR,IAAI,IAAI,CAACC,IAAI,EACT,OAAA;EACJ,MAAA,IAAMd,EAAE,GAAG,IAAI,CAACA,EAAE,CAAA;EAClB,MAAA,IAAI,CAACc,IAAI,GAAG,CACR9X,EAAE,CAACgX,EAAE,EAAE,MAAM,EAAE,IAAI,CAACzK,MAAM,CAAChK,IAAI,CAAC,IAAI,CAAC,CAAC,EACtCvC,EAAE,CAACgX,EAAE,EAAE,QAAQ,EAAE,IAAI,CAACe,QAAQ,CAACxV,IAAI,CAAC,IAAI,CAAC,CAAC,EAC1CvC,EAAE,CAACgX,EAAE,EAAE,OAAO,EAAE,IAAI,CAACjK,OAAO,CAACxK,IAAI,CAAC,IAAI,CAAC,CAAC,EACxCvC,EAAE,CAACgX,EAAE,EAAE,OAAO,EAAE,IAAI,CAACrK,OAAO,CAACpK,IAAI,CAAC,IAAI,CAAC,CAAC,CAC3C,CAAA;EACL,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAhBI,GAAA,EAAA;MAAA1J,GAAA,EAAA,QAAA;MAAA0P,GAAA,EAiBA,SAAAA,GAAAA,GAAa;EACT,MAAA,OAAO,CAAC,CAAC,IAAI,CAACuP,IAAI,CAAA;EACtB,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EATI,GAAA,EAAA;MAAAjf,GAAA,EAAA,SAAA;MAAA6L,KAAA,EAUA,SAAAiS,OAAAA,GAAU;EACN,MAAA,IAAI,IAAI,CAACM,SAAS,EACd,OAAO,IAAI,CAAA;QACf,IAAI,CAACY,SAAS,EAAE,CAAA;EAChB,MAAA,IAAI,CAAC,IAAI,CAACb,EAAE,CAAC,eAAe,CAAC,EACzB,IAAI,CAACA,EAAE,CAAClS,IAAI,EAAE,CAAC;EACnB,MAAA,IAAI,MAAM,KAAK,IAAI,CAACkS,EAAE,CAACgB,WAAW,EAC9B,IAAI,CAACzL,MAAM,EAAE,CAAA;EACjB,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EAFI,GAAA,EAAA;MAAA1T,GAAA,EAAA,MAAA;MAAA6L,KAAA,EAGA,SAAAI,IAAAA,GAAO;EACH,MAAA,OAAO,IAAI,CAAC6R,OAAO,EAAE,CAAA;EACzB,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAdI,GAAA,EAAA;MAAA9d,GAAA,EAAA,MAAA;MAAA6L,KAAA,EAeA,SAAAU,IAAAA,GAAc;EAAA,MAAA,KAAA,IAAA3D,IAAA,GAAAjB,SAAA,CAAAlF,MAAA,EAAN0F,IAAI,GAAA9D,IAAAA,KAAA,CAAAuE,IAAA,GAAAE,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA,EAAA,EAAA;EAAJX,QAAAA,IAAI,CAAAW,IAAA,CAAAnB,GAAAA,SAAA,CAAAmB,IAAA,CAAA,CAAA;EAAA,OAAA;EACRX,MAAAA,IAAI,CAACiU,OAAO,CAAC,SAAS,CAAC,CAAA;QACvB,IAAI,CAAClU,IAAI,CAACR,KAAK,CAAC,IAAI,EAAES,IAAI,CAAC,CAAA;EAC3B,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAhBI,GAAA,EAAA;MAAAnI,GAAA,EAAA,MAAA;EAAA6L,IAAAA,KAAA,EAiBA,SAAA3D,IAAK+L,CAAAA,EAAE,EAAW;EACd,MAAA,IAAIsH,eAAe,CAACvS,cAAc,CAACiL,EAAE,CAAC,EAAE;EACpC,QAAA,MAAM,IAAI7I,KAAK,CAAC,GAAG,GAAG6I,EAAE,CAAC1T,QAAQ,EAAE,GAAG,4BAA4B,CAAC,CAAA;EACvE,OAAA;QAAC,KAAA6e,IAAAA,KAAA,GAAAzX,SAAA,CAAAlF,MAAA,EAHO0F,IAAI,OAAA9D,KAAA,CAAA+a,KAAA,GAAAA,CAAAA,GAAAA,KAAA,WAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;EAAJlX,QAAAA,IAAI,CAAAkX,KAAA,GAAA1X,CAAAA,CAAAA,GAAAA,SAAA,CAAA0X,KAAA,CAAA,CAAA;EAAA,OAAA;EAIZlX,MAAAA,IAAI,CAACiU,OAAO,CAACnI,EAAE,CAAC,CAAA;EAChB,MAAA,IAAI,IAAI,CAAC6K,KAAK,CAACQ,OAAO,IAAI,CAAC,IAAI,CAACV,KAAK,CAACW,SAAS,IAAI,CAAC,IAAI,CAACX,KAAK,YAAS,EAAE;EACrE,QAAA,IAAI,CAACY,WAAW,CAACrX,IAAI,CAAC,CAAA;EACtB,QAAA,OAAO,IAAI,CAAA;EACf,OAAA;EACA,MAAA,IAAMnG,MAAM,GAAG;UACX9B,IAAI,EAAEsb,UAAU,CAACG,KAAK;EACtBxb,QAAAA,IAAI,EAAEgI,IAAAA;SACT,CAAA;EACDnG,MAAAA,MAAM,CAAC4X,OAAO,GAAG,EAAE,CAAA;QACnB5X,MAAM,CAAC4X,OAAO,CAACC,QAAQ,GAAG,IAAI,CAAC+E,KAAK,CAAC/E,QAAQ,KAAK,KAAK,CAAA;EACvD;QACA,IAAI,UAAU,KAAK,OAAO1R,IAAI,CAACA,IAAI,CAAC1F,MAAM,GAAG,CAAC,CAAC,EAAE;EAC7C,QAAA,IAAMmV,EAAE,GAAG,IAAI,CAAC8G,GAAG,EAAE,CAAA;EACrB,QAAA,IAAMe,GAAG,GAAGtX,IAAI,CAACuX,GAAG,EAAE,CAAA;EACtB,QAAA,IAAI,CAACC,oBAAoB,CAAC/H,EAAE,EAAE6H,GAAG,CAAC,CAAA;UAClCzd,MAAM,CAAC4V,EAAE,GAAGA,EAAE,CAAA;EAClB,OAAA;QACA,IAAMgI,mBAAmB,GAAG,IAAI,CAACzB,EAAE,CAAC0B,MAAM,IACtC,IAAI,CAAC1B,EAAE,CAAC0B,MAAM,CAACtL,SAAS,IACxB,IAAI,CAAC4J,EAAE,CAAC0B,MAAM,CAACtL,SAAS,CAAC9I,QAAQ,CAAA;EACrC,MAAA,IAAMqU,aAAa,GAAG,IAAI,CAAClB,KAAK,CAAS,UAAA,CAAA,KAAK,CAACgB,mBAAmB,IAAI,CAAC,IAAI,CAACxB,SAAS,CAAC,CAAA;QACtF,IAAI0B,aAAa,EAAE,CAClB,MACI,IAAI,IAAI,CAAC1B,SAAS,EAAE;EACrB,QAAA,IAAI,CAAC2B,uBAAuB,CAAC/d,MAAM,CAAC,CAAA;EACpC,QAAA,IAAI,CAACA,MAAM,CAACA,MAAM,CAAC,CAAA;EACvB,OAAC,MACI;EACD,QAAA,IAAI,CAACuc,UAAU,CAAC5Z,IAAI,CAAC3C,MAAM,CAAC,CAAA;EAChC,OAAA;EACA,MAAA,IAAI,CAAC4c,KAAK,GAAG,EAAE,CAAA;EACf,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EAFI,GAAA,EAAA;MAAA5e,GAAA,EAAA,sBAAA;EAAA6L,IAAAA,KAAA,EAGA,SAAA8T,oBAAAA,CAAqB/H,EAAE,EAAE6H,GAAG,EAAE;EAAA,MAAA,IAAAjU,MAAA,GAAA,IAAA,CAAA;EAC1B,MAAA,IAAI0F,EAAE,CAAA;QACN,IAAMM,OAAO,GAAG,CAACN,EAAE,GAAG,IAAI,CAAC0N,KAAK,CAACpN,OAAO,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAC4N,KAAK,CAACkB,UAAU,CAAA;QAChG,IAAIxO,OAAO,KAAKvE,SAAS,EAAE;EACvB,QAAA,IAAI,CAAC0R,IAAI,CAAC/G,EAAE,CAAC,GAAG6H,GAAG,CAAA;EACnB,QAAA,OAAA;EACJ,OAAA;EACA;QACA,IAAMQ,KAAK,GAAG,IAAI,CAAC9B,EAAE,CAAC1U,YAAY,CAAC,YAAM;EACrC,QAAA,OAAO+B,MAAI,CAACmT,IAAI,CAAC/G,EAAE,CAAC,CAAA;EACpB,QAAA,KAAK,IAAIpV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgJ,MAAI,CAAC+S,UAAU,CAAC9b,MAAM,EAAED,CAAC,EAAE,EAAE;YAC7C,IAAIgJ,MAAI,CAAC+S,UAAU,CAAC/b,CAAC,CAAC,CAACoV,EAAE,KAAKA,EAAE,EAAE;cAC9BpM,MAAI,CAAC+S,UAAU,CAACtW,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC,CAAA;EAChC,WAAA;EACJ,SAAA;UACAid,GAAG,CAACjf,IAAI,CAACgL,MAAI,EAAE,IAAIJ,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAAA;SACvD,EAAEoG,OAAO,CAAC,CAAA;EACX,MAAA,IAAI,CAACmN,IAAI,CAAC/G,EAAE,CAAC,GAAG,YAAa;EACzB;EACApM,QAAAA,MAAI,CAAC2S,EAAE,CAACxU,cAAc,CAACsW,KAAK,CAAC,CAAA;EAAC,QAAA,KAAA,IAAAC,KAAA,GAAAvY,SAAA,CAAAlF,MAAA,EAFd0F,IAAI,GAAA9D,IAAAA,KAAA,CAAA6b,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;EAAJhY,UAAAA,IAAI,CAAAgY,KAAA,CAAAxY,GAAAA,SAAA,CAAAwY,KAAA,CAAA,CAAA;EAAA,SAAA;UAGpBV,GAAG,CAAC/X,KAAK,CAAC8D,MAAI,EAAA,CAAG,IAAI,CAAA,CAAAmD,MAAA,CAAKxG,IAAI,CAAC,CAAC,CAAA;SACnC,CAAA;EACL,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAfI,GAAA,EAAA;MAAAnI,GAAA,EAAA,aAAA;EAAA6L,IAAAA,KAAA,EAgBA,SAAAuU,WAAYnM,CAAAA,EAAE,EAAW;EAAA,MAAA,IAAAnE,MAAA,GAAA,IAAA,CAAA;QAAA,KAAAuQ,IAAAA,KAAA,GAAA1Y,SAAA,CAAAlF,MAAA,EAAN0F,IAAI,OAAA9D,KAAA,CAAAgc,KAAA,GAAAA,CAAAA,GAAAA,KAAA,WAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;EAAJnY,QAAAA,IAAI,CAAAmY,KAAA,GAAA3Y,CAAAA,CAAAA,GAAAA,SAAA,CAAA2Y,KAAA,CAAA,CAAA;EAAA,OAAA;EACnB;EACA,MAAA,IAAMC,OAAO,GAAG,IAAI,CAAC3B,KAAK,CAACpN,OAAO,KAAKvE,SAAS,IAAI,IAAI,CAAC6R,KAAK,CAACkB,UAAU,KAAK/S,SAAS,CAAA;EACvF,MAAA,OAAO,IAAIyF,OAAO,CAAC,UAACC,OAAO,EAAE6N,MAAM,EAAK;EACpCrY,QAAAA,IAAI,CAACxD,IAAI,CAAC,UAAC8b,IAAI,EAAEC,IAAI,EAAK;EACtB,UAAA,IAAIH,OAAO,EAAE;cACT,OAAOE,IAAI,GAAGD,MAAM,CAACC,IAAI,CAAC,GAAG9N,OAAO,CAAC+N,IAAI,CAAC,CAAA;EAC9C,WAAC,MACI;cACD,OAAO/N,OAAO,CAAC8N,IAAI,CAAC,CAAA;EACxB,WAAA;EACJ,SAAC,CAAC,CAAA;EACF3Q,QAAAA,MAAI,CAAC5H,IAAI,CAAAR,KAAA,CAAToI,MAAI,EAAMmE,CAAAA,EAAE,CAAAtF,CAAAA,MAAA,CAAKxG,IAAI,CAAC,CAAA,CAAA;EAC1B,OAAC,CAAC,CAAA;EACN,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAnI,GAAA,EAAA,aAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAA2T,WAAYrX,CAAAA,IAAI,EAAE;EAAA,MAAA,IAAA4H,MAAA,GAAA,IAAA,CAAA;EACd,MAAA,IAAI0P,GAAG,CAAA;QACP,IAAI,OAAOtX,IAAI,CAACA,IAAI,CAAC1F,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;EAC7Cgd,QAAAA,GAAG,GAAGtX,IAAI,CAACuX,GAAG,EAAE,CAAA;EACpB,OAAA;EACA,MAAA,IAAM1d,MAAM,GAAG;EACX4V,QAAAA,EAAE,EAAE,IAAI,CAAC6G,SAAS,EAAE;EACpBkC,QAAAA,QAAQ,EAAE,CAAC;EACXC,QAAAA,OAAO,EAAE,KAAK;EACdzY,QAAAA,IAAI,EAAJA,IAAI;UACJyW,KAAK,EAAEpO,QAAA,CAAc;EAAE+O,UAAAA,SAAS,EAAE,IAAA;WAAM,EAAE,IAAI,CAACX,KAAK,CAAA;SACvD,CAAA;EACDzW,MAAAA,IAAI,CAACxD,IAAI,CAAC,UAAC2J,GAAG,EAAsB;UAChC,IAAItM,MAAM,KAAK+N,MAAI,CAACyO,MAAM,CAAC,CAAC,CAAC,EAAE;EAC3B;EACA,UAAA,OAAA;EACJ,SAAA;EACA,QAAA,IAAMqC,QAAQ,GAAGvS,GAAG,KAAK,IAAI,CAAA;EAC7B,QAAA,IAAIuS,QAAQ,EAAE;YACV,IAAI7e,MAAM,CAAC2e,QAAQ,GAAG5Q,MAAI,CAAC+O,KAAK,CAACQ,OAAO,EAAE;EACtCvP,YAAAA,MAAI,CAACyO,MAAM,CAACvY,KAAK,EAAE,CAAA;EACnB,YAAA,IAAIwZ,GAAG,EAAE;gBACLA,GAAG,CAACnR,GAAG,CAAC,CAAA;EACZ,aAAA;EACJ,WAAA;EACJ,SAAC,MACI;EACDyB,UAAAA,MAAI,CAACyO,MAAM,CAACvY,KAAK,EAAE,CAAA;EACnB,UAAA,IAAIwZ,GAAG,EAAE;cAAA,KAAAqB,IAAAA,KAAA,GAAAnZ,SAAA,CAAAlF,MAAA,EAhBEse,YAAY,OAAA1c,KAAA,CAAAyc,KAAA,GAAAA,CAAAA,GAAAA,KAAA,WAAAE,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA,EAAA,EAAA;EAAZD,cAAAA,YAAY,CAAAC,KAAA,GAAArZ,CAAAA,CAAAA,GAAAA,SAAA,CAAAqZ,KAAA,CAAA,CAAA;EAAA,aAAA;cAiBnBvB,GAAG,CAAA/X,KAAA,CAAC,KAAA,CAAA,EAAA,CAAA,IAAI,EAAAiH,MAAA,CAAKoS,YAAY,CAAC,CAAA,CAAA;EAC9B,WAAA;EACJ,SAAA;UACA/e,MAAM,CAAC4e,OAAO,GAAG,KAAK,CAAA;EACtB,QAAA,OAAO7Q,MAAI,CAACkR,WAAW,EAAE,CAAA;EAC7B,OAAC,CAAC,CAAA;EACF,MAAA,IAAI,CAACzC,MAAM,CAAC7Z,IAAI,CAAC3C,MAAM,CAAC,CAAA;QACxB,IAAI,CAACif,WAAW,EAAE,CAAA;EACtB,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAjhB,GAAA,EAAA,aAAA;MAAA6L,KAAA,EAMA,SAAAoV,WAAAA,GAA2B;EAAA,MAAA,IAAfC,KAAK,GAAAvZ,SAAA,CAAAlF,MAAA,GAAA,CAAA,IAAAkF,SAAA,CAAA,CAAA,CAAA,KAAAsF,SAAA,GAAAtF,SAAA,CAAA,CAAA,CAAA,GAAG,KAAK,CAAA;EACrB,MAAA,IAAI,CAAC,IAAI,CAACyW,SAAS,IAAI,IAAI,CAACI,MAAM,CAAC/b,MAAM,KAAK,CAAC,EAAE;EAC7C,QAAA,OAAA;EACJ,OAAA;EACA,MAAA,IAAMT,MAAM,GAAG,IAAI,CAACwc,MAAM,CAAC,CAAC,CAAC,CAAA;EAC7B,MAAA,IAAIxc,MAAM,CAAC4e,OAAO,IAAI,CAACM,KAAK,EAAE;EAC1B,QAAA,OAAA;EACJ,OAAA;QACAlf,MAAM,CAAC4e,OAAO,GAAG,IAAI,CAAA;QACrB5e,MAAM,CAAC2e,QAAQ,EAAE,CAAA;EACjB,MAAA,IAAI,CAAC/B,KAAK,GAAG5c,MAAM,CAAC4c,KAAK,CAAA;QACzB,IAAI,CAAC1W,IAAI,CAACR,KAAK,CAAC,IAAI,EAAE1F,MAAM,CAACmG,IAAI,CAAC,CAAA;EACtC,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAnI,GAAA,EAAA,QAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAA7J,MAAOA,CAAAA,OAAM,EAAE;EACXA,MAAAA,OAAM,CAACga,GAAG,GAAG,IAAI,CAACA,GAAG,CAAA;EACrB,MAAA,IAAI,CAACmC,EAAE,CAACgD,OAAO,CAACnf,OAAM,CAAC,CAAA;EAC3B,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAhC,GAAA,EAAA,QAAA;MAAA6L,KAAA,EAKA,SAAA6H,MAAAA,GAAS;EAAA,MAAA,IAAA1D,MAAA,GAAA,IAAA,CAAA;EACL,MAAA,IAAI,OAAO,IAAI,CAAC6O,IAAI,IAAI,UAAU,EAAE;EAChC,QAAA,IAAI,CAACA,IAAI,CAAC,UAAC1e,IAAI,EAAK;EAChB6P,UAAAA,MAAI,CAACoR,kBAAkB,CAACjhB,IAAI,CAAC,CAAA;EACjC,SAAC,CAAC,CAAA;EACN,OAAC,MACI;EACD,QAAA,IAAI,CAACihB,kBAAkB,CAAC,IAAI,CAACvC,IAAI,CAAC,CAAA;EACtC,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAA7e,GAAA,EAAA,oBAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAuV,kBAAmBjhB,CAAAA,IAAI,EAAE;QACrB,IAAI,CAAC6B,MAAM,CAAC;UACR9B,IAAI,EAAEsb,UAAU,CAAC+B,OAAO;EACxBpd,QAAAA,IAAI,EAAE,IAAI,CAACkhB,IAAI,GACT7Q,QAAA,CAAc;YAAE8Q,GAAG,EAAE,IAAI,CAACD,IAAI;YAAEE,MAAM,EAAE,IAAI,CAACC,WAAAA;WAAa,EAAErhB,IAAI,CAAC,GACjEA,IAAAA;EACV,OAAC,CAAC,CAAA;EACN,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAH,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAqI,OAAQ5F,CAAAA,GAAG,EAAE;EACT,MAAA,IAAI,CAAC,IAAI,CAAC8P,SAAS,EAAE;EACjB,QAAA,IAAI,CAAChW,YAAY,CAAC,eAAe,EAAEkG,GAAG,CAAC,CAAA;EAC3C,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EANI,GAAA,EAAA;MAAAtO,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAOA,SAAAiI,OAAAA,CAAQjJ,MAAM,EAAEC,WAAW,EAAE;QACzB,IAAI,CAACsT,SAAS,GAAG,KAAK,CAAA;QACtB,OAAO,IAAI,CAACxG,EAAE,CAAA;QACd,IAAI,CAACxP,YAAY,CAAC,YAAY,EAAEyC,MAAM,EAAEC,WAAW,CAAC,CAAA;EACxD,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAA9K,GAAA,EAAA,UAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAqT,QAASld,CAAAA,MAAM,EAAE;QACb,IAAMyf,aAAa,GAAGzf,MAAM,CAACga,GAAG,KAAK,IAAI,CAACA,GAAG,CAAA;QAC7C,IAAI,CAACyF,aAAa,EACd,OAAA;QACJ,QAAQzf,MAAM,CAAC9B,IAAI;UACf,KAAKsb,UAAU,CAAC+B,OAAO;YACnB,IAAIvb,MAAM,CAAC7B,IAAI,IAAI6B,MAAM,CAAC7B,IAAI,CAACkQ,GAAG,EAAE;EAChC,YAAA,IAAI,CAACqR,SAAS,CAAC1f,MAAM,CAAC7B,IAAI,CAACkQ,GAAG,EAAErO,MAAM,CAAC7B,IAAI,CAACmhB,GAAG,CAAC,CAAA;EACpD,WAAC,MACI;cACD,IAAI,CAAClZ,YAAY,CAAC,eAAe,EAAE,IAAIgD,KAAK,CAAC,2LAA2L,CAAC,CAAC,CAAA;EAC9O,WAAA;EACA,UAAA,MAAA;UACJ,KAAKoQ,UAAU,CAACG,KAAK,CAAA;UACrB,KAAKH,UAAU,CAACM,YAAY;EACxB,UAAA,IAAI,CAAC6F,OAAO,CAAC3f,MAAM,CAAC,CAAA;EACpB,UAAA,MAAA;UACJ,KAAKwZ,UAAU,CAACI,GAAG,CAAA;UACnB,KAAKJ,UAAU,CAACO,UAAU;EACtB,UAAA,IAAI,CAAC6F,KAAK,CAAC5f,MAAM,CAAC,CAAA;EAClB,UAAA,MAAA;UACJ,KAAKwZ,UAAU,CAACgC,UAAU;YACtB,IAAI,CAACqE,YAAY,EAAE,CAAA;EACnB,UAAA,MAAA;UACJ,KAAKrG,UAAU,CAACiC,aAAa;YACzB,IAAI,CAACJ,OAAO,EAAE,CAAA;YACd,IAAM/O,GAAG,GAAG,IAAIlD,KAAK,CAACpJ,MAAM,CAAC7B,IAAI,CAAC2hB,OAAO,CAAC,CAAA;EAC1C;EACAxT,UAAAA,GAAG,CAACnO,IAAI,GAAG6B,MAAM,CAAC7B,IAAI,CAACA,IAAI,CAAA;EAC3B,UAAA,IAAI,CAACiI,YAAY,CAAC,eAAe,EAAEkG,GAAG,CAAC,CAAA;EACvC,UAAA,MAAA;EACR,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAtO,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAA8V,OAAQ3f,CAAAA,MAAM,EAAE;EACZ,MAAA,IAAMmG,IAAI,GAAGnG,MAAM,CAAC7B,IAAI,IAAI,EAAE,CAAA;EAC9B,MAAA,IAAI,IAAI,IAAI6B,MAAM,CAAC4V,EAAE,EAAE;UACnBzP,IAAI,CAACxD,IAAI,CAAC,IAAI,CAAC8a,GAAG,CAACzd,MAAM,CAAC4V,EAAE,CAAC,CAAC,CAAA;EAClC,OAAA;QACA,IAAI,IAAI,CAACwG,SAAS,EAAE;EAChB,QAAA,IAAI,CAAC2D,SAAS,CAAC5Z,IAAI,CAAC,CAAA;EACxB,OAAC,MACI;UACD,IAAI,CAACmW,aAAa,CAAC3Z,IAAI,CAAChF,MAAM,CAACke,MAAM,CAAC1V,IAAI,CAAC,CAAC,CAAA;EAChD,OAAA;EACJ,KAAA;EAAC,GAAA,EAAA;MAAAnI,GAAA,EAAA,WAAA;EAAA6L,IAAAA,KAAA,EACD,SAAAkW,SAAU5Z,CAAAA,IAAI,EAAE;QACZ,IAAI,IAAI,CAAC6Z,aAAa,IAAI,IAAI,CAACA,aAAa,CAACvf,MAAM,EAAE;UACjD,IAAM4F,SAAS,GAAG,IAAI,CAAC2Z,aAAa,CAAC7b,KAAK,EAAE,CAAA;EAAC,QAAA,IAAA8b,SAAA,GAAAC,0BAAA,CACtB7Z,SAAS,CAAA;YAAA8Z,KAAA,CAAA;EAAA,QAAA,IAAA;YAAhC,KAAAF,SAAA,CAAAG,CAAA,EAAAD,EAAAA,CAAAA,CAAAA,KAAA,GAAAF,SAAA,CAAArb,CAAA,EAAA6O,EAAAA,IAAA,GAAkC;EAAA,YAAA,IAAvB4M,QAAQ,GAAAF,KAAA,CAAAtW,KAAA,CAAA;EACfwW,YAAAA,QAAQ,CAAC3a,KAAK,CAAC,IAAI,EAAES,IAAI,CAAC,CAAA;EAC9B,WAAA;EAAC,SAAA,CAAA,OAAAmG,GAAA,EAAA;YAAA2T,SAAA,CAAAvT,CAAA,CAAAJ,GAAA,CAAA,CAAA;EAAA,SAAA,SAAA;EAAA2T,UAAAA,SAAA,CAAAK,CAAA,EAAA,CAAA;EAAA,SAAA;EACL,OAAA;EACAvW,MAAAA,IAAA,CAAAC,eAAA,CAAAgL,MAAA,CAAA1W,SAAA,CAAWoH,EAAAA,MAAAA,EAAAA,IAAAA,CAAAA,CAAAA,KAAK,CAAC,IAAI,EAAES,IAAI,CAAC,CAAA;EAC5B,MAAA,IAAI,IAAI,CAACkZ,IAAI,IAAIlZ,IAAI,CAAC1F,MAAM,IAAI,OAAO0F,IAAI,CAACA,IAAI,CAAC1F,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;UACvE,IAAI,CAAC+e,WAAW,GAAGrZ,IAAI,CAACA,IAAI,CAAC1F,MAAM,GAAG,CAAC,CAAC,CAAA;EAC5C,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAzC,GAAA,EAAA,KAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAA4T,GAAI7H,CAAAA,EAAE,EAAE;QACJ,IAAMpP,IAAI,GAAG,IAAI,CAAA;QACjB,IAAI+Z,IAAI,GAAG,KAAK,CAAA;EAChB,MAAA,OAAO,YAAmB;EACtB;EACA,QAAA,IAAIA,IAAI,EACJ,OAAA;EACJA,QAAAA,IAAI,GAAG,IAAI,CAAA;EAAC,QAAA,KAAA,IAAAC,KAAA,GAAA7a,SAAA,CAAAlF,MAAA,EAJI0F,IAAI,GAAA9D,IAAAA,KAAA,CAAAme,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,EAAA;EAAJta,UAAAA,IAAI,CAAAsa,KAAA,CAAA9a,GAAAA,SAAA,CAAA8a,KAAA,CAAA,CAAA;EAAA,SAAA;UAKpBja,IAAI,CAACxG,MAAM,CAAC;YACR9B,IAAI,EAAEsb,UAAU,CAACI,GAAG;EACpBhE,UAAAA,EAAE,EAAEA,EAAE;EACNzX,UAAAA,IAAI,EAAEgI,IAAAA;EACV,SAAC,CAAC,CAAA;SACL,CAAA;EACL,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAnI,GAAA,EAAA,OAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAA+V,KAAM5f,CAAAA,MAAM,EAAE;QACV,IAAMyd,GAAG,GAAG,IAAI,CAACd,IAAI,CAAC3c,MAAM,CAAC4V,EAAE,CAAC,CAAA;EAChC,MAAA,IAAI,UAAU,KAAK,OAAO6H,GAAG,EAAE;UAC3BA,GAAG,CAAC/X,KAAK,CAAC,IAAI,EAAE1F,MAAM,CAAC7B,IAAI,CAAC,CAAA;EAC5B,QAAA,OAAO,IAAI,CAACwe,IAAI,CAAC3c,MAAM,CAAC4V,EAAE,CAAC,CAAA;EAC/B,OAEA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA5X,GAAA,EAAA,WAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAA6V,SAAAA,CAAU9J,EAAE,EAAE0J,GAAG,EAAE;QACf,IAAI,CAAC1J,EAAE,GAAGA,EAAE,CAAA;QACZ,IAAI,CAACyG,SAAS,GAAGiD,GAAG,IAAI,IAAI,CAACD,IAAI,KAAKC,GAAG,CAAA;EACzC,MAAA,IAAI,CAACD,IAAI,GAAGC,GAAG,CAAC;QAChB,IAAI,CAAClD,SAAS,GAAG,IAAI,CAAA;QACrB,IAAI,CAACsE,YAAY,EAAE,CAAA;EACnB,MAAA,IAAI,CAACta,YAAY,CAAC,SAAS,CAAC,CAAA;EAC5B,MAAA,IAAI,CAAC6Y,WAAW,CAAC,IAAI,CAAC,CAAA;EAC1B,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAjhB,GAAA,EAAA,cAAA;MAAA6L,KAAA,EAKA,SAAA6W,YAAAA,GAAe;EAAA,MAAA,IAAAhS,MAAA,GAAA,IAAA,CAAA;EACX,MAAA,IAAI,CAAC4N,aAAa,CAACve,OAAO,CAAC,UAACoI,IAAI,EAAA;EAAA,QAAA,OAAKuI,MAAI,CAACqR,SAAS,CAAC5Z,IAAI,CAAC,CAAA;SAAC,CAAA,CAAA;QAC1D,IAAI,CAACmW,aAAa,GAAG,EAAE,CAAA;EACvB,MAAA,IAAI,CAACC,UAAU,CAACxe,OAAO,CAAC,UAACiC,MAAM,EAAK;EAChC0O,QAAAA,MAAI,CAACqP,uBAAuB,CAAC/d,MAAM,CAAC,CAAA;EACpC0O,QAAAA,MAAI,CAAC1O,MAAM,CAACA,MAAM,CAAC,CAAA;EACvB,OAAC,CAAC,CAAA;QACF,IAAI,CAACuc,UAAU,GAAG,EAAE,CAAA;EACxB,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAve,GAAA,EAAA,cAAA;MAAA6L,KAAA,EAKA,SAAAgW,YAAAA,GAAe;QACX,IAAI,CAACxE,OAAO,EAAE,CAAA;EACd,MAAA,IAAI,CAACvJ,OAAO,CAAC,sBAAsB,CAAC,CAAA;EACxC,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EANI,GAAA,EAAA;MAAA9T,GAAA,EAAA,SAAA;MAAA6L,KAAA,EAOA,SAAAwR,OAAAA,GAAU;QACN,IAAI,IAAI,CAAC4B,IAAI,EAAE;EACX;EACA,QAAA,IAAI,CAACA,IAAI,CAAClf,OAAO,CAAC,UAAC6d,UAAU,EAAA;YAAA,OAAKA,UAAU,EAAE,CAAA;WAAC,CAAA,CAAA;UAC/C,IAAI,CAACqB,IAAI,GAAGhS,SAAS,CAAA;EACzB,OAAA;EACA,MAAA,IAAI,CAACkR,EAAE,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAA;EAC7B,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAfI,GAAA,EAAA;MAAAne,GAAA,EAAA,YAAA;MAAA6L,KAAA,EAgBA,SAAAmS,UAAAA,GAAa;QACT,IAAI,IAAI,CAACI,SAAS,EAAE;UAChB,IAAI,CAACpc,MAAM,CAAC;YAAE9B,IAAI,EAAEsb,UAAU,CAACgC,UAAAA;EAAW,SAAC,CAAC,CAAA;EAChD,OAAA;EACA;QACA,IAAI,CAACH,OAAO,EAAE,CAAA;QACd,IAAI,IAAI,CAACe,SAAS,EAAE;EAChB;EACA,QAAA,IAAI,CAACtK,OAAO,CAAC,sBAAsB,CAAC,CAAA;EACxC,OAAA;EACA,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA9T,GAAA,EAAA,OAAA;MAAA6L,KAAA,EAKA,SAAAO,KAAAA,GAAQ;EACJ,MAAA,OAAO,IAAI,CAAC4R,UAAU,EAAE,CAAA;EAC5B,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EARI,GAAA,EAAA;MAAAhe,GAAA,EAAA,UAAA;EAAA6L,IAAAA,KAAA,EASA,SAAAgO,QAASA,CAAAA,SAAQ,EAAE;EACf,MAAA,IAAI,CAAC+E,KAAK,CAAC/E,QAAQ,GAAGA,SAAQ,CAAA;EAC9B,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EARI,GAAA,EAAA;MAAA7Z,GAAA,EAAA,UAAA;MAAA0P,GAAA,EASA,SAAAA,GAAAA,GAAe;EACX,MAAA,IAAI,CAACkP,KAAK,CAAS,UAAA,CAAA,GAAG,IAAI,CAAA;EAC1B,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAZI,GAAA,EAAA;MAAA5e,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAaA,SAAA2F,OAAQA,CAAAA,QAAO,EAAE;EACb,MAAA,IAAI,CAACoN,KAAK,CAACpN,OAAO,GAAGA,QAAO,CAAA;EAC5B,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAVI,GAAA,EAAA;MAAAxR,GAAA,EAAA,OAAA;EAAA6L,IAAAA,KAAA,EAWA,SAAA8W,KAAMN,CAAAA,QAAQ,EAAE;EACZ,MAAA,IAAI,CAACL,aAAa,GAAG,IAAI,CAACA,aAAa,IAAI,EAAE,CAAA;EAC7C,MAAA,IAAI,CAACA,aAAa,CAACrd,IAAI,CAAC0d,QAAQ,CAAC,CAAA;EACjC,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAVI,GAAA,EAAA;MAAAriB,GAAA,EAAA,YAAA;EAAA6L,IAAAA,KAAA,EAWA,SAAA+W,UAAWP,CAAAA,QAAQ,EAAE;EACjB,MAAA,IAAI,CAACL,aAAa,GAAG,IAAI,CAACA,aAAa,IAAI,EAAE,CAAA;EAC7C,MAAA,IAAI,CAACA,aAAa,CAAC5F,OAAO,CAACiG,QAAQ,CAAC,CAAA;EACpC,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAjBI,GAAA,EAAA;MAAAriB,GAAA,EAAA,QAAA;EAAA6L,IAAAA,KAAA,EAkBA,SAAAgX,MAAOR,CAAAA,QAAQ,EAAE;EACb,MAAA,IAAI,CAAC,IAAI,CAACL,aAAa,EAAE;EACrB,QAAA,OAAO,IAAI,CAAA;EACf,OAAA;EACA,MAAA,IAAIK,QAAQ,EAAE;EACV,QAAA,IAAMha,SAAS,GAAG,IAAI,CAAC2Z,aAAa,CAAA;EACpC,QAAA,KAAK,IAAIxf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,SAAS,CAAC5F,MAAM,EAAED,CAAC,EAAE,EAAE;EACvC,UAAA,IAAI6f,QAAQ,KAAKha,SAAS,CAAC7F,CAAC,CAAC,EAAE;EAC3B6F,YAAAA,SAAS,CAACJ,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC,CAAA;EACtB,YAAA,OAAO,IAAI,CAAA;EACf,WAAA;EACJ,SAAA;EACJ,OAAC,MACI;UACD,IAAI,CAACwf,aAAa,GAAG,EAAE,CAAA;EAC3B,OAAA;EACA,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EAHI,GAAA,EAAA;MAAAhiB,GAAA,EAAA,cAAA;MAAA6L,KAAA,EAIA,SAAAiX,YAAAA,GAAe;EACX,MAAA,OAAO,IAAI,CAACd,aAAa,IAAI,EAAE,CAAA;EACnC,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAZI,GAAA,EAAA;MAAAhiB,GAAA,EAAA,eAAA;EAAA6L,IAAAA,KAAA,EAaA,SAAAkX,aAAcV,CAAAA,QAAQ,EAAE;EACpB,MAAA,IAAI,CAACW,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,IAAI,EAAE,CAAA;EAC7D,MAAA,IAAI,CAACA,qBAAqB,CAACre,IAAI,CAAC0d,QAAQ,CAAC,CAAA;EACzC,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAZI,GAAA,EAAA;MAAAriB,GAAA,EAAA,oBAAA;EAAA6L,IAAAA,KAAA,EAaA,SAAAoX,kBAAmBZ,CAAAA,QAAQ,EAAE;EACzB,MAAA,IAAI,CAACW,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,IAAI,EAAE,CAAA;EAC7D,MAAA,IAAI,CAACA,qBAAqB,CAAC5G,OAAO,CAACiG,QAAQ,CAAC,CAAA;EAC5C,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAjBI,GAAA,EAAA;MAAAriB,GAAA,EAAA,gBAAA;EAAA6L,IAAAA,KAAA,EAkBA,SAAAqX,cAAeb,CAAAA,QAAQ,EAAE;EACrB,MAAA,IAAI,CAAC,IAAI,CAACW,qBAAqB,EAAE;EAC7B,QAAA,OAAO,IAAI,CAAA;EACf,OAAA;EACA,MAAA,IAAIX,QAAQ,EAAE;EACV,QAAA,IAAMha,SAAS,GAAG,IAAI,CAAC2a,qBAAqB,CAAA;EAC5C,QAAA,KAAK,IAAIxgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,SAAS,CAAC5F,MAAM,EAAED,CAAC,EAAE,EAAE;EACvC,UAAA,IAAI6f,QAAQ,KAAKha,SAAS,CAAC7F,CAAC,CAAC,EAAE;EAC3B6F,YAAAA,SAAS,CAACJ,MAAM,CAACzF,CAAC,EAAE,CAAC,CAAC,CAAA;EACtB,YAAA,OAAO,IAAI,CAAA;EACf,WAAA;EACJ,SAAA;EACJ,OAAC,MACI;UACD,IAAI,CAACwgB,qBAAqB,GAAG,EAAE,CAAA;EACnC,OAAA;EACA,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EAHI,GAAA,EAAA;MAAAhjB,GAAA,EAAA,sBAAA;MAAA6L,KAAA,EAIA,SAAAsX,oBAAAA,GAAuB;EACnB,MAAA,OAAO,IAAI,CAACH,qBAAqB,IAAI,EAAE,CAAA;EAC3C,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EANI,GAAA,EAAA;MAAAhjB,GAAA,EAAA,yBAAA;EAAA6L,IAAAA,KAAA,EAOA,SAAAkU,uBAAwB/d,CAAAA,MAAM,EAAE;QAC5B,IAAI,IAAI,CAACghB,qBAAqB,IAAI,IAAI,CAACA,qBAAqB,CAACvgB,MAAM,EAAE;UACjE,IAAM4F,SAAS,GAAG,IAAI,CAAC2a,qBAAqB,CAAC7c,KAAK,EAAE,CAAA;EAAC,QAAA,IAAAid,UAAA,GAAAlB,0BAAA,CAC9B7Z,SAAS,CAAA;YAAAgb,MAAA,CAAA;EAAA,QAAA,IAAA;YAAhC,KAAAD,UAAA,CAAAhB,CAAA,EAAAiB,EAAAA,CAAAA,CAAAA,MAAA,GAAAD,UAAA,CAAAxc,CAAA,EAAA6O,EAAAA,IAAA,GAAkC;EAAA,YAAA,IAAvB4M,QAAQ,GAAAgB,MAAA,CAAAxX,KAAA,CAAA;cACfwW,QAAQ,CAAC3a,KAAK,CAAC,IAAI,EAAE1F,MAAM,CAAC7B,IAAI,CAAC,CAAA;EACrC,WAAA;EAAC,SAAA,CAAA,OAAAmO,GAAA,EAAA;YAAA8U,UAAA,CAAA1U,CAAA,CAAAJ,GAAA,CAAA,CAAA;EAAA,SAAA,SAAA;EAAA8U,UAAAA,UAAA,CAAAd,CAAA,EAAA,CAAA;EAAA,SAAA;EACL,OAAA;EACJ,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,EAAA,OAAAtL,MAAA,CAAA;EAAA,CAAA,CA5xBuB/P,OAAO,CAAA;;ECxCnC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASqc,OAAOA,CAAC/Z,IAAI,EAAE;EAC1BA,EAAAA,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;EACjB,EAAA,IAAI,CAACga,EAAE,GAAGha,IAAI,CAACia,GAAG,IAAI,GAAG,CAAA;EACzB,EAAA,IAAI,CAACC,GAAG,GAAGla,IAAI,CAACka,GAAG,IAAI,KAAK,CAAA;EAC5B,EAAA,IAAI,CAACC,MAAM,GAAGna,IAAI,CAACma,MAAM,IAAI,CAAC,CAAA;EAC9B,EAAA,IAAI,CAACC,MAAM,GAAGpa,IAAI,CAACoa,MAAM,GAAG,CAAC,IAAIpa,IAAI,CAACoa,MAAM,IAAI,CAAC,GAAGpa,IAAI,CAACoa,MAAM,GAAG,CAAC,CAAA;IACnE,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAA;EACrB,CAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACAN,OAAO,CAAChjB,SAAS,CAACujB,QAAQ,GAAG,YAAY;EACrC,EAAA,IAAIN,EAAE,GAAG,IAAI,CAACA,EAAE,GAAGzc,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC2c,MAAM,EAAE,IAAI,CAACE,QAAQ,EAAE,CAAC,CAAA;IACzD,IAAI,IAAI,CAACD,MAAM,EAAE;EACb,IAAA,IAAIG,IAAI,GAAGhd,IAAI,CAACid,MAAM,EAAE,CAAA;EACxB,IAAA,IAAIC,SAAS,GAAGld,IAAI,CAACmH,KAAK,CAAC6V,IAAI,GAAG,IAAI,CAACH,MAAM,GAAGJ,EAAE,CAAC,CAAA;MACnDA,EAAE,GAAG,CAACzc,IAAI,CAACmH,KAAK,CAAC6V,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,GAAGP,EAAE,GAAGS,SAAS,GAAGT,EAAE,GAAGS,SAAS,CAAA;EAC3E,GAAA;IACA,OAAOld,IAAI,CAAC0c,GAAG,CAACD,EAAE,EAAE,IAAI,CAACE,GAAG,CAAC,GAAG,CAAC,CAAA;EACrC,CAAC,CAAA;EACD;EACA;EACA;EACA;EACA;EACAH,OAAO,CAAChjB,SAAS,CAAC2jB,KAAK,GAAG,YAAY;IAClC,IAAI,CAACL,QAAQ,GAAG,CAAC,CAAA;EACrB,CAAC,CAAA;EACD;EACA;EACA;EACA;EACA;EACAN,OAAO,CAAChjB,SAAS,CAAC4jB,MAAM,GAAG,UAAUV,GAAG,EAAE;IACtC,IAAI,CAACD,EAAE,GAAGC,GAAG,CAAA;EACjB,CAAC,CAAA;EACD;EACA;EACA;EACA;EACA;EACAF,OAAO,CAAChjB,SAAS,CAAC6jB,MAAM,GAAG,UAAUV,GAAG,EAAE;IACtC,IAAI,CAACA,GAAG,GAAGA,GAAG,CAAA;EAClB,CAAC,CAAA;EACD;EACA;EACA;EACA;EACA;EACAH,OAAO,CAAChjB,SAAS,CAAC8jB,SAAS,GAAG,UAAUT,MAAM,EAAE;IAC5C,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAA;EACxB,CAAC;;EC3DYU,IAAAA,OAAO,0BAAA/Y,QAAA,EAAA;IAAAZ,SAAA,CAAA2Z,OAAA,EAAA/Y,QAAA,CAAA,CAAA;EAAA,EAAA,IAAAX,MAAA,GAAAC,YAAA,CAAAyZ,OAAA,CAAA,CAAA;EAChB,EAAA,SAAAA,OAAYnU,CAAAA,GAAG,EAAE3G,IAAI,EAAE;EAAA,IAAA,IAAAyB,KAAA,CAAA;EAAAC,IAAAA,eAAA,OAAAoZ,OAAA,CAAA,CAAA;EACnB,IAAA,IAAInT,EAAE,CAAA;MACNlG,KAAA,GAAAL,MAAA,CAAAnK,IAAA,CAAA,IAAA,CAAA,CAAA;EACAwK,IAAAA,KAAA,CAAKsZ,IAAI,GAAG,EAAE,CAAA;MACdtZ,KAAA,CAAKiU,IAAI,GAAG,EAAE,CAAA;EACd,IAAA,IAAI/O,GAAG,IAAI,QAAQ,KAAAgH,OAAA,CAAYhH,GAAG,CAAE,EAAA;EAChC3G,MAAAA,IAAI,GAAG2G,GAAG,CAAA;EACVA,MAAAA,GAAG,GAAGjD,SAAS,CAAA;EACnB,KAAA;EACA1D,IAAAA,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;EACjBA,IAAAA,IAAI,CAAC6D,IAAI,GAAG7D,IAAI,CAAC6D,IAAI,IAAI,YAAY,CAAA;MACrCpC,KAAA,CAAKzB,IAAI,GAAGA,IAAI,CAAA;EAChBD,IAAAA,qBAAqB,CAAAoC,sBAAA,CAAAV,KAAA,CAAA,EAAOzB,IAAI,CAAC,CAAA;MACjCyB,KAAA,CAAKuZ,YAAY,CAAChb,IAAI,CAACgb,YAAY,KAAK,KAAK,CAAC,CAAA;MAC9CvZ,KAAA,CAAKwZ,oBAAoB,CAACjb,IAAI,CAACib,oBAAoB,IAAIC,QAAQ,CAAC,CAAA;MAChEzZ,KAAA,CAAK0Z,iBAAiB,CAACnb,IAAI,CAACmb,iBAAiB,IAAI,IAAI,CAAC,CAAA;MACtD1Z,KAAA,CAAK2Z,oBAAoB,CAACpb,IAAI,CAACob,oBAAoB,IAAI,IAAI,CAAC,CAAA;MAC5D3Z,KAAA,CAAK4Z,mBAAmB,CAAC,CAAC1T,EAAE,GAAG3H,IAAI,CAACqb,mBAAmB,MAAM,IAAI,IAAI1T,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG,CAAC,CAAA;EAC9FlG,IAAAA,KAAA,CAAK6Z,OAAO,GAAG,IAAIvB,OAAO,CAAC;EACvBE,MAAAA,GAAG,EAAExY,KAAA,CAAK0Z,iBAAiB,EAAE;EAC7BjB,MAAAA,GAAG,EAAEzY,KAAA,CAAK2Z,oBAAoB,EAAE;EAChChB,MAAAA,MAAM,EAAE3Y,KAAA,CAAK4Z,mBAAmB,EAAC;EACrC,KAAC,CAAC,CAAA;EACF5Z,IAAAA,KAAA,CAAKwG,OAAO,CAAC,IAAI,IAAIjI,IAAI,CAACiI,OAAO,GAAG,KAAK,GAAGjI,IAAI,CAACiI,OAAO,CAAC,CAAA;MACzDxG,KAAA,CAAKmU,WAAW,GAAG,QAAQ,CAAA;MAC3BnU,KAAA,CAAKkF,GAAG,GAAGA,GAAG,CAAA;EACd,IAAA,IAAM4U,OAAO,GAAGvb,IAAI,CAACwb,MAAM,IAAIA,MAAM,CAAA;MACrC/Z,KAAA,CAAKga,OAAO,GAAG,IAAIF,OAAO,CAACrJ,OAAO,EAAE,CAAA;MACpCzQ,KAAA,CAAKia,OAAO,GAAG,IAAIH,OAAO,CAACxI,OAAO,EAAE,CAAA;EACpCtR,IAAAA,KAAA,CAAK+T,YAAY,GAAGxV,IAAI,CAAC2b,WAAW,KAAK,KAAK,CAAA;MAC9C,IAAIla,KAAA,CAAK+T,YAAY,EACjB/T,KAAA,CAAKiB,IAAI,EAAE,CAAA;EAAC,IAAA,OAAAjB,KAAA,CAAA;EACpB,GAAA;EAACE,EAAAA,YAAA,CAAAmZ,OAAA,EAAA,CAAA;MAAArkB,GAAA,EAAA,cAAA;EAAA6L,IAAAA,KAAA,EACD,SAAA0Y,YAAaY,CAAAA,CAAC,EAAE;QACZ,IAAI,CAACxd,SAAS,CAAClF,MAAM,EACjB,OAAO,IAAI,CAAC2iB,aAAa,CAAA;EAC7B,MAAA,IAAI,CAACA,aAAa,GAAG,CAAC,CAACD,CAAC,CAAA;EACxB,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EAAC,GAAA,EAAA;MAAAnlB,GAAA,EAAA,sBAAA;EAAA6L,IAAAA,KAAA,EACD,SAAA2Y,oBAAqBW,CAAAA,CAAC,EAAE;EACpB,MAAA,IAAIA,CAAC,KAAKlY,SAAS,EACf,OAAO,IAAI,CAACoY,qBAAqB,CAAA;QACrC,IAAI,CAACA,qBAAqB,GAAGF,CAAC,CAAA;EAC9B,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EAAC,GAAA,EAAA;MAAAnlB,GAAA,EAAA,mBAAA;EAAA6L,IAAAA,KAAA,EACD,SAAA6Y,iBAAkBS,CAAAA,CAAC,EAAE;EACjB,MAAA,IAAIjU,EAAE,CAAA;EACN,MAAA,IAAIiU,CAAC,KAAKlY,SAAS,EACf,OAAO,IAAI,CAACqY,kBAAkB,CAAA;QAClC,IAAI,CAACA,kBAAkB,GAAGH,CAAC,CAAA;QAC3B,CAACjU,EAAE,GAAG,IAAI,CAAC2T,OAAO,MAAM,IAAI,IAAI3T,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgT,MAAM,CAACiB,CAAC,CAAC,CAAA;EACrE,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EAAC,GAAA,EAAA;MAAAnlB,GAAA,EAAA,qBAAA;EAAA6L,IAAAA,KAAA,EACD,SAAA+Y,mBAAoBO,CAAAA,CAAC,EAAE;EACnB,MAAA,IAAIjU,EAAE,CAAA;EACN,MAAA,IAAIiU,CAAC,KAAKlY,SAAS,EACf,OAAO,IAAI,CAACsY,oBAAoB,CAAA;QACpC,IAAI,CAACA,oBAAoB,GAAGJ,CAAC,CAAA;QAC7B,CAACjU,EAAE,GAAG,IAAI,CAAC2T,OAAO,MAAM,IAAI,IAAI3T,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkT,SAAS,CAACe,CAAC,CAAC,CAAA;EACxE,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EAAC,GAAA,EAAA;MAAAnlB,GAAA,EAAA,sBAAA;EAAA6L,IAAAA,KAAA,EACD,SAAA8Y,oBAAqBQ,CAAAA,CAAC,EAAE;EACpB,MAAA,IAAIjU,EAAE,CAAA;EACN,MAAA,IAAIiU,CAAC,KAAKlY,SAAS,EACf,OAAO,IAAI,CAACuY,qBAAqB,CAAA;QACrC,IAAI,CAACA,qBAAqB,GAAGL,CAAC,CAAA;QAC9B,CAACjU,EAAE,GAAG,IAAI,CAAC2T,OAAO,MAAM,IAAI,IAAI3T,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiT,MAAM,CAACgB,CAAC,CAAC,CAAA;EACrE,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EAAC,GAAA,EAAA;MAAAnlB,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EACD,SAAA2F,OAAQ2T,CAAAA,CAAC,EAAE;QACP,IAAI,CAACxd,SAAS,CAAClF,MAAM,EACjB,OAAO,IAAI,CAACgjB,QAAQ,CAAA;QACxB,IAAI,CAACA,QAAQ,GAAGN,CAAC,CAAA;EACjB,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAnlB,GAAA,EAAA,sBAAA;MAAA6L,KAAA,EAMA,SAAA6Z,oBAAAA,GAAuB;EACnB;EACA,MAAA,IAAI,CAAC,IAAI,CAACC,aAAa,IACnB,IAAI,CAACP,aAAa,IAClB,IAAI,CAACP,OAAO,CAACjB,QAAQ,KAAK,CAAC,EAAE;EAC7B;UACA,IAAI,CAACgC,SAAS,EAAE,CAAA;EACpB,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EACA;EANI,GAAA,EAAA;MAAA5lB,GAAA,EAAA,MAAA;EAAA6L,IAAAA,KAAA,EAOA,SAAAI,IAAK3E,CAAAA,EAAE,EAAE;EAAA,MAAA,IAAAkE,MAAA,GAAA,IAAA,CAAA;QACL,IAAI,CAAC,IAAI,CAAC2T,WAAW,CAAC5R,OAAO,CAAC,MAAM,CAAC,EACjC,OAAO,IAAI,CAAA;EACf,MAAA,IAAI,CAACsS,MAAM,GAAG,IAAIgG,QAAM,CAAC,IAAI,CAAC3V,GAAG,EAAE,IAAI,CAAC3G,IAAI,CAAC,CAAA;EAC7C,MAAA,IAAMqC,MAAM,GAAG,IAAI,CAACiU,MAAM,CAAA;QAC1B,IAAMrX,IAAI,GAAG,IAAI,CAAA;QACjB,IAAI,CAAC2W,WAAW,GAAG,SAAS,CAAA;QAC5B,IAAI,CAAC2G,aAAa,GAAG,KAAK,CAAA;EAC1B;QACA,IAAMC,cAAc,GAAG5e,EAAE,CAACyE,MAAM,EAAE,MAAM,EAAE,YAAY;UAClDpD,IAAI,CAACkL,MAAM,EAAE,CAAA;UACbpM,EAAE,IAAIA,EAAE,EAAE,CAAA;EACd,OAAC,CAAC,CAAA;EACF,MAAA,IAAMwE,OAAO,GAAG,SAAVA,OAAOA,CAAIwC,GAAG,EAAK;UACrB9C,MAAI,CAACyG,OAAO,EAAE,CAAA;UACdzG,MAAI,CAAC2T,WAAW,GAAG,QAAQ,CAAA;EAC3B3T,QAAAA,MAAI,CAACpD,YAAY,CAAC,OAAO,EAAEkG,GAAG,CAAC,CAAA;EAC/B,QAAA,IAAIhH,EAAE,EAAE;YACJA,EAAE,CAACgH,GAAG,CAAC,CAAA;EACX,SAAC,MACI;EACD;YACA9C,MAAI,CAACka,oBAAoB,EAAE,CAAA;EAC/B,SAAA;SACH,CAAA;EACD;QACA,IAAMM,QAAQ,GAAG7e,EAAE,CAACyE,MAAM,EAAE,OAAO,EAAEE,OAAO,CAAC,CAAA;EAC7C,MAAA,IAAI,KAAK,KAAK,IAAI,CAAC2Z,QAAQ,EAAE;EACzB,QAAA,IAAMjU,OAAO,GAAG,IAAI,CAACiU,QAAQ,CAAA;EAC7B;EACA,QAAA,IAAMxF,KAAK,GAAG,IAAI,CAACxW,YAAY,CAAC,YAAM;EAClCsc,UAAAA,cAAc,EAAE,CAAA;EAChBja,UAAAA,OAAO,CAAC,IAAIV,KAAK,CAAC,SAAS,CAAC,CAAC,CAAA;YAC7BQ,MAAM,CAACQ,KAAK,EAAE,CAAA;WACjB,EAAEoF,OAAO,CAAC,CAAA;EACX,QAAA,IAAI,IAAI,CAACjI,IAAI,CAACoK,SAAS,EAAE;YACrBsM,KAAK,CAACpM,KAAK,EAAE,CAAA;EACjB,SAAA;EACA,QAAA,IAAI,CAACoL,IAAI,CAACta,IAAI,CAAC,YAAM;EACjB6G,UAAAA,MAAI,CAAC7B,cAAc,CAACsW,KAAK,CAAC,CAAA;EAC9B,SAAC,CAAC,CAAA;EACN,OAAA;EACA,MAAA,IAAI,CAAChB,IAAI,CAACta,IAAI,CAACohB,cAAc,CAAC,CAAA;EAC9B,MAAA,IAAI,CAAC9G,IAAI,CAACta,IAAI,CAACqhB,QAAQ,CAAC,CAAA;EACxB,MAAA,OAAO,IAAI,CAAA;EACf,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAhmB,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAiS,OAAQxW,CAAAA,EAAE,EAAE;EACR,MAAA,OAAO,IAAI,CAAC2E,IAAI,CAAC3E,EAAE,CAAC,CAAA;EACxB,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAtH,GAAA,EAAA,QAAA;MAAA6L,KAAA,EAKA,SAAA6H,MAAAA,GAAS;EACL;QACA,IAAI,CAACzB,OAAO,EAAE,CAAA;EACd;QACA,IAAI,CAACkN,WAAW,GAAG,MAAM,CAAA;EACzB,MAAA,IAAI,CAAC/W,YAAY,CAAC,MAAM,CAAC,CAAA;EACzB;EACA,MAAA,IAAMwD,MAAM,GAAG,IAAI,CAACiU,MAAM,CAAA;EAC1B,MAAA,IAAI,CAACZ,IAAI,CAACta,IAAI,CAACwC,EAAE,CAACyE,MAAM,EAAE,MAAM,EAAE,IAAI,CAACqa,MAAM,CAACvc,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEvC,EAAE,CAACyE,MAAM,EAAE,MAAM,EAAE,IAAI,CAACsa,MAAM,CAACxc,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEvC,EAAE,CAACyE,MAAM,EAAE,OAAO,EAAE,IAAI,CAACsI,OAAO,CAACxK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEvC,EAAE,CAACyE,MAAM,EAAE,OAAO,EAAE,IAAI,CAACkI,OAAO,CAACpK,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEvC,EAAE,CAAC,IAAI,CAAC8d,OAAO,EAAE,SAAS,EAAE,IAAI,CAACkB,SAAS,CAACzc,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;EAC9P,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA1J,GAAA,EAAA,QAAA;MAAA6L,KAAA,EAKA,SAAAoa,MAAAA,GAAS;EACL,MAAA,IAAI,CAAC7d,YAAY,CAAC,MAAM,CAAC,CAAA;EAC7B,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAApI,GAAA,EAAA,QAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAqa,MAAO/lB,CAAAA,IAAI,EAAE;QACT,IAAI;EACA,QAAA,IAAI,CAAC8kB,OAAO,CAACzI,GAAG,CAACrc,IAAI,CAAC,CAAA;SACzB,CACD,OAAOuO,CAAC,EAAE;EACN,QAAA,IAAI,CAACoF,OAAO,CAAC,aAAa,EAAEpF,CAAC,CAAC,CAAA;EAClC,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA1O,GAAA,EAAA,WAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAsa,SAAUnkB,CAAAA,MAAM,EAAE;EAAA,MAAA,IAAA8N,MAAA,GAAA,IAAA,CAAA;EACd;EACA0C,MAAAA,QAAQ,CAAC,YAAM;EACX1C,QAAAA,MAAI,CAAC1H,YAAY,CAAC,QAAQ,EAAEpG,MAAM,CAAC,CAAA;EACvC,OAAC,EAAE,IAAI,CAACyH,YAAY,CAAC,CAAA;EACzB,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAzJ,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAqI,OAAQ5F,CAAAA,GAAG,EAAE;EACT,MAAA,IAAI,CAAClG,YAAY,CAAC,OAAO,EAAEkG,GAAG,CAAC,CAAA;EACnC,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAtO,GAAA,EAAA,QAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAD,MAAAA,CAAOoQ,GAAG,EAAEzS,IAAI,EAAE;EACd,MAAA,IAAIqC,MAAM,GAAG,IAAI,CAAC0Y,IAAI,CAACtI,GAAG,CAAC,CAAA;QAC3B,IAAI,CAACpQ,MAAM,EAAE;UACTA,MAAM,GAAG,IAAIoL,MAAM,CAAC,IAAI,EAAEgF,GAAG,EAAEzS,IAAI,CAAC,CAAA;EACpC,QAAA,IAAI,CAAC+a,IAAI,CAACtI,GAAG,CAAC,GAAGpQ,MAAM,CAAA;SAC1B,MACI,IAAI,IAAI,CAACmT,YAAY,IAAI,CAACnT,MAAM,CAACwa,MAAM,EAAE;UAC1Cxa,MAAM,CAACkS,OAAO,EAAE,CAAA;EACpB,OAAA;EACA,MAAA,OAAOlS,MAAM,CAAA;EACjB,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAA5L,GAAA,EAAA,UAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAwa,QAASza,CAAAA,MAAM,EAAE;QACb,IAAM0Y,IAAI,GAAG3kB,MAAM,CAACG,IAAI,CAAC,IAAI,CAACwkB,IAAI,CAAC,CAAA;EACnC,MAAA,KAAA,IAAAgC,EAAA,GAAA,CAAA,EAAAC,KAAA,GAAkBjC,IAAI,EAAAgC,EAAA,GAAAC,KAAA,CAAA9jB,MAAA,EAAA6jB,EAAA,EAAE,EAAA;EAAnB,QAAA,IAAMtK,GAAG,GAAAuK,KAAA,CAAAD,EAAA,CAAA,CAAA;EACV,QAAA,IAAM1a,OAAM,GAAG,IAAI,CAAC0Y,IAAI,CAACtI,GAAG,CAAC,CAAA;UAC7B,IAAIpQ,OAAM,CAACwa,MAAM,EAAE;EACf,UAAA,OAAA;EACJ,SAAA;EACJ,OAAA;QACA,IAAI,CAACI,MAAM,EAAE,CAAA;EACjB,KAAA;EACA;EACJ;EACA;EACA;EACA;EACA;EALI,GAAA,EAAA;MAAAxmB,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAMA,SAAAsV,OAAQnf,CAAAA,MAAM,EAAE;QACZ,IAAMoC,cAAc,GAAG,IAAI,CAAC4gB,OAAO,CAAC3iB,MAAM,CAACL,MAAM,CAAC,CAAA;EAClD,MAAA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,cAAc,CAAC3B,MAAM,EAAED,CAAC,EAAE,EAAE;EAC5C,QAAA,IAAI,CAACqd,MAAM,CAACrT,KAAK,CAACpI,cAAc,CAAC5B,CAAC,CAAC,EAAER,MAAM,CAAC4X,OAAO,CAAC,CAAA;EACxD,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA5Z,GAAA,EAAA,SAAA;MAAA6L,KAAA,EAKA,SAAAoG,OAAAA,GAAU;EACN,MAAA,IAAI,CAACgN,IAAI,CAAClf,OAAO,CAAC,UAAC6d,UAAU,EAAA;UAAA,OAAKA,UAAU,EAAE,CAAA;SAAC,CAAA,CAAA;EAC/C,MAAA,IAAI,CAACqB,IAAI,CAACxc,MAAM,GAAG,CAAC,CAAA;EACpB,MAAA,IAAI,CAACwiB,OAAO,CAAC5H,OAAO,EAAE,CAAA;EAC1B,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAArd,GAAA,EAAA,QAAA;MAAA6L,KAAA,EAKA,SAAA2a,MAAAA,GAAS;QACL,IAAI,CAACV,aAAa,GAAG,IAAI,CAAA;QACzB,IAAI,CAACH,aAAa,GAAG,KAAK,CAAA;EAC1B,MAAA,IAAI,CAAC7R,OAAO,CAAC,cAAc,CAAC,CAAA;QAC5B,IAAI,IAAI,CAAC+L,MAAM,EACX,IAAI,CAACA,MAAM,CAACzT,KAAK,EAAE,CAAA;EAC3B,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAApM,GAAA,EAAA,YAAA;MAAA6L,KAAA,EAKA,SAAAmS,UAAAA,GAAa;EACT,MAAA,OAAO,IAAI,CAACwI,MAAM,EAAE,CAAA;EACxB,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAxmB,GAAA,EAAA,SAAA;EAAA6L,IAAAA,KAAA,EAKA,SAAAiI,OAAAA,CAAQjJ,MAAM,EAAEC,WAAW,EAAE;QACzB,IAAI,CAACmH,OAAO,EAAE,CAAA;EACd,MAAA,IAAI,CAAC4S,OAAO,CAACZ,KAAK,EAAE,CAAA;QACpB,IAAI,CAAC9E,WAAW,GAAG,QAAQ,CAAA;QAC3B,IAAI,CAAC/W,YAAY,CAAC,OAAO,EAAEyC,MAAM,EAAEC,WAAW,CAAC,CAAA;QAC/C,IAAI,IAAI,CAACsa,aAAa,IAAI,CAAC,IAAI,CAACU,aAAa,EAAE;UAC3C,IAAI,CAACF,SAAS,EAAE,CAAA;EACpB,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAA5lB,GAAA,EAAA,WAAA;MAAA6L,KAAA,EAKA,SAAA+Z,SAAAA,GAAY;EAAA,MAAA,IAAA7V,MAAA,GAAA,IAAA,CAAA;QACR,IAAI,IAAI,CAAC4V,aAAa,IAAI,IAAI,CAACG,aAAa,EACxC,OAAO,IAAI,CAAA;QACf,IAAMtd,IAAI,GAAG,IAAI,CAAA;QACjB,IAAI,IAAI,CAACqc,OAAO,CAACjB,QAAQ,IAAI,IAAI,CAACyB,qBAAqB,EAAE;EACrD,QAAA,IAAI,CAACR,OAAO,CAACZ,KAAK,EAAE,CAAA;EACpB,QAAA,IAAI,CAAC7b,YAAY,CAAC,kBAAkB,CAAC,CAAA;UACrC,IAAI,CAACud,aAAa,GAAG,KAAK,CAAA;EAC9B,OAAC,MACI;UACD,IAAMc,KAAK,GAAG,IAAI,CAAC5B,OAAO,CAAChB,QAAQ,EAAE,CAAA;UACrC,IAAI,CAAC8B,aAAa,GAAG,IAAI,CAAA;EACzB,QAAA,IAAM1F,KAAK,GAAG,IAAI,CAACxW,YAAY,CAAC,YAAM;YAClC,IAAIjB,IAAI,CAACsd,aAAa,EAClB,OAAA;YACJ/V,MAAI,CAAC3H,YAAY,CAAC,mBAAmB,EAAEI,IAAI,CAACqc,OAAO,CAACjB,QAAQ,CAAC,CAAA;EAC7D;YACA,IAAIpb,IAAI,CAACsd,aAAa,EAClB,OAAA;EACJtd,UAAAA,IAAI,CAACyD,IAAI,CAAC,UAACqC,GAAG,EAAK;EACf,YAAA,IAAIA,GAAG,EAAE;gBACL9F,IAAI,CAACmd,aAAa,GAAG,KAAK,CAAA;gBAC1Bnd,IAAI,CAACod,SAAS,EAAE,CAAA;EAChB7V,cAAAA,MAAI,CAAC3H,YAAY,CAAC,iBAAiB,EAAEkG,GAAG,CAAC,CAAA;EAC7C,aAAC,MACI;gBACD9F,IAAI,CAACke,WAAW,EAAE,CAAA;EACtB,aAAA;EACJ,WAAC,CAAC,CAAA;WACL,EAAED,KAAK,CAAC,CAAA;EACT,QAAA,IAAI,IAAI,CAACld,IAAI,CAACoK,SAAS,EAAE;YACrBsM,KAAK,CAACpM,KAAK,EAAE,CAAA;EACjB,SAAA;EACA,QAAA,IAAI,CAACoL,IAAI,CAACta,IAAI,CAAC,YAAM;EACjBoL,UAAAA,MAAI,CAACpG,cAAc,CAACsW,KAAK,CAAC,CAAA;EAC9B,SAAC,CAAC,CAAA;EACN,OAAA;EACJ,KAAA;EACA;EACJ;EACA;EACA;EACA;EAJI,GAAA,EAAA;MAAAjgB,GAAA,EAAA,aAAA;MAAA6L,KAAA,EAKA,SAAA6a,WAAAA,GAAc;EACV,MAAA,IAAMC,OAAO,GAAG,IAAI,CAAC9B,OAAO,CAACjB,QAAQ,CAAA;QACrC,IAAI,CAAC+B,aAAa,GAAG,KAAK,CAAA;EAC1B,MAAA,IAAI,CAACd,OAAO,CAACZ,KAAK,EAAE,CAAA;EACpB,MAAA,IAAI,CAAC7b,YAAY,CAAC,WAAW,EAAEue,OAAO,CAAC,CAAA;EAC3C,KAAA;EAAC,GAAA,CAAA,CAAA,CAAA;EAAA,EAAA,OAAAtC,OAAA,CAAA;EAAA,CAAA,CA9VwBpd,OAAO,CAAA;;ECHpC;EACA;EACA;EACA,IAAM2f,KAAK,GAAG,EAAE,CAAA;EAChB,SAASrkB,MAAMA,CAAC2N,GAAG,EAAE3G,IAAI,EAAE;EACvB,EAAA,IAAI2N,OAAA,CAAOhH,GAAG,CAAA,KAAK,QAAQ,EAAE;EACzB3G,IAAAA,IAAI,GAAG2G,GAAG,CAAA;EACVA,IAAAA,GAAG,GAAGjD,SAAS,CAAA;EACnB,GAAA;EACA1D,EAAAA,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;IACjB,IAAMsd,MAAM,GAAG5M,GAAG,CAAC/J,GAAG,EAAE3G,IAAI,CAAC6D,IAAI,IAAI,YAAY,CAAC,CAAA;EAClD,EAAA,IAAMiJ,MAAM,GAAGwQ,MAAM,CAACxQ,MAAM,CAAA;EAC5B,EAAA,IAAMuB,EAAE,GAAGiP,MAAM,CAACjP,EAAE,CAAA;EACpB,EAAA,IAAMxK,IAAI,GAAGyZ,MAAM,CAACzZ,IAAI,CAAA;EACxB,EAAA,IAAMqU,aAAa,GAAGmF,KAAK,CAAChP,EAAE,CAAC,IAAIxK,IAAI,IAAIwZ,KAAK,CAAChP,EAAE,CAAC,CAAC,MAAM,CAAC,CAAA;EAC5D,EAAA,IAAMkP,aAAa,GAAGvd,IAAI,CAACwd,QAAQ,IAC/Bxd,IAAI,CAAC,sBAAsB,CAAC,IAC5B,KAAK,KAAKA,IAAI,CAACyd,SAAS,IACxBvF,aAAa,CAAA;EACjB,EAAA,IAAItD,EAAE,CAAA;EACN,EAAA,IAAI2I,aAAa,EAAE;EACf3I,IAAAA,EAAE,GAAG,IAAIkG,OAAO,CAAChO,MAAM,EAAE9M,IAAI,CAAC,CAAA;EAClC,GAAC,MACI;EACD,IAAA,IAAI,CAACqd,KAAK,CAAChP,EAAE,CAAC,EAAE;QACZgP,KAAK,CAAChP,EAAE,CAAC,GAAG,IAAIyM,OAAO,CAAChO,MAAM,EAAE9M,IAAI,CAAC,CAAA;EACzC,KAAA;EACA4U,IAAAA,EAAE,GAAGyI,KAAK,CAAChP,EAAE,CAAC,CAAA;EAClB,GAAA;IACA,IAAIiP,MAAM,CAAClb,KAAK,IAAI,CAACpC,IAAI,CAACoC,KAAK,EAAE;EAC7BpC,IAAAA,IAAI,CAACoC,KAAK,GAAGkb,MAAM,CAACnQ,QAAQ,CAAA;EAChC,GAAA;IACA,OAAOyH,EAAE,CAACvS,MAAM,CAACib,MAAM,CAACzZ,IAAI,EAAE7D,IAAI,CAAC,CAAA;EACvC,CAAA;EACA;EACA;EACAiH,QAAA,CAAcjO,MAAM,EAAE;EAClB8hB,EAAAA,OAAO,EAAPA,OAAO;EACPrN,EAAAA,MAAM,EAANA,MAAM;EACNmH,EAAAA,EAAE,EAAE5b,MAAM;EACVub,EAAAA,OAAO,EAAEvb,MAAAA;EACb,CAAC,CAAC;;;;;;;;"}