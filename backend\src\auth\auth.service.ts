import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { OAuth2Client } from 'google-auth-library';
import { UserService } from '../user/user.service';
import axios from 'axios';
import { AuthProvider, SignInDto } from './dto/signin.dto';
import { AuthProfileResponseDto } from './dto/auth-profile.response.dto';
import { SignupDto } from './dto/signup.dto';
import { UserStatus } from 'src/user/schemas/user.schema';
import { SignedDto } from './dto/signed.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { OtpSession, OtpSessionDocument } from './schemas/otp-session.schema';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { UpdateProfileDto } from './dto/update-profile.dto';

interface OtpPayload {
  email?: string;
  phone?: string;
  gateway_key: string;
  brand: string;
  type: string;
  platform: string;
  user_agent: string;
}

@Injectable()
export class AuthService {
  private logger = new Logger(AuthService.name);
  private readonly fazpassBaseUrl: string;
  private readonly merchantKey: string;
  private readonly smsGatewayKey: string;
  private readonly waGatewayKey: string;
  private readonly emailGatewayKey: string;
  private readonly brandName: string;
  private readonly whitelistedEmailOtps: {
    email: string;
    otp: string;
  }[];

  constructor(
    @InjectModel(OtpSession.name) private otpSessionModel: Model<OtpSessionDocument>,
    private userService: UserService,
    private jwtService: JwtService,
    private configService: ConfigService
  ) {
    this.fazpassBaseUrl = this.configService.get<string>('app.fazpass.baseUrl', '');
    this.merchantKey = this.configService.get<string>('app.fazpass.merchantKey', '');
    this.smsGatewayKey = this.configService.get<string>('app.fazpass.smsGatewayKey', '');
    this.waGatewayKey = this.configService.get<string>('app.fazpass.waGatewayKey', '');
    this.emailGatewayKey = this.configService.get<string>('app.fazpass.emailGatewayKey', '');
    this.brandName = this.configService.get<string>('app.fazpass.brandName', '');
    
    this.whitelistedEmailOtps = [
      {
        email: this.configService.get<string>('BYPASS_ACCOUNT_EMAIL', ''),
        otp: this.configService.get<string>('BYPASS_ACCOUNT_OTP', '')
      },
      {
        email: '<EMAIL>',
        otp: '123456'
      }
    ];
  }

  /**
   * Create a new OTP session
   * @param otpId 
   * @param provider 
   * @param to 
   * @returns 
   */
  private async createOtpSession(otpId: string, provider: AuthProvider, to: string) {
    const otpSession = new this.otpSessionModel({
      otpId,
      provider,
      to
    });

    return await otpSession.save();
  }

  /**
   * Get the latest OTP session for a user
   * @param provider 
   * @param to 
   * @returns 
   */
  private async getOtpSession(provider: AuthProvider, to: string) {
    return await this.otpSessionModel.findOne({ provider, to }).sort({ createdAt: -1 }).exec();
  }

  /**
   * Delete all OTP sessions for a user
   * @param provider 
   * @param to 
   * @returns 
   */
  private async deleteOtpSession(provider: AuthProvider, to: string) {
    return await this.otpSessionModel.deleteMany({ provider, to }).exec();
  }

  async signin(params: SignInDto) {
    try {
      const user = await this.userService.findByEmailOrPhoneNumber(params.email as string, params.phoneNumber as string);
      
      switch (params.provider) {
        case AuthProvider.EMAIL:
          return this.requestOtp(params.provider, user.email);
        case AuthProvider.WHATSAPP:
          return this.requestOtp(params.provider, user.phoneNumber);
        case AuthProvider.SMS:
          return this.requestOtp(params.provider, user.phoneNumber);
        default:
          throw new BadRequestException('Invalid provider');
      }
    } catch(error) {
      throw new BadRequestException(error.message);
    }
  }

  async signup(params: SignupDto) {
    try {
      // check current user
      const user = await this.userService.checkUserExistsEmailOrPhoneNumber(params.email as string, params.phoneNumber as string);
      
      if (user) {
        throw new BadRequestException('User already exists');
      }

      const createdUser = await this.userService.createUser({
        name: params.name,
        email: params.email ?? '',
        image: undefined,
        phoneNumber: params.phoneNumber ?? '',
        status: UserStatus.ACTIVE,
        verified: false,
        religion: params.religion ?? '',
        gender: params.gender ?? '',
        dateOfBirth: params.dateOfBirth ?? '',
        occupation: params.occupation ?? '',
        isSmoker: params.isSmoker ?? false,
        acceptDifferentReligion: params.acceptDifferentReligion ?? false,
        about: params.about ?? ''
      });

      if (createdUser) {
        switch (params.provider) {
          case AuthProvider.EMAIL:
            return await this.requestOtp(AuthProvider.EMAIL, createdUser.email)
          case AuthProvider.WHATSAPP:
            return await this.requestOtp(AuthProvider.WHATSAPP, createdUser.phoneNumber)
          case AuthProvider.SMS:
            return await this.requestOtp(AuthProvider.SMS, createdUser.phoneNumber)
          default:
            throw new BadRequestException('Invalid provider');
        }
      }
    } catch(error) {
      throw new BadRequestException(`Failed to create user: ${error.message}`);
    }
  }

  async jwtSignin(user: SignedDto) {
    const accessToken = this.jwtService.sign({ sub: user.id });
    const decodedToken = this.jwtService.decode(accessToken);
    
    return {
      accessToken,
      expiredAt: decodedToken.exp,
      issuedAt: decodedToken.iat
    };
  }

  async requestOtp(provider: AuthProvider, to: string) {
    let payload: OtpPayload | null = null;
    let successMessage: string | null = null;

    try {
      if (provider === AuthProvider.EMAIL) {
        successMessage = 'OTP sent to email';
        payload = {
          email: to,
          gateway_key: this.emailGatewayKey,
          brand: this.brandName,
          type: 'login',
          platform: 'web',
          user_agent: 'Mozilla/5.0',
        };
      } else if (provider === AuthProvider.SMS) {
        successMessage = 'OTP sent to phone';
        payload = {
          phone: to,
          gateway_key: this.smsGatewayKey,
          brand: this.brandName,
          type: 'login',
          platform: 'web',
          user_agent: 'Mozilla/5.0',
        };
      } else if (provider === AuthProvider.WHATSAPP) {
        successMessage = 'OTP sent to WhatsApp';
        payload = {
          phone: to,
          gateway_key: this.waGatewayKey,
          brand: this.brandName,
          type: 'login',
          platform: 'web',
          user_agent: 'Mozilla/5.0',
        };
      }

      this.logger.log(`Sending ${provider} OTP with payload:`, payload);

      const requestResponse = await axios.post(
        `${this.fazpassBaseUrl}/v1/otp/generate`,
        payload,
        {
          headers: {
            'Authorization': `Bearer ${this.merchantKey}`,
            'Content-Type': 'application/json',
          },
        },
      );

      this.logger.log('Fazpass response:', requestResponse.data);

      const otpId = requestResponse.data.data.id;

      // save otp session
      await this.createOtpSession(otpId, provider, to);

      return {
        message: successMessage
      };
    } catch (err) {
      this.logger.error(err)
      throw new BadRequestException(err.message);
    }
  }

  async verifyIdToken(idToken: string) {
    const client = new OAuth2Client();
    const ticket = await client.verifyIdToken({
      idToken,
      audience: this.configService.get('google.clientId')
    });

    const payload: any = ticket.getPayload();
    const user = await this.userService.findOneByEmail(payload.email)

    if (user) {
      return this.jwtSignin({
        id: user.id
      })
    } else {
      // get user image from google
      // const image = await this.userService.uploadImageFromUrl(payload.picture);
      
      const createdUser = await this.userService.createUser({
        name: payload.family_name + ' ' + payload.given_name,
        email: payload.email,
        image: undefined, // payload.picture -> should be fetch from google and upload to s3
        phoneNumber: '',
        status: UserStatus.ACTIVE,
        verified: true // for google sign in is already verified
      })

      if (createdUser) {
        return this.jwtSignin({
          id: createdUser.id
        })
      } else {
        throw new BadRequestException('Failed to sync user data')
      }
    }
  }

  async refreshAccessToken(id: string) {
    const user = await this.userService.findOne(id);
    
    return this.jwtSignin({
      id: user.id
    })
  }

  async verifyOtp(params: VerifyOtpDto): Promise<any> {
    if (![AuthProvider.EMAIL, AuthProvider.SMS, AuthProvider.WHATSAPP].includes(params.provider)) {
      throw new BadRequestException('Invalid provider');
    }

    try {
      const user = await this.userService.findByEmailOrPhoneNumber(params.email as string, params.phoneNumber as string);

      if (this.whitelistedEmailOtps.some(otp => otp.email === user.email && otp.otp === params.code)) {
        return this.jwtSignin({
          id: user.id
        })
      }
      const otpSession = await this.getOtpSession(params.provider, user.email);
      
      if (!otpSession) {
        throw new BadRequestException('OTP session not found. Please request OTP first.');
      }
      
      if (user.status !== UserStatus.ACTIVE) {
        throw new BadRequestException('User is inactive');
      }

      const response = await axios.post(
        `${this.fazpassBaseUrl}/v1/otp/verify`,
        {
          otp_id: otpSession.otpId,
          otp: params.code,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.merchantKey}`,
            'Content-Type': 'application/json',
          },
        },
      );

      this.logger.log('Fazpass verify response:', response.data);

      if (params.provider === AuthProvider.EMAIL && params.email) {
        await this.deleteOtpSession(params.provider, user.email);
      } else {
        await this.deleteOtpSession(params.provider, user.phoneNumber);
      }

      // Update user's verified status using userService
      await this.userService.verifyUser(user.id);

      return this.jwtSignin({
        id: user.id
      });
    } catch (error) {
      this.logger.error('Error verifying OTP:', error.response?.data || error.message);
      throw new BadRequestException('Failed to verify OTP');
    }
  }

  async getProfile(userId: string): Promise<AuthProfileResponseDto> {
    const user = await this.userService.findOne(userId);

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      image: user.image?.url || '',
      phoneNumber: user.phoneNumber,
      dateOfBirth: user.dateOfBirth,
      photos: user.photos.map(photo => ({
        id: photo.id,
        url: photo.url,
        key: photo.key
      })),
      gender: user.gender,
      religion: user.religion,
      occupation: user.occupation,
      isSmoker: user.isSmoker,
      acceptDifferentReligion: user.acceptDifferentReligion,
      about: user.about,
      psychTestCompleted: user.psychTestCompleted,
      status: user.status,
    }
  }

  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<AuthProfileResponseDto> {
    const user = await this.userService.update(userId, updateProfileDto);

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      image: user.image?.url || '',
      phoneNumber: user.phoneNumber,
      dateOfBirth: user.dateOfBirth,
      photos: user.photos.map(photo => ({
        id: photo.id,
        url: photo.url,
        key: photo.key
      })),
      gender: user.gender,
      religion: user.religion,
      occupation: user.occupation,
      isSmoker: user.isSmoker,
      acceptDifferentReligion: user.acceptDifferentReligion,
      about: user.about,
      psychTestCompleted: user.psychTestCompleted,
      status: user.status,
    }
  }
}
