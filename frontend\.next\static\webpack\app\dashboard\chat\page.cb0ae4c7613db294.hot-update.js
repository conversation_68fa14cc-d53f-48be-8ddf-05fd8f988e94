"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/chat/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/chat/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/dashboard/chat/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatPage() {\n    _s();\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [selectedChat, setSelectedChat] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [messagesLoading, setMessagesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [currentUserId, setCurrentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            fetchChats();\n            getCurrentUserId();\n            return ({\n                \"ChatPage.useEffect\": ()=>{\n                    if (socket) {\n                        console.log('Cleaning up socket connection');\n                        socket.disconnect();\n                    }\n                }\n            })[\"ChatPage.useEffect\"];\n        }\n    }[\"ChatPage.useEffect\"], []);\n    // Auto-select first chat when chats are loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            if (chats.length > 0 && !selectedChat) {\n                console.log('Auto-selecting first chat:', chats[0]);\n                handleChatSelect(chats[0]);\n            }\n        }\n    }[\"ChatPage.useEffect\"], [\n        chats,\n        selectedChat\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatPage.useEffect\"], [\n        messages\n    ]);\n    const getCurrentUserId = async ()=>{\n        try {\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) return;\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/auth/profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const profile = await response.json();\n                setCurrentUserId(profile.id);\n            }\n        } catch (err) {\n            console.error('Failed to get current user ID:', err);\n        }\n    };\n    const fetchChats = async ()=>{\n        try {\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) {\n                router.replace('/login');\n                return;\n            }\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/chats\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                if (response.status === 401) {\n                    localStorage.removeItem('pairsona_token');\n                    router.replace('/login');\n                    return;\n                }\n                throw new Error('Failed to fetch chats');\n            }\n            const chatsData = await response.json();\n            console.log('Fetched chats:', chatsData);\n            setChats(chatsData);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load chats');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchMessages = async (chatId)=>{\n        try {\n            setMessagesLoading(true);\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) return;\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/chats/\").concat(chatId, \"/messages?page=1&limit=50\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch messages');\n            }\n            const messagesData = await response.json();\n            setMessages(messagesData.data.reverse()); // Reverse to show oldest first\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load messages');\n        } finally{\n            setMessagesLoading(false);\n        }\n    };\n    const connectToChat = (chatOrMatchId)=>{\n        const token = localStorage.getItem('pairsona_token');\n        if (!token) return;\n        // Disconnect existing socket\n        if (socket) {\n            socket.disconnect();\n        }\n        console.log('🚀 Connecting to chat with ID:', chatOrMatchId);\n        console.log('🌐 API Base URL:', \"https://api.pairsona.id\");\n        // Use the exact format from backend documentation\n        const socketUrl = \"\".concat(\"https://api.pairsona.id\", \"/chat\");\n        console.log('🌐 Socket URL:', socketUrl);\n        // Follow EXACT backend documentation format\n        const config = {\n            transports: [\n                'websocket'\n            ],\n            auth: {\n                token: \"Bearer \".concat(token) // Backend expects Bearer prefix in auth.token\n            },\n            query: {\n                chatId: chatOrMatchId // Use chatId as specified in docs\n            }\n        };\n        console.log('📋 Socket.IO config:', JSON.stringify(config, null, 2));\n        const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_7__.io)(socketUrl, config);\n        newSocket.on('connect', ()=>{\n            console.log('✅ Connected to chat server');\n            console.log('Socket ID:', newSocket.id);\n        });\n        newSocket.on('message', (message)=>{\n            console.log('📨 New message received:', message);\n            setMessages((prev)=>[\n                    ...prev,\n                    message\n                ]);\n            // Update last message in chats list\n            setChats((prev)=>prev.map((chat)=>chat.id === message.chatId ? {\n                        ...chat,\n                        lastMessage: {\n                            id: message.id,\n                            senderId: message.senderId,\n                            message: message.message,\n                            sentAt: message.sentAt\n                        }\n                    } : chat));\n        });\n        newSocket.on('disconnect', (reason)=>{\n            console.log('❌ Disconnected from chat server, reason:', reason);\n        });\n        newSocket.on('connect_error', (error)=>{\n            console.error('🚫 Socket connection error:', error);\n            console.error('Error details:', error.message);\n            setError('Failed to connect to chat server. Please try again.');\n        });\n        newSocket.on('error', (error)=>{\n            console.error('⚠️ Socket error:', error);\n            setError('Connection error. Please try again.');\n        });\n        setSocket(newSocket);\n    };\n    const handleChatSelect = (chat)=>{\n        console.log('Selecting chat:', chat);\n        setSelectedChat(chat);\n        setMessages([]);\n        fetchMessages(chat.id);\n        // Try with matchId first (based on authentication error)\n        console.log('🔄 Trying connection with matchId:', chat.matchId);\n        connectToChat(chat.matchId);\n    };\n    const sendMessage = async ()=>{\n        if (!newMessage.trim() || !selectedChat || !socket || sending) return;\n        console.log('Sending message:', newMessage.trim());\n        console.log('Socket connected:', socket.connected);\n        console.log('Selected chat:', selectedChat.id);\n        try {\n            setSending(true);\n            if (!socket.connected) {\n                console.error('Socket not connected, attempting to reconnect...');\n                connectToChat(selectedChat.id);\n                setError('Connection lost. Please try again.');\n                return;\n            }\n            socket.emit('sendMessage', {\n                message: newMessage.trim()\n            });\n            console.log('Message emitted successfully');\n            setNewMessage('');\n        } catch (err) {\n            console.error('Error sending message:', err);\n            setError('Failed to send message');\n        } finally{\n            setSending(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const formatTime = (dateString)=>{\n        return new Date(dateString).toLocaleTimeString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    const formatLastMessageTime = (dateString)=>{\n        const now = new Date();\n        const messageDate = new Date(dateString);\n        const diffInHours = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return 'Now';\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n        return messageDate.toLocaleDateString();\n    };\n    const getInitials = (name)=>{\n        return name.split(' ').map((n)=>n[0]).join('').toUpperCase();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Chat\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Loading your conversations...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"md:col-span-1 h-[calc(100vh-200px)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2 text-[#D0544D]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Conversations\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: Array.from({\n                                            length: 3\n                                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 rounded-full bg-gray-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, i, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"md:col-span-2 h-[calc(100vh-200px)]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-8 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-8 h-8 animate-spin text-[#D0544D]\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Chat\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Connect with your matches.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 mb-4\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchChats,\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n            lineNumber: 361,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Chat\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Connect with your matches.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"md:col-span-1 h-[calc(100vh-200px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-[#D0544D]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Conversations\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-0\",\n                                children: chats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No conversations yet\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Start connecting with your matches to begin chatting!\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divide-y\",\n                                    children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 cursor-pointer hover:bg-[#F2E7DB]/50 transition-colors \".concat((selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id) === chat.id ? 'bg-[#F2E7DB]/30' : ''),\n                                            onClick: ()=>handleChatSelect(chat),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                                className: \"w-10 h-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                                        src: chat.user.image || undefined,\n                                                                        alt: chat.user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                        className: \"bg-[#D0544D]/20 text-[#D0544D]\",\n                                                                        children: getInitials(chat.user.name)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            chat.user.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium truncate\",\n                                                                children: chat.user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground truncate\",\n                                                                children: chat.lastMessage ? chat.lastMessage.message : 'No messages yet'\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: chat.lastMessage ? formatLastMessageTime(chat.lastMessage.sentAt) : ''\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, chat.id, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"md:col-span-2 h-[calc(100vh-200px)] flex flex-col\",\n                        children: selectedChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                    className: \"border-b\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mr-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                            className: \"w-8 h-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                                    src: selectedChat.user.image || undefined,\n                                                                    alt: selectedChat.user.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                    className: \"bg-[#D0544D]/20 text-[#D0544D]\",\n                                                                    children: getInitials(selectedChat.user.name)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedChat.user.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: selectedChat.user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: selectedChat.user.isOnline ? 'Online' : 'Offline'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"flex-1 overflow-auto p-4\",\n                                    children: messagesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 animate-spin text-[#D0544D]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                        children: \"No messages yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"Start the conversation by sending a message!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 23\n                                            }, this) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(message.senderId === currentUserId ? 'justify-end' : 'justify-start'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg p-3 max-w-[80%] \".concat(message.senderId === currentUserId ? 'bg-[#D0544D] text-white' : 'bg-[#F2E7DB]'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: message.message\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1 \".concat(message.senderId === currentUserId ? 'text-white/70' : 'text-muted-foreground'),\n                                                                children: formatTime(message.sentAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, message.id, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 25\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: messagesEndRef\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                type: \"text\",\n                                                placeholder: \"Type a message...\",\n                                                value: newMessage,\n                                                onChange: (e)=>setNewMessage(e.target.value),\n                                                onKeyDown: handleKeyDown,\n                                                disabled: sending,\n                                                className: \"flex-1 focus:ring-[#D0544D] focus:border-[#D0544D]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: sendMessage,\n                                                disabled: !newMessage.trim() || sending,\n                                                className: \"bg-[#D0544D] hover:bg-[#D0544D]/90 text-white\",\n                                                children: sending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"flex-1 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"mx-auto h-16 w-16 text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-medium text-gray-900 mb-2\",\n                                        children: \"Select a conversation\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Choose a conversation from the sidebar to start chatting\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                lineNumber: 384,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n        lineNumber: 378,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"S6ZjTg6MVYCshZyj22E3f9HdYt8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2NoYXQvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0Y7QUFDaEM7QUFDRjtBQUMrQjtBQUVHO0FBQzVCO0FBQ1I7QUFDRTtBQXVDL0IsU0FBU21COztJQUN0QixNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR1AsK0NBQVFBLENBQVMsRUFBRTtJQUM3QyxNQUFNLENBQUNRLGNBQWNDLGdCQUFnQixHQUFHVCwrQ0FBUUEsQ0FBYztJQUM5RCxNQUFNLENBQUNVLFVBQVVDLFlBQVksR0FBR1gsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUNZLFlBQVlDLGNBQWMsR0FBR2IsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDYyxTQUFTQyxXQUFXLEdBQUdmLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2dCLGlCQUFpQkMsbUJBQW1CLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNrQixTQUFTQyxXQUFXLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNvQixPQUFPQyxTQUFTLEdBQUdyQiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDc0IsZUFBZUMsaUJBQWlCLEdBQUd2QiwrQ0FBUUEsQ0FBZ0I7SUFDbEUsTUFBTSxDQUFDd0IsUUFBUUMsVUFBVSxHQUFHekIsK0NBQVFBLENBQWdCO0lBRXBELE1BQU0wQixpQkFBaUJ4Qiw2Q0FBTUEsQ0FBaUI7SUFDOUMsTUFBTXlCLFNBQVN4QiwwREFBU0E7SUFFeEJGLGdEQUFTQTs4QkFBQztZQUNSMkI7WUFDQUM7WUFFQTtzQ0FBTztvQkFDTCxJQUFJTCxRQUFRO3dCQUNWTSxRQUFRQyxHQUFHLENBQUM7d0JBQ1pQLE9BQU9RLFVBQVU7b0JBQ25CO2dCQUNGOztRQUNGOzZCQUFHLEVBQUU7SUFFTCwrQ0FBK0M7SUFDL0MvQixnREFBU0E7OEJBQUM7WUFDUixJQUFJSyxNQUFNMkIsTUFBTSxHQUFHLEtBQUssQ0FBQ3pCLGNBQWM7Z0JBQ3JDc0IsUUFBUUMsR0FBRyxDQUFDLDhCQUE4QnpCLEtBQUssQ0FBQyxFQUFFO2dCQUNsRDRCLGlCQUFpQjVCLEtBQUssQ0FBQyxFQUFFO1lBQzNCO1FBQ0Y7NkJBQUc7UUFBQ0E7UUFBT0U7S0FBYTtJQUV4QlAsZ0RBQVNBOzhCQUFDO1lBQ1JrQztRQUNGOzZCQUFHO1FBQUN6QjtLQUFTO0lBRWIsTUFBTW1CLG1CQUFtQjtRQUN2QixJQUFJO1lBQ0YsTUFBTU8sUUFBUUMsYUFBYUMsT0FBTyxDQUFDO1lBQ25DLElBQUksQ0FBQ0YsT0FBTztZQUVaLE1BQU1HLFdBQVcsTUFBTUMsTUFBTSxHQUF3QyxPQUFyQ0MseUJBQW9DLEVBQUMsa0JBQWdCO2dCQUNuRkcsU0FBUztvQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTlI7Z0JBQzdCO1lBQ0Y7WUFFQSxJQUFJRyxTQUFTTSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsVUFBVSxNQUFNUCxTQUFTUSxJQUFJO2dCQUNuQ3hCLGlCQUFpQnVCLFFBQVFFLEVBQUU7WUFDN0I7UUFDRixFQUFFLE9BQU9DLEtBQUs7WUFDWm5CLFFBQVFWLEtBQUssQ0FBQyxrQ0FBa0M2QjtRQUNsRDtJQUNGO0lBRUEsTUFBTXJCLGFBQWE7UUFDakIsSUFBSTtZQUNGLE1BQU1RLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUNuQyxJQUFJLENBQUNGLE9BQU87Z0JBQ1ZULE9BQU91QixPQUFPLENBQUM7Z0JBQ2Y7WUFDRjtZQUVBLE1BQU1YLFdBQVcsTUFBTUMsTUFBTSxHQUF3QyxPQUFyQ0MseUJBQW9DLEVBQUMsV0FBUztnQkFDNUVHLFNBQVM7b0JBQ1AsaUJBQWlCLFVBQWdCLE9BQU5SO2dCQUM3QjtZQUNGO1lBRUEsSUFBSSxDQUFDRyxTQUFTTSxFQUFFLEVBQUU7Z0JBQ2hCLElBQUlOLFNBQVNZLE1BQU0sS0FBSyxLQUFLO29CQUMzQmQsYUFBYWUsVUFBVSxDQUFDO29CQUN4QnpCLE9BQU91QixPQUFPLENBQUM7b0JBQ2Y7Z0JBQ0Y7Z0JBQ0EsTUFBTSxJQUFJRyxNQUFNO1lBQ2xCO1lBRUEsTUFBTUMsWUFBWSxNQUFNZixTQUFTUSxJQUFJO1lBQ3JDakIsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQnVCO1lBQzlCL0MsU0FBUytDO1FBRVgsRUFBRSxPQUFPTCxLQUFLO1lBQ1o1QixTQUFTNEIsZUFBZUksUUFBUUosSUFBSU0sT0FBTyxHQUFHO1FBQ2hELFNBQVU7WUFDUnhDLFdBQVc7UUFDYjtJQUNGO0lBQ0EsTUFBTXlDLGdCQUFnQixPQUFPQztRQUMzQixJQUFJO1lBQ0Z4QyxtQkFBbUI7WUFDbkIsTUFBTW1CLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUNuQyxJQUFJLENBQUNGLE9BQU87WUFFWixNQUFNRyxXQUFXLE1BQU1DLE1BQU0sR0FBaURpQixPQUE5Q2hCLHlCQUFvQyxFQUFDLFdBQWdCLE9BQVBnQixRQUFPLDhCQUE0QjtnQkFDL0diLFNBQVM7b0JBQ1AsaUJBQWlCLFVBQWdCLE9BQU5SO2dCQUM3QjtZQUNGO1lBRUEsSUFBSSxDQUFDRyxTQUFTTSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSVEsTUFBTTtZQUNsQjtZQUVBLE1BQU1LLGVBQWlDLE1BQU1uQixTQUFTUSxJQUFJO1lBQzFEcEMsWUFBWStDLGFBQWFDLElBQUksQ0FBQ0MsT0FBTyxLQUFLLCtCQUErQjtRQUUzRSxFQUFFLE9BQU9YLEtBQUs7WUFDWjVCLFNBQVM0QixlQUFlSSxRQUFRSixJQUFJTSxPQUFPLEdBQUc7UUFDaEQsU0FBVTtZQUNSdEMsbUJBQW1CO1FBQ3JCO0lBQ0Y7SUFFQSxNQUFNNEMsZ0JBQWdCLENBQUNDO1FBQ3JCLE1BQU0xQixRQUFRQyxhQUFhQyxPQUFPLENBQUM7UUFDbkMsSUFBSSxDQUFDRixPQUFPO1FBRVosNkJBQTZCO1FBQzdCLElBQUlaLFFBQVE7WUFDVkEsT0FBT1EsVUFBVTtRQUNuQjtRQUVBRixRQUFRQyxHQUFHLENBQUMsa0NBQWtDK0I7UUFDOUNoQyxRQUFRQyxHQUFHLENBQUMsb0JBQW9CVSx5QkFBb0M7UUFFcEUsa0RBQWtEO1FBQ2xELE1BQU1zQixZQUFZLEdBQXdDLE9BQXJDdEIseUJBQW9DLEVBQUM7UUFDMURYLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JnQztRQUU5Qiw0Q0FBNEM7UUFDNUMsTUFBTUMsU0FBUztZQUNiQyxZQUFZO2dCQUFDO2FBQVk7WUFDekJDLE1BQU07Z0JBQ0o5QixPQUFPLFVBQWdCLE9BQU5BLE9BQVEsOENBQThDO1lBQ3pFO1lBQ0ErQixPQUFPO2dCQUNMVixRQUFRSyxjQUFjLGtDQUFrQztZQUMxRDtRQUNGO1FBRUFoQyxRQUFRQyxHQUFHLENBQUMsd0JBQXdCcUMsS0FBS0MsU0FBUyxDQUFDTCxRQUFRLE1BQU07UUFFakUsTUFBTU0sWUFBWWxFLG9EQUFFQSxDQUFDMkQsV0FBV0M7UUFFaENNLFVBQVVDLEVBQUUsQ0FBQyxXQUFXO1lBQ3RCekMsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxjQUFjdUMsVUFBVXRCLEVBQUU7UUFDeEM7UUFFQXNCLFVBQVVDLEVBQUUsQ0FBQyxXQUFXLENBQUNoQjtZQUN2QnpCLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJ3QjtZQUN4QzVDLFlBQVk2RCxDQUFBQSxPQUFRO3VCQUFJQTtvQkFBTWpCO2lCQUFRO1lBRXRDLG9DQUFvQztZQUNwQ2hELFNBQVNpRSxDQUFBQSxPQUFRQSxLQUFLQyxHQUFHLENBQUNDLENBQUFBLE9BQ3hCQSxLQUFLMUIsRUFBRSxLQUFLTyxRQUFRRSxNQUFNLEdBQ3RCO3dCQUNFLEdBQUdpQixJQUFJO3dCQUNQQyxhQUFhOzRCQUNYM0IsSUFBSU8sUUFBUVAsRUFBRTs0QkFDZDRCLFVBQVVyQixRQUFRcUIsUUFBUTs0QkFDMUJyQixTQUFTQSxRQUFRQSxPQUFPOzRCQUN4QnNCLFFBQVF0QixRQUFRc0IsTUFBTTt3QkFDeEI7b0JBQ0YsSUFDQUg7UUFFUjtRQUVBSixVQUFVQyxFQUFFLENBQUMsY0FBYyxDQUFDTztZQUMxQmhELFFBQVFDLEdBQUcsQ0FBQyw0Q0FBNEMrQztRQUMxRDtRQUVBUixVQUFVQyxFQUFFLENBQUMsaUJBQWlCLENBQUNuRDtZQUM3QlUsUUFBUVYsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0NVLFFBQVFWLEtBQUssQ0FBQyxrQkFBa0JBLE1BQU1tQyxPQUFPO1lBQzdDbEMsU0FBUztRQUNYO1FBRUFpRCxVQUFVQyxFQUFFLENBQUMsU0FBUyxDQUFDbkQ7WUFDckJVLFFBQVFWLEtBQUssQ0FBQyxvQkFBb0JBO1lBQ2xDQyxTQUFTO1FBQ1g7UUFFQUksVUFBVTZDO0lBQ1o7SUFFQSxNQUFNcEMsbUJBQW1CLENBQUN3QztRQUN4QjVDLFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUIyQztRQUMvQmpFLGdCQUFnQmlFO1FBQ2hCL0QsWUFBWSxFQUFFO1FBQ2Q2QyxjQUFja0IsS0FBSzFCLEVBQUU7UUFFckIseURBQXlEO1FBQ3pEbEIsUUFBUUMsR0FBRyxDQUFDLHNDQUFzQzJDLEtBQUtLLE9BQU87UUFDOURsQixjQUFjYSxLQUFLSyxPQUFPO0lBQzVCO0lBRUEsTUFBTUMsY0FBYztRQUNsQixJQUFJLENBQUNwRSxXQUFXcUUsSUFBSSxNQUFNLENBQUN6RSxnQkFBZ0IsQ0FBQ2dCLFVBQVVOLFNBQVM7UUFFL0RZLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JuQixXQUFXcUUsSUFBSTtRQUMvQ25ELFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJQLE9BQU8wRCxTQUFTO1FBQ2pEcEQsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQnZCLGFBQWF3QyxFQUFFO1FBRTdDLElBQUk7WUFDRjdCLFdBQVc7WUFFWCxJQUFJLENBQUNLLE9BQU8wRCxTQUFTLEVBQUU7Z0JBQ3JCcEQsUUFBUVYsS0FBSyxDQUFDO2dCQUNkeUMsY0FBY3JELGFBQWF3QyxFQUFFO2dCQUM3QjNCLFNBQVM7Z0JBQ1Q7WUFDRjtZQUVBRyxPQUFPMkQsSUFBSSxDQUFDLGVBQWU7Z0JBQ3pCNUIsU0FBUzNDLFdBQVdxRSxJQUFJO1lBQzFCO1lBRUFuRCxRQUFRQyxHQUFHLENBQUM7WUFDWmxCLGNBQWM7UUFFaEIsRUFBRSxPQUFPb0MsS0FBSztZQUNabkIsUUFBUVYsS0FBSyxDQUFDLDBCQUEwQjZCO1lBQ3hDNUIsU0FBUztRQUNYLFNBQVU7WUFDUkYsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNaUUsZ0JBQWdCLENBQUNDO1FBQ3JCLElBQUlBLEVBQUVDLEdBQUcsS0FBSyxXQUFXLENBQUNELEVBQUVFLFFBQVEsRUFBRTtZQUNwQ0YsRUFBRUcsY0FBYztZQUNoQlI7UUFDRjtJQUNGO0lBRUEsTUFBTTdDLGlCQUFpQjtZQUNyQlQ7U0FBQUEsMEJBQUFBLGVBQWUrRCxPQUFPLGNBQXRCL0QsOENBQUFBLHdCQUF3QmdFLGNBQWMsQ0FBQztZQUFFQyxVQUFVO1FBQVM7SUFDOUQ7SUFFQSxNQUFNQyxhQUFhLENBQUNDO1FBQ2xCLE9BQU8sSUFBSUMsS0FBS0QsWUFBWUUsa0JBQWtCLENBQUMsU0FBUztZQUN0REMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFFBQVE7UUFDVjtJQUNGO0lBRUEsTUFBTUMsd0JBQXdCLENBQUNOO1FBQzdCLE1BQU1PLE1BQU0sSUFBSU47UUFDaEIsTUFBTU8sY0FBYyxJQUFJUCxLQUFLRDtRQUM3QixNQUFNUyxjQUFjQyxLQUFLQyxLQUFLLENBQUMsQ0FBQ0osSUFBSUssT0FBTyxLQUFLSixZQUFZSSxPQUFPLEVBQUMsSUFBTSxRQUFPLEtBQUssRUFBQztRQUV2RixJQUFJSCxjQUFjLEdBQUcsT0FBTztRQUM1QixJQUFJQSxjQUFjLElBQUksT0FBTyxHQUFlLE9BQVpBLGFBQVk7UUFDNUMsT0FBT0QsWUFBWUssa0JBQWtCO0lBQ3ZDO0lBRUEsTUFBTUMsY0FBYyxDQUFDQztRQUNuQixPQUFPQSxLQUFLQyxLQUFLLENBQUMsS0FBS3BDLEdBQUcsQ0FBQ3FDLENBQUFBLElBQUtBLENBQUMsQ0FBQyxFQUFFLEVBQUVDLElBQUksQ0FBQyxJQUFJQyxXQUFXO0lBQzVEO0lBRUEsSUFBSWxHLFNBQVM7UUFDWCxxQkFDRSw4REFBQ21HO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDs7c0NBQ0MsOERBQUNFOzRCQUFHRCxXQUFVO3NDQUFvQzs7Ozs7O3NDQUNsRCw4REFBQ0U7NEJBQUVGLFdBQVU7c0NBQXdCOzs7Ozs7Ozs7Ozs7OEJBRXZDLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNoSSxxREFBSUE7NEJBQUNnSSxXQUFVOzs4Q0FDZCw4REFBQzlILDJEQUFVQTs4Q0FDVCw0RUFBQ0MsMERBQVNBO3dDQUFDNkgsV0FBVTs7MERBQ25CLDhEQUFDdkgsd0hBQWFBO2dEQUFDdUgsV0FBVTs7Ozs7OzRDQUFnQzs7Ozs7Ozs7Ozs7OzhDQUk3RCw4REFBQy9ILDREQUFXQTtvQ0FBQytILFdBQVU7OENBQ3JCLDRFQUFDRDt3Q0FBSUMsV0FBVTtrREFDWkcsTUFBTUMsSUFBSSxDQUFDOzRDQUFFckYsUUFBUTt3Q0FBRSxHQUFHd0MsR0FBRyxDQUFDLENBQUM4QyxHQUFHQyxrQkFDakMsOERBQUNQO2dEQUFZQyxXQUFVOzBEQUNyQiw0RUFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7Ozs7O3NFQUNmLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzs7Ozs7OEVBQ2YsOERBQUNEO29FQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7K0NBTFhNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBYWxCLDhEQUFDdEkscURBQUlBOzRCQUFDZ0ksV0FBVTtzQ0FDZCw0RUFBQy9ILDREQUFXQTtnQ0FBQytILFdBQVU7MENBQ3JCLDRFQUFDckgsd0hBQU9BO29DQUFDcUgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU0vQjtJQUVBLElBQUk5RixPQUFPO1FBQ1QscUJBQ0UsOERBQUM2RjtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7O3NDQUNDLDhEQUFDRTs0QkFBR0QsV0FBVTtzQ0FBb0M7Ozs7OztzQ0FDbEQsOERBQUNFOzRCQUFFRixXQUFVO3NDQUF3Qjs7Ozs7Ozs7Ozs7OzhCQUV2Qyw4REFBQ2hJLHFEQUFJQTtvQkFBQ2dJLFdBQVU7OEJBQ2QsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3BILHlIQUFXQTtnQ0FBQ29ILFdBQVU7Ozs7OzswQ0FDdkIsOERBQUNFO2dDQUFFRixXQUFVOzBDQUFxQjlGOzs7Ozs7MENBQ2xDLDhEQUFDOUIseURBQU1BO2dDQUFDbUksU0FBUzdGOzBDQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUt2QztJQUVBLHFCQUNFLDhEQUFDcUY7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEOztrQ0FDQyw4REFBQ0U7d0JBQUdELFdBQVU7a0NBQW9DOzs7Ozs7a0NBQ2xELDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBd0I7Ozs7Ozs7Ozs7OzswQkFHdkMsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ2hJLHFEQUFJQTt3QkFBQ2dJLFdBQVU7OzBDQUNkLDhEQUFDOUgsMkRBQVVBOzBDQUNULDRFQUFDQywwREFBU0E7b0NBQUM2SCxXQUFVOztzREFDbkIsOERBQUN2SCx3SEFBYUE7NENBQUN1SCxXQUFVOzs7Ozs7d0NBQWdDOzs7Ozs7Ozs7Ozs7MENBSTdELDhEQUFDL0gsNERBQVdBO2dDQUFDK0gsV0FBVTswQ0FDcEI1RyxNQUFNMkIsTUFBTSxLQUFLLGtCQUNoQiw4REFBQ2dGO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ25ILHlIQUFLQTs0Q0FBQ21ILFdBQVU7Ozs7OztzREFDakIsOERBQUNROzRDQUFHUixXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ0U7NENBQUVGLFdBQVU7c0RBQWdCOzs7Ozs7Ozs7Ozt5REFHL0IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaNUcsTUFBTW1FLEdBQUcsQ0FBQyxDQUFDQyxxQkFDViw4REFBQ3VDOzRDQUVDQyxXQUFXLDhEQUVWLE9BREMxRyxDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWN3QyxFQUFFLE1BQUswQixLQUFLMUIsRUFBRSxHQUFHLG9CQUFvQjs0Q0FFckR5RSxTQUFTLElBQU12RixpQkFBaUJ3QztzREFFaEMsNEVBQUN1QztnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQzFILHlEQUFNQTtnRUFBQzBILFdBQVU7O2tGQUNoQiw4REFBQ3hILDhEQUFXQTt3RUFBQ2lJLEtBQUtqRCxLQUFLa0QsSUFBSSxDQUFDQyxLQUFLLElBQUlDO3dFQUFXQyxLQUFLckQsS0FBS2tELElBQUksQ0FBQ2hCLElBQUk7Ozs7OztrRkFDbkUsOERBQUNuSCxpRUFBY0E7d0VBQUN5SCxXQUFVO2tGQUN2QlAsWUFBWWpDLEtBQUtrRCxJQUFJLENBQUNoQixJQUFJOzs7Ozs7Ozs7Ozs7NERBRzlCbEMsS0FBS2tELElBQUksQ0FBQ0ksUUFBUSxrQkFDakIsOERBQUNmO2dFQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7a0VBR25CLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNFO2dFQUFFRixXQUFVOzBFQUF3QnhDLEtBQUtrRCxJQUFJLENBQUNoQixJQUFJOzs7Ozs7MEVBQ25ELDhEQUFDUTtnRUFBRUYsV0FBVTswRUFDVnhDLEtBQUtDLFdBQVcsR0FBR0QsS0FBS0MsV0FBVyxDQUFDcEIsT0FBTyxHQUFHOzs7Ozs7Ozs7Ozs7a0VBR25ELDhEQUFDMEQ7d0RBQUlDLFdBQVU7a0VBQ1p4QyxLQUFLQyxXQUFXLEdBQUd3QixzQkFBc0J6QixLQUFLQyxXQUFXLENBQUNFLE1BQU0sSUFBSTs7Ozs7Ozs7Ozs7OzJDQXpCcEVILEtBQUsxQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBb0N4Qiw4REFBQzlELHFEQUFJQTt3QkFBQ2dJLFdBQVU7a0NBQ2IxRyw2QkFDQzs7OENBQ0UsOERBQUNwQiwyREFBVUE7b0NBQUM4SCxXQUFVOzhDQUNwQiw0RUFBQzdILDBEQUFTQTt3Q0FBQzZILFdBQVU7a0RBQ25CLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQzFILHlEQUFNQTs0REFBQzBILFdBQVU7OzhFQUNoQiw4REFBQ3hILDhEQUFXQTtvRUFBQ2lJLEtBQUtuSCxhQUFhb0gsSUFBSSxDQUFDQyxLQUFLLElBQUlDO29FQUFXQyxLQUFLdkgsYUFBYW9ILElBQUksQ0FBQ2hCLElBQUk7Ozs7Ozs4RUFDbkYsOERBQUNuSCxpRUFBY0E7b0VBQUN5SCxXQUFVOzhFQUN2QlAsWUFBWW5HLGFBQWFvSCxJQUFJLENBQUNoQixJQUFJOzs7Ozs7Ozs7Ozs7d0RBR3RDcEcsYUFBYW9ILElBQUksQ0FBQ0ksUUFBUSxrQkFDekIsOERBQUNmOzREQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7OERBR25CLDhEQUFDRDs7c0VBQ0MsOERBQUNTOzREQUFHUixXQUFVO3NFQUFpQjFHLGFBQWFvSCxJQUFJLENBQUNoQixJQUFJOzs7Ozs7c0VBQ3JELDhEQUFDUTs0REFBRUYsV0FBVTtzRUFDVjFHLGFBQWFvSCxJQUFJLENBQUNJLFFBQVEsR0FBRyxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU9uRCw4REFBQzdJLDREQUFXQTtvQ0FBQytILFdBQVU7OENBQ3BCbEcsZ0NBQ0MsOERBQUNpRzt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3JILHdIQUFPQTs0Q0FBQ3FILFdBQVU7Ozs7Ozs7Ozs7NkRBR3JCLDhEQUFDRDt3Q0FBSUMsV0FBVTs7NENBQ1p4RyxTQUFTdUIsTUFBTSxLQUFLLGtCQUNuQiw4REFBQ2dGO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ3ZILHdIQUFhQTt3REFBQ3VILFdBQVU7Ozs7OztrRUFDekIsOERBQUNRO3dEQUFHUixXQUFVO2tFQUF5Qzs7Ozs7O2tFQUN2RCw4REFBQ0U7d0RBQUVGLFdBQVU7a0VBQWdCOzs7Ozs7Ozs7Ozt1REFHL0J4RyxTQUFTK0QsR0FBRyxDQUFDLENBQUNsQix3QkFDWiw4REFBQzBEO29EQUVDQyxXQUFXLFFBQTZFLE9BQXJFM0QsUUFBUXFCLFFBQVEsS0FBS3RELGdCQUFnQixnQkFBZ0I7OERBRXhFLDRFQUFDMkY7d0RBQ0NDLFdBQVcsOEJBSVYsT0FIQzNELFFBQVFxQixRQUFRLEtBQUt0RCxnQkFDakIsNEJBQ0E7OzBFQUdOLDhEQUFDOEY7Z0VBQUVGLFdBQVU7MEVBQVczRCxRQUFRQSxPQUFPOzs7Ozs7MEVBQ3ZDLDhEQUFDNkQ7Z0VBQUVGLFdBQVcsZ0JBSWIsT0FIQzNELFFBQVFxQixRQUFRLEtBQUt0RCxnQkFDakIsa0JBQ0E7MEVBRUhzRSxXQUFXckMsUUFBUXNCLE1BQU07Ozs7Ozs7Ozs7OzttREFoQnpCdEIsUUFBUVAsRUFBRTs7Ozs7MERBc0JyQiw4REFBQ2lFO2dEQUFJZ0IsS0FBS3ZHOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLaEIsOERBQUN1RjtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDM0gsdURBQUtBO2dEQUNKMkksTUFBSztnREFDTEMsYUFBWTtnREFDWkMsT0FBT3hIO2dEQUNQeUgsVUFBVSxDQUFDaEQsSUFBTXhFLGNBQWN3RSxFQUFFaUQsTUFBTSxDQUFDRixLQUFLO2dEQUM3Q0csV0FBV25EO2dEQUNYb0QsVUFBVXRIO2dEQUNWZ0csV0FBVTs7Ozs7OzBEQUVaLDhEQUFDNUgseURBQU1BO2dEQUNMbUksU0FBU3pDO2dEQUNUd0QsVUFBVSxDQUFDNUgsV0FBV3FFLElBQUksTUFBTS9EO2dEQUNoQ2dHLFdBQVU7MERBRVRoRyx3QkFDQyw4REFBQ3JCLHdIQUFPQTtvREFBQ3FILFdBQVU7Ozs7O3lFQUVuQiw4REFBQ3RILHlIQUFJQTtvREFBQ3NILFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3lEQU8xQiw4REFBQy9ILDREQUFXQTs0QkFBQytILFdBQVU7c0NBQ3JCLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUN2SCx3SEFBYUE7d0NBQUN1SCxXQUFVOzs7Ozs7a0RBQ3pCLDhEQUFDUTt3Q0FBR1IsV0FBVTtrREFBeUM7Ozs7OztrREFDdkQsOERBQUNFO3dDQUFFRixXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVE3QztHQWxmd0I3Rzs7UUFhUEYsc0RBQVNBOzs7S0FiRkUiLCJzb3VyY2VzIjpbIkU6XFxwYWlyc29uYVxcZnJvbnRlbmRcXHNyY1xcYXBwXFxkYXNoYm9hcmRcXGNoYXRcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jYXJkXCI7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiO1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCI7XG5pbXBvcnQgeyBBdmF0YXIsIEF2YXRhckZhbGxiYWNrLCBBdmF0YXJJbWFnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYXZhdGFyXCI7XG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIjtcbmltcG9ydCB7IE1lc3NhZ2VTcXVhcmUsIFNlbmQsIExvYWRlcjIsIEFsZXJ0Q2lyY2xlLCBVc2VycyB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZVJlZiB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuaW1wb3J0IHsgaW8sIFNvY2tldCB9IGZyb20gXCJzb2NrZXQuaW8tY2xpZW50XCI7XG5cbmludGVyZmFjZSBVc2VyIHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBpbWFnZTogc3RyaW5nIHwgbnVsbDtcbiAgaXNPbmxpbmU6IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBMYXN0TWVzc2FnZSB7XG4gIGlkOiBzdHJpbmc7XG4gIHNlbmRlcklkOiBzdHJpbmc7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgc2VudEF0OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBDaGF0IHtcbiAgaWQ6IHN0cmluZztcbiAgbWF0Y2hJZDogc3RyaW5nO1xuICB1c2VyOiBVc2VyO1xuICBsYXN0TWVzc2FnZTogTGFzdE1lc3NhZ2UgfCBudWxsO1xufVxuXG5pbnRlcmZhY2UgTWVzc2FnZSB7XG4gIGlkOiBzdHJpbmc7XG4gIGNoYXRJZDogc3RyaW5nO1xuICBzZW5kZXJJZDogc3RyaW5nO1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIHNlbnRBdDogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgTWVzc2FnZXNSZXNwb25zZSB7XG4gIGRhdGE6IE1lc3NhZ2VbXTtcbiAgdG90YWxJdGVtczogbnVtYmVyO1xuICBpdGVtc1BlclBhZ2U6IG51bWJlcjtcbiAgdG90YWxQYWdlczogbnVtYmVyO1xuICBjdXJyZW50UGFnZTogbnVtYmVyO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDaGF0UGFnZSgpIHtcbiAgY29uc3QgW2NoYXRzLCBzZXRDaGF0c10gPSB1c2VTdGF0ZTxDaGF0W10+KFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkQ2hhdCwgc2V0U2VsZWN0ZWRDaGF0XSA9IHVzZVN0YXRlPENoYXQgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW21lc3NhZ2VzLCBzZXRNZXNzYWdlc10gPSB1c2VTdGF0ZTxNZXNzYWdlW10+KFtdKTtcbiAgY29uc3QgW25ld01lc3NhZ2UsIHNldE5ld01lc3NhZ2VdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW21lc3NhZ2VzTG9hZGluZywgc2V0TWVzc2FnZXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NlbmRpbmcsIHNldFNlbmRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbY3VycmVudFVzZXJJZCwgc2V0Q3VycmVudFVzZXJJZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NvY2tldCwgc2V0U29ja2V0XSA9IHVzZVN0YXRlPFNvY2tldCB8IG51bGw+KG51bGwpO1xuXG4gIGNvbnN0IG1lc3NhZ2VzRW5kUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaENoYXRzKCk7XG4gICAgZ2V0Q3VycmVudFVzZXJJZCgpO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmIChzb2NrZXQpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ0NsZWFuaW5nIHVwIHNvY2tldCBjb25uZWN0aW9uJyk7XG4gICAgICAgIHNvY2tldC5kaXNjb25uZWN0KCk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW10pO1xuXG4gIC8vIEF1dG8tc2VsZWN0IGZpcnN0IGNoYXQgd2hlbiBjaGF0cyBhcmUgbG9hZGVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGNoYXRzLmxlbmd0aCA+IDAgJiYgIXNlbGVjdGVkQ2hhdCkge1xuICAgICAgY29uc29sZS5sb2coJ0F1dG8tc2VsZWN0aW5nIGZpcnN0IGNoYXQ6JywgY2hhdHNbMF0pO1xuICAgICAgaGFuZGxlQ2hhdFNlbGVjdChjaGF0c1swXSk7XG4gICAgfVxuICB9LCBbY2hhdHMsIHNlbGVjdGVkQ2hhdF0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2Nyb2xsVG9Cb3R0b20oKTtcbiAgfSwgW21lc3NhZ2VzXSk7XG5cbiAgY29uc3QgZ2V0Q3VycmVudFVzZXJJZCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncGFpcnNvbmFfdG9rZW4nKTtcbiAgICAgIGlmICghdG9rZW4pIHJldHVybjtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfQkFTRV9VUkx9L2F1dGgvcHJvZmlsZWAsIHtcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IHByb2ZpbGUgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHNldEN1cnJlbnRVc2VySWQocHJvZmlsZS5pZCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZ2V0IGN1cnJlbnQgdXNlciBJRDonLCBlcnIpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmZXRjaENoYXRzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdwYWlyc29uYV90b2tlbicpO1xuICAgICAgaWYgKCF0b2tlbikge1xuICAgICAgICByb3V0ZXIucmVwbGFjZSgnL2xvZ2luJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfQkFTRV9VUkx9L2NoYXRzYCwge1xuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdwYWlyc29uYV90b2tlbicpO1xuICAgICAgICAgIHJvdXRlci5yZXBsYWNlKCcvbG9naW4nKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggY2hhdHMnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY2hhdHNEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc29sZS5sb2coJ0ZldGNoZWQgY2hhdHM6JywgY2hhdHNEYXRhKTtcbiAgICAgIHNldENoYXRzKGNoYXRzRGF0YSk7XG5cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGxvYWQgY2hhdHMnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuICBjb25zdCBmZXRjaE1lc3NhZ2VzID0gYXN5bmMgKGNoYXRJZDogc3RyaW5nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldE1lc3NhZ2VzTG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3BhaXJzb25hX3Rva2VuJyk7XG4gICAgICBpZiAoIXRva2VuKSByZXR1cm47XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX0JBU0VfVVJMfS9jaGF0cy8ke2NoYXRJZH0vbWVzc2FnZXM/cGFnZT0xJmxpbWl0PTUwYCwge1xuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIG1lc3NhZ2VzJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IG1lc3NhZ2VzRGF0YTogTWVzc2FnZXNSZXNwb25zZSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIHNldE1lc3NhZ2VzKG1lc3NhZ2VzRGF0YS5kYXRhLnJldmVyc2UoKSk7IC8vIFJldmVyc2UgdG8gc2hvdyBvbGRlc3QgZmlyc3RcblxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gbG9hZCBtZXNzYWdlcycpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRNZXNzYWdlc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBjb25uZWN0VG9DaGF0ID0gKGNoYXRPck1hdGNoSWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3BhaXJzb25hX3Rva2VuJyk7XG4gICAgaWYgKCF0b2tlbikgcmV0dXJuO1xuXG4gICAgLy8gRGlzY29ubmVjdCBleGlzdGluZyBzb2NrZXRcbiAgICBpZiAoc29ja2V0KSB7XG4gICAgICBzb2NrZXQuZGlzY29ubmVjdCgpO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfwn5qAIENvbm5lY3RpbmcgdG8gY2hhdCB3aXRoIElEOicsIGNoYXRPck1hdGNoSWQpO1xuICAgIGNvbnNvbGUubG9nKCfwn4yQIEFQSSBCYXNlIFVSTDonLCBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfQkFTRV9VUkwpO1xuXG4gICAgLy8gVXNlIHRoZSBleGFjdCBmb3JtYXQgZnJvbSBiYWNrZW5kIGRvY3VtZW50YXRpb25cbiAgICBjb25zdCBzb2NrZXRVcmwgPSBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfQkFTRV9VUkx9L2NoYXRgO1xuICAgIGNvbnNvbGUubG9nKCfwn4yQIFNvY2tldCBVUkw6Jywgc29ja2V0VXJsKTtcblxuICAgIC8vIEZvbGxvdyBFWEFDVCBiYWNrZW5kIGRvY3VtZW50YXRpb24gZm9ybWF0XG4gICAgY29uc3QgY29uZmlnID0ge1xuICAgICAgdHJhbnNwb3J0czogWyd3ZWJzb2NrZXQnXSxcbiAgICAgIGF1dGg6IHtcbiAgICAgICAgdG9rZW46IGBCZWFyZXIgJHt0b2tlbn1gIC8vIEJhY2tlbmQgZXhwZWN0cyBCZWFyZXIgcHJlZml4IGluIGF1dGgudG9rZW5cbiAgICAgIH0sXG4gICAgICBxdWVyeToge1xuICAgICAgICBjaGF0SWQ6IGNoYXRPck1hdGNoSWQgLy8gVXNlIGNoYXRJZCBhcyBzcGVjaWZpZWQgaW4gZG9jc1xuICAgICAgfVxuICAgIH07XG5cbiAgICBjb25zb2xlLmxvZygn8J+TiyBTb2NrZXQuSU8gY29uZmlnOicsIEpTT04uc3RyaW5naWZ5KGNvbmZpZywgbnVsbCwgMikpO1xuXG4gICAgY29uc3QgbmV3U29ja2V0ID0gaW8oc29ja2V0VXJsLCBjb25maWcpO1xuXG4gICAgbmV3U29ja2V0Lm9uKCdjb25uZWN0JywgKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ+KchSBDb25uZWN0ZWQgdG8gY2hhdCBzZXJ2ZXInKTtcbiAgICAgIGNvbnNvbGUubG9nKCdTb2NrZXQgSUQ6JywgbmV3U29ja2V0LmlkKTtcbiAgICB9KTtcblxuICAgIG5ld1NvY2tldC5vbignbWVzc2FnZScsIChtZXNzYWdlOiBNZXNzYWdlKSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygn8J+TqCBOZXcgbWVzc2FnZSByZWNlaXZlZDonLCBtZXNzYWdlKTtcbiAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIG1lc3NhZ2VdKTtcblxuICAgICAgLy8gVXBkYXRlIGxhc3QgbWVzc2FnZSBpbiBjaGF0cyBsaXN0XG4gICAgICBzZXRDaGF0cyhwcmV2ID0+IHByZXYubWFwKGNoYXQgPT5cbiAgICAgICAgY2hhdC5pZCA9PT0gbWVzc2FnZS5jaGF0SWRcbiAgICAgICAgICA/IHtcbiAgICAgICAgICAgICAgLi4uY2hhdCxcbiAgICAgICAgICAgICAgbGFzdE1lc3NhZ2U6IHtcbiAgICAgICAgICAgICAgICBpZDogbWVzc2FnZS5pZCxcbiAgICAgICAgICAgICAgICBzZW5kZXJJZDogbWVzc2FnZS5zZW5kZXJJZCxcbiAgICAgICAgICAgICAgICBtZXNzYWdlOiBtZXNzYWdlLm1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgc2VudEF0OiBtZXNzYWdlLnNlbnRBdFxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgOiBjaGF0XG4gICAgICApKTtcbiAgICB9KTtcblxuICAgIG5ld1NvY2tldC5vbignZGlzY29ubmVjdCcsIChyZWFzb24pID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCfinYwgRGlzY29ubmVjdGVkIGZyb20gY2hhdCBzZXJ2ZXIsIHJlYXNvbjonLCByZWFzb24pO1xuICAgIH0pO1xuXG4gICAgbmV3U29ja2V0Lm9uKCdjb25uZWN0X2Vycm9yJywgKGVycm9yKSA9PiB7XG4gICAgICBjb25zb2xlLmVycm9yKCfwn5qrIFNvY2tldCBjb25uZWN0aW9uIGVycm9yOicsIGVycm9yKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRldGFpbHM6JywgZXJyb3IubWVzc2FnZSk7XG4gICAgICBzZXRFcnJvcignRmFpbGVkIHRvIGNvbm5lY3QgdG8gY2hhdCBzZXJ2ZXIuIFBsZWFzZSB0cnkgYWdhaW4uJyk7XG4gICAgfSk7XG5cbiAgICBuZXdTb2NrZXQub24oJ2Vycm9yJywgKGVycm9yKSA9PiB7XG4gICAgICBjb25zb2xlLmVycm9yKCfimqDvuI8gU29ja2V0IGVycm9yOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdDb25uZWN0aW9uIGVycm9yLiBQbGVhc2UgdHJ5IGFnYWluLicpO1xuICAgIH0pO1xuXG4gICAgc2V0U29ja2V0KG5ld1NvY2tldCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ2hhdFNlbGVjdCA9IChjaGF0OiBDaGF0KSA9PiB7XG4gICAgY29uc29sZS5sb2coJ1NlbGVjdGluZyBjaGF0OicsIGNoYXQpO1xuICAgIHNldFNlbGVjdGVkQ2hhdChjaGF0KTtcbiAgICBzZXRNZXNzYWdlcyhbXSk7XG4gICAgZmV0Y2hNZXNzYWdlcyhjaGF0LmlkKTtcblxuICAgIC8vIFRyeSB3aXRoIG1hdGNoSWQgZmlyc3QgKGJhc2VkIG9uIGF1dGhlbnRpY2F0aW9uIGVycm9yKVxuICAgIGNvbnNvbGUubG9nKCfwn5SEIFRyeWluZyBjb25uZWN0aW9uIHdpdGggbWF0Y2hJZDonLCBjaGF0Lm1hdGNoSWQpO1xuICAgIGNvbm5lY3RUb0NoYXQoY2hhdC5tYXRjaElkKTtcbiAgfTtcblxuICBjb25zdCBzZW5kTWVzc2FnZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIW5ld01lc3NhZ2UudHJpbSgpIHx8ICFzZWxlY3RlZENoYXQgfHwgIXNvY2tldCB8fCBzZW5kaW5nKSByZXR1cm47XG5cbiAgICBjb25zb2xlLmxvZygnU2VuZGluZyBtZXNzYWdlOicsIG5ld01lc3NhZ2UudHJpbSgpKTtcbiAgICBjb25zb2xlLmxvZygnU29ja2V0IGNvbm5lY3RlZDonLCBzb2NrZXQuY29ubmVjdGVkKTtcbiAgICBjb25zb2xlLmxvZygnU2VsZWN0ZWQgY2hhdDonLCBzZWxlY3RlZENoYXQuaWQpO1xuXG4gICAgdHJ5IHtcbiAgICAgIHNldFNlbmRpbmcodHJ1ZSk7XG5cbiAgICAgIGlmICghc29ja2V0LmNvbm5lY3RlZCkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdTb2NrZXQgbm90IGNvbm5lY3RlZCwgYXR0ZW1wdGluZyB0byByZWNvbm5lY3QuLi4nKTtcbiAgICAgICAgY29ubmVjdFRvQ2hhdChzZWxlY3RlZENoYXQuaWQpO1xuICAgICAgICBzZXRFcnJvcignQ29ubmVjdGlvbiBsb3N0LiBQbGVhc2UgdHJ5IGFnYWluLicpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHNvY2tldC5lbWl0KCdzZW5kTWVzc2FnZScsIHtcbiAgICAgICAgbWVzc2FnZTogbmV3TWVzc2FnZS50cmltKClcbiAgICAgIH0pO1xuXG4gICAgICBjb25zb2xlLmxvZygnTWVzc2FnZSBlbWl0dGVkIHN1Y2Nlc3NmdWxseScpO1xuICAgICAgc2V0TmV3TWVzc2FnZSgnJyk7XG5cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNlbmRpbmcgbWVzc2FnZTonLCBlcnIpO1xuICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBzZW5kIG1lc3NhZ2UnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0U2VuZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgc2VuZE1lc3NhZ2UoKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3Qgc2Nyb2xsVG9Cb3R0b20gPSAoKSA9PiB7XG4gICAgbWVzc2FnZXNFbmRSZWYuY3VycmVudD8uc2Nyb2xsSW50b1ZpZXcoeyBiZWhhdmlvcjogJ3Ntb290aCcgfSk7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0VGltZSA9IChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gbmV3IERhdGUoZGF0ZVN0cmluZykudG9Mb2NhbGVUaW1lU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcbiAgICAgIG1pbnV0ZTogJzItZGlnaXQnLFxuICAgICAgaG91cjEyOiB0cnVlXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0TGFzdE1lc3NhZ2VUaW1lID0gKGRhdGVTdHJpbmc6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgY29uc3QgbWVzc2FnZURhdGUgPSBuZXcgRGF0ZShkYXRlU3RyaW5nKTtcbiAgICBjb25zdCBkaWZmSW5Ib3VycyA9IE1hdGguZmxvb3IoKG5vdy5nZXRUaW1lKCkgLSBtZXNzYWdlRGF0ZS5nZXRUaW1lKCkpIC8gKDEwMDAgKiA2MCAqIDYwKSk7XG5cbiAgICBpZiAoZGlmZkluSG91cnMgPCAxKSByZXR1cm4gJ05vdyc7XG4gICAgaWYgKGRpZmZJbkhvdXJzIDwgMjQpIHJldHVybiBgJHtkaWZmSW5Ib3Vyc31oIGFnb2A7XG4gICAgcmV0dXJuIG1lc3NhZ2VEYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygpO1xuICB9O1xuXG4gIGNvbnN0IGdldEluaXRpYWxzID0gKG5hbWU6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBuYW1lLnNwbGl0KCcgJykubWFwKG4gPT4gblswXSkuam9pbignJykudG9VcHBlckNhc2UoKTtcbiAgfTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdHJhY2tpbmctdGlnaHRcIj5DaGF0PC9oMT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5Mb2FkaW5nIHlvdXIgY29udmVyc2F0aW9ucy4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNlwiPlxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm1kOmNvbC1zcGFuLTEgaC1bY2FsYygxMDB2aC0yMDBweCldXCI+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxNZXNzYWdlU3F1YXJlIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMiB0ZXh0LVsjRDA1NDREXVwiIC8+XG4gICAgICAgICAgICAgICAgQ29udmVyc2F0aW9uc1xuICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICB7QXJyYXkuZnJvbSh7IGxlbmd0aDogMyB9KS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgcm91bmRlZC1mdWxsIGJnLWdyYXktMjAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMjAwIHJvdW5kZWQgdy0zLzQgbWItMlwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTMgYmctZ3JheS0yMDAgcm91bmRlZCB3LTEvMlwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm1kOmNvbC1zcGFuLTIgaC1bY2FsYygxMDB2aC0yMDBweCldXCI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC04IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cInctOCBoLTggYW5pbWF0ZS1zcGluIHRleHQtWyNEMDU0NERdXCIgLz5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoZXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRyYWNraW5nLXRpZ2h0XCI+Q2hhdDwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+Q29ubmVjdCB3aXRoIHlvdXIgbWF0Y2hlcy48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwibXgtYXV0byBoLTEyIHctMTIgdGV4dC1yZWQtNTAwIG1iLTRcIiAvPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIG1iLTRcIj57ZXJyb3J9PC9wPlxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtmZXRjaENoYXRzfT5UcnkgQWdhaW48L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIDxkaXY+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdHJhY2tpbmctdGlnaHRcIj5DaGF0PC9oMT5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+Q29ubmVjdCB3aXRoIHlvdXIgbWF0Y2hlcy48L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgIHsvKiBDaGF0IGxpc3Qgc2lkZWJhciAqL31cbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwibWQ6Y29sLXNwYW4tMSBoLVtjYWxjKDEwMHZoLTIwMHB4KV1cIj5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPE1lc3NhZ2VTcXVhcmUgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yIHRleHQtWyNEMDU0NERdXCIgLz5cbiAgICAgICAgICAgICAgQ29udmVyc2F0aW9uc1xuICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwLTBcIj5cbiAgICAgICAgICAgIHtjaGF0cy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC04IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPFVzZXJzIGNsYXNzTmFtZT1cIm14LWF1dG8gaC0xMiB3LTEyIHRleHQtZ3JheS00MDAgbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+Tm8gY29udmVyc2F0aW9ucyB5ZXQ8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5TdGFydCBjb25uZWN0aW5nIHdpdGggeW91ciBtYXRjaGVzIHRvIGJlZ2luIGNoYXR0aW5nITwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRpdmlkZS15XCI+XG4gICAgICAgICAgICAgICAge2NoYXRzLm1hcCgoY2hhdCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICBrZXk9e2NoYXQuaWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtNCBjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1bI0YyRTdEQl0vNTAgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZENoYXQ/LmlkID09PSBjaGF0LmlkID8gJ2JnLVsjRjJFN0RCXS8zMCcgOiAnJ1xuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQ2hhdFNlbGVjdChjaGF0KX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyIGNsYXNzTmFtZT1cInctMTAgaC0xMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFySW1hZ2Ugc3JjPXtjaGF0LnVzZXIuaW1hZ2UgfHwgdW5kZWZpbmVkfSBhbHQ9e2NoYXQudXNlci5uYW1lfSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwiYmctWyNEMDU0NERdLzIwIHRleHQtWyNEMDU0NERdXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldEluaXRpYWxzKGNoYXQudXNlci5uYW1lKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXJGYWxsYmFjaz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyPlxuICAgICAgICAgICAgICAgICAgICAgICAge2NoYXQudXNlci5pc09ubGluZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0xIC1yaWdodC0xIHctMyBoLTMgYmctZ3JlZW4tNTAwIHJvdW5kZWQtZnVsbCBib3JkZXItMiBib3JkZXItd2hpdGVcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdHJ1bmNhdGVcIj57Y2hhdC51c2VyLm5hbWV9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmQgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NoYXQubGFzdE1lc3NhZ2UgPyBjaGF0Lmxhc3RNZXNzYWdlLm1lc3NhZ2UgOiAnTm8gbWVzc2FnZXMgeWV0J31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Y2hhdC5sYXN0TWVzc2FnZSA/IGZvcm1hdExhc3RNZXNzYWdlVGltZShjaGF0Lmxhc3RNZXNzYWdlLnNlbnRBdCkgOiAnJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICB7LyogQ2hhdCBhcmVhICovfVxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJtZDpjb2wtc3Bhbi0yIGgtW2NhbGMoMTAwdmgtMjAwcHgpXSBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAge3NlbGVjdGVkQ2hhdCA/IChcbiAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cImJvcmRlci1iXCI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtci0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJ3LTggaC04XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFySW1hZ2Ugc3JjPXtzZWxlY3RlZENoYXQudXNlci5pbWFnZSB8fCB1bmRlZmluZWR9IGFsdD17c2VsZWN0ZWRDaGF0LnVzZXIubmFtZX0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXJGYWxsYmFjayBjbGFzc05hbWU9XCJiZy1bI0QwNTQ0RF0vMjAgdGV4dC1bI0QwNTQ0RF1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2dldEluaXRpYWxzKHNlbGVjdGVkQ2hhdC51c2VyLm5hbWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXJGYWxsYmFjaz5cbiAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhcj5cbiAgICAgICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRDaGF0LnVzZXIuaXNPbmxpbmUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTEgLXJpZ2h0LTEgdy0zIGgtMyBiZy1ncmVlbi01MDAgcm91bmRlZC1mdWxsIGJvcmRlci0yIGJvcmRlci13aGl0ZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+e3NlbGVjdGVkQ2hhdC51c2VyLm5hbWV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkQ2hhdC51c2VyLmlzT25saW5lID8gJ09ubGluZScgOiAnT2ZmbGluZSd9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG5cbiAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy1hdXRvIHAtNFwiPlxuICAgICAgICAgICAgICAgIHttZXNzYWdlc0xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJ3LTYgaC02IGFuaW1hdGUtc3BpbiB0ZXh0LVsjRDA1NDREXVwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAge21lc3NhZ2VzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxNZXNzYWdlU3F1YXJlIGNsYXNzTmFtZT1cIm14LWF1dG8gaC0xMiB3LTEyIHRleHQtZ3JheS00MDAgbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5ObyBtZXNzYWdlcyB5ZXQ8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPlN0YXJ0IHRoZSBjb252ZXJzYXRpb24gYnkgc2VuZGluZyBhIG1lc3NhZ2UhPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2VzLm1hcCgobWVzc2FnZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e21lc3NhZ2UuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggJHttZXNzYWdlLnNlbmRlcklkID09PSBjdXJyZW50VXNlcklkID8gJ2p1c3RpZnktZW5kJyA6ICdqdXN0aWZ5LXN0YXJ0J31gfVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcm91bmRlZC1sZyBwLTMgbWF4LXctWzgwJV0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2Uuc2VuZGVySWQgPT09IGN1cnJlbnRVc2VySWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctWyNEMDU0NERdIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLVsjRjJFN0RCXSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc21cIj57bWVzc2FnZS5tZXNzYWdlfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LXhzIG10LTEgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2Uuc2VuZGVySWQgPT09IGN1cnJlbnRVc2VySWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAndGV4dC13aGl0ZS83MCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1tdXRlZC1mb3JlZ3JvdW5kJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRUaW1lKG1lc3NhZ2Uuc2VudEF0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPGRpdiByZWY9e21lc3NhZ2VzRW5kUmVmfSAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXItdFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiVHlwZSBhIG1lc3NhZ2UuLi5cIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3TWVzc2FnZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdNZXNzYWdlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgb25LZXlEb3duPXtoYW5kbGVLZXlEb3dufVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17c2VuZGluZ31cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGZvY3VzOnJpbmctWyNEMDU0NERdIGZvY3VzOmJvcmRlci1bI0QwNTQ0RF1cIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17c2VuZE1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshbmV3TWVzc2FnZS50cmltKCkgfHwgc2VuZGluZ31cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctWyNEMDU0NERdIGhvdmVyOmJnLVsjRDA1NDREXS85MCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge3NlbmRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwiaC01IHctNSBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDxTZW5kIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxNZXNzYWdlU3F1YXJlIGNsYXNzTmFtZT1cIm14LWF1dG8gaC0xNiB3LTE2IHRleHQtZ3JheS00MDAgbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+U2VsZWN0IGEgY29udmVyc2F0aW9uPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+Q2hvb3NlIGEgY29udmVyc2F0aW9uIGZyb20gdGhlIHNpZGViYXIgdG8gc3RhcnQgY2hhdHRpbmc8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICApfVxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQnV0dG9uIiwiSW5wdXQiLCJBdmF0YXIiLCJBdmF0YXJGYWxsYmFjayIsIkF2YXRhckltYWdlIiwiTWVzc2FnZVNxdWFyZSIsIlNlbmQiLCJMb2FkZXIyIiwiQWxlcnRDaXJjbGUiLCJVc2VycyIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlUm91dGVyIiwiaW8iLCJDaGF0UGFnZSIsImNoYXRzIiwic2V0Q2hhdHMiLCJzZWxlY3RlZENoYXQiLCJzZXRTZWxlY3RlZENoYXQiLCJtZXNzYWdlcyIsInNldE1lc3NhZ2VzIiwibmV3TWVzc2FnZSIsInNldE5ld01lc3NhZ2UiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsIm1lc3NhZ2VzTG9hZGluZyIsInNldE1lc3NhZ2VzTG9hZGluZyIsInNlbmRpbmciLCJzZXRTZW5kaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImN1cnJlbnRVc2VySWQiLCJzZXRDdXJyZW50VXNlcklkIiwic29ja2V0Iiwic2V0U29ja2V0IiwibWVzc2FnZXNFbmRSZWYiLCJyb3V0ZXIiLCJmZXRjaENoYXRzIiwiZ2V0Q3VycmVudFVzZXJJZCIsImNvbnNvbGUiLCJsb2ciLCJkaXNjb25uZWN0IiwibGVuZ3RoIiwiaGFuZGxlQ2hhdFNlbGVjdCIsInNjcm9sbFRvQm90dG9tIiwidG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwicmVzcG9uc2UiLCJmZXRjaCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfQkFTRV9VUkwiLCJoZWFkZXJzIiwib2siLCJwcm9maWxlIiwianNvbiIsImlkIiwiZXJyIiwicmVwbGFjZSIsInN0YXR1cyIsInJlbW92ZUl0ZW0iLCJFcnJvciIsImNoYXRzRGF0YSIsIm1lc3NhZ2UiLCJmZXRjaE1lc3NhZ2VzIiwiY2hhdElkIiwibWVzc2FnZXNEYXRhIiwiZGF0YSIsInJldmVyc2UiLCJjb25uZWN0VG9DaGF0IiwiY2hhdE9yTWF0Y2hJZCIsInNvY2tldFVybCIsImNvbmZpZyIsInRyYW5zcG9ydHMiLCJhdXRoIiwicXVlcnkiLCJKU09OIiwic3RyaW5naWZ5IiwibmV3U29ja2V0Iiwib24iLCJwcmV2IiwibWFwIiwiY2hhdCIsImxhc3RNZXNzYWdlIiwic2VuZGVySWQiLCJzZW50QXQiLCJyZWFzb24iLCJtYXRjaElkIiwic2VuZE1lc3NhZ2UiLCJ0cmltIiwiY29ubmVjdGVkIiwiZW1pdCIsImhhbmRsZUtleURvd24iLCJlIiwia2V5Iiwic2hpZnRLZXkiLCJwcmV2ZW50RGVmYXVsdCIsImN1cnJlbnQiLCJzY3JvbGxJbnRvVmlldyIsImJlaGF2aW9yIiwiZm9ybWF0VGltZSIsImRhdGVTdHJpbmciLCJEYXRlIiwidG9Mb2NhbGVUaW1lU3RyaW5nIiwiaG91ciIsIm1pbnV0ZSIsImhvdXIxMiIsImZvcm1hdExhc3RNZXNzYWdlVGltZSIsIm5vdyIsIm1lc3NhZ2VEYXRlIiwiZGlmZkluSG91cnMiLCJNYXRoIiwiZmxvb3IiLCJnZXRUaW1lIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiZ2V0SW5pdGlhbHMiLCJuYW1lIiwic3BsaXQiLCJuIiwiam9pbiIsInRvVXBwZXJDYXNlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiQXJyYXkiLCJmcm9tIiwiXyIsImkiLCJvbkNsaWNrIiwiaDMiLCJzcmMiLCJ1c2VyIiwiaW1hZ2UiLCJ1bmRlZmluZWQiLCJhbHQiLCJpc09ubGluZSIsInJlZiIsInR5cGUiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJvbktleURvd24iLCJkaXNhYmxlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/chat/page.tsx\n"));

/***/ })

});