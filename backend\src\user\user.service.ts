import { Injectable, ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Photo, User, UserDocument } from './schemas/user.schema';
import { CreateUserDto } from './dto/create-user.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { NotificationEvent } from 'src/notification/notification.service';
import { UpdateUserDto } from './dto/update-user.dto';
import { EVENT_IMAGE_DELETED, EVENT_PHOTO_DELETED } from 'src/constants/event-emitter';

@Injectable()
export class UserService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private readonly eventEmitter: EventEmitter2
  ) {}

  /**
   * Find a user by ID
   * @param id 
   * @returns 
   */
  async findOne(id: string): Promise<UserDocument> {
    const user = await this.userModel.findById(id).exec();
    
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    
    return user;
  }

  /**
   * Update a user
   * @param id 
   * @param updateData 
   * @returns 
   */
  async update(id: string, updateData: Partial<UpdateUserDto>): Promise<UserDocument> {
    const updatedUser = await this.userModel.findByIdAndUpdate(id, updateData, { new: true }).exec();
      
    if (!updatedUser) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    
    return updatedUser;
  }

  /**
   * Find a user by email
   * @param email 
   * @returns 
   */
  async findOneByEmail(email: string): Promise<UserDocument> {
    const user = await this.userModel.findOne({ email }).exec();
    
    if (!user) {
      throw new NotFoundException(`User with email ${email} not found`);
    }
    
    return user;
  }

  /**
   * Find a user by email or phone number
   * @param email 
   * @param phoneNumber 
   * @returns 
   */
  async findByEmailOrPhoneNumber(email: string, phoneNumber: string): Promise<UserDocument> {
    const user = await this.userModel.findOne({ $or: [{ email }, { phoneNumber }] }).exec();
    
    if (!user) {
      throw new NotFoundException(`User not found`);
    }
    
    return user;
  }

  /**
   * Check if a user exists by email or phone number
   * @param email 
   * @param phoneNumber 
   * @returns 
   */
  async checkUserExistsEmailOrPhoneNumber(email: string, phoneNumber: string): Promise<boolean> {
    const user = await this.userModel.findOne({ $or: [{ email }, { phoneNumber }] }).select('_id').exec();
    
    if (!user) {
      return false;
    }
    
    return true;
  }
  
  /**
   * Create a user
   * @param createUserDto 
   * @returns 
   */
  async createUser(createUserDto: CreateUserDto): Promise<UserDocument> {
    try {
      return await this.userModel.create(createUserDto);
    } catch (error) {
      throw new ConflictException(error.message);
    }
  }

  /**
   * Verify a user
   * @param id 
   * @returns 
   */
  async verifyUser(id: string): Promise<Boolean> {
    try {
      const user = await this.userModel.findById(id);
      
      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      if (!user.verified && user.email) {
        this.eventEmitter.emit(NotificationEvent.USER_SIGNUP, {
          userId: user.id,
          name: user.name,
          email: user.email,
          phoneNumber: user.phoneNumber
        });
      }
      
      await this.userModel.findByIdAndUpdate(id, { verified: true });
      return true;
    } catch(error) {
      throw new BadRequestException(error.message);
    }
  }

  async findUsersWithCompletedPersonalityTest(): Promise<UserDocument[]> {
    try {
      return await this.userModel.find({ psychTestCompleted: true }).exec();
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async markUserAsPersonalityTestCompleted(userId: string): Promise<UserDocument | null> {
    try {
      return await this.userModel.findByIdAndUpdate(userId, {
        psychTestCompleted: true
      }, { new: true }).exec();
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Update an image
   * 
   * @param userId 
   * @param photoData 
   * @returns 
   */
  async updateImage(userId: string, photoData: { url: string; key: string }) {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    if (user.image) {
      this.eventEmitter.emit(EVENT_IMAGE_DELETED, {
        userId,
        key: user.image.key
      });
    }
    
    return this.userModel.findByIdAndUpdate(
      userId,
      { image: photoData },
      { new: true }
    );
  }

  /**
   * Remove an image
   * 
   * @param userId 
   * @returns 
   */
  async removeImage(userId: string) {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    if (user.image) {
      this.eventEmitter.emit(EVENT_IMAGE_DELETED, {
        userId,
        key: user.image.key
      });
    }

    return this.userModel.findByIdAndUpdate(
      userId,
      { image: undefined },
      { new: true }
    );
  }

  /**
   * Add a photo
   * 
   * @param userId 
   * @param photoData 
   * @returns 
   */
  async addPhoto(userId: string, photoData: { url: string; key: string }) {
    return this.userModel.findByIdAndUpdate(
      userId,
      { $push: { photos: photoData } },
      { new: true }
    );
  }

  /**
   * Remove a photo by ID
   * 
   * @param userId 
   * @param photoId 
   * @returns 
   */
  async removePhotoById(userId: string, photoId: string) {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const photo = user.photos.id(photoId);
    if (!photo) {
      throw new NotFoundException('Photo not found');
    }

    this.eventEmitter.emit(EVENT_PHOTO_DELETED, {
      userId,
      key: photo.key
    });

    return this.userModel.findByIdAndUpdate(
      userId,
      { $pull: { photos: { _id: new Types.ObjectId(photoId) } } },
      { new: true }
    );
  }  
}
