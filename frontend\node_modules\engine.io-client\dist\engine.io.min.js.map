{"version": 3, "file": "engine.io.min.js", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../build/esm/globalThis.browser.js", "../build/esm/util.js", "../build/esm/contrib/parseqs.js", "../build/esm/contrib/yeast.js", "../build/esm/transport.js", "../build/esm/contrib/has-cors.js", "../build/esm/transports/xmlhttprequest.browser.js", "../build/esm/transports/polling.js", "../build/esm/transports/websocket-constructor.browser.js", "../build/esm/transports/websocket.js", "../build/esm/transports/webtransport.js", "../build/esm/transports/index.js", "../build/esm/contrib/parseuri.js", "../build/esm/socket.js", "../build/esm/browser-entrypoint.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data\n            .arrayBuffer()\n            .then(toArray)\n            .then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, encoded => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, encodedPacket => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        }\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else if (state === 2 /* READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        }\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\nexport function createCookieJar() { }\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { createCookieJar, XHR as XMLHttpRequest, } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n        if (this.opts.withCredentials) {\n            this.cookieJar = createCookieJar();\n        }\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, cookieJar: this.cookieJar }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        var _a;\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, true);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(xhr);\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"./websocket-constructor.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        // @ts-ignore\n        if (typeof WebTransport !== \"function\") {\n            return;\n        }\n        // @ts-ignore\n        this.transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        this.transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this.transport.ready.then(() => {\n            this.transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this.writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this.writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this.writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this.transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: Polling,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 2000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { defaultBinaryType } from \"./transports/websocket-constructor.js\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\n            \"polling\",\n            \"websocket\",\n            \"webtransport\",\n        ];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this.upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            this.resetPingTimeout();\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport default (uri, opts) => new Socket(uri, opts);\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "TEXT_ENCODER", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "_ref", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "chars", "lookup", "i", "length", "charCodeAt", "TEXT_DECODER", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "createPacketEncoderStream", "TransformStream", "transform", "packet", "controller", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "encodePacketToBinary", "header", "payloadLength", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "_len", "attr", "_key", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "prev", "TransportError", "_Error", "_inherits", "_super", "_createSuper", "reason", "description", "context", "_this", "_classCallCheck", "_wrapNativeSuper", "Error", "Transport", "_Emitter", "_super2", "_this2", "writable", "_assertThisInitialized", "query", "socket", "_createClass", "value", "_get", "_getPrototypeOf", "readyState", "doOpen", "doClose", "onClose", "packets", "write", "onPacket", "details", "onPause", "schema", "undefined", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "str", "encodeURIComponent", "alphabet", "map", "seed", "num", "Math", "floor", "yeast", "now", "Date", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Polling", "_Transport", "polling", "location", "isSSL", "protocol", "xd", "forceBase64", "withCredentials", "cookieJar", "createCookieJar", "poll", "pause", "total", "doPoll", "_this3", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "onOpen", "_this4", "close", "_this5", "count", "encodePayload", "doWrite", "timestampRequests", "timestampParam", "sid", "b64", "createUri", "_extends", "Request", "uri", "_this6", "req", "request", "method", "xhrStatus", "onError", "_this7", "onData", "pollXhr", "get", "_this8", "_a", "_this9", "xhr", "open", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "addCookies", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "status", "onLoad", "send", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "WebSocket", "MozWebSocket", "defaultBinaryType", "isReactNative", "navigator", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "_loop", "lastPacket", "WT", "WebTransport", "transport", "transportOptions", "name", "closed", "ready", "createBidirectionalStream", "stream", "decoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "pow", "createPacketDecoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "writer", "getWriter", "read", "done", "transports", "websocket", "webtransport", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "writeBuffer", "_typeof", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "offlineEventListener", "EIO", "priorWebsocketSuccess", "createTransport", "setTransport", "onDrain", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "probe", "resetPingTimeout", "onHandshake", "JSON", "sendPacket", "code", "filterUpgrades", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades"], "mappings": ";;;;;29FAAA,IAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,IAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQ,SAAAC,GAC9BH,EAAqBH,EAAaM,IAAQA,CAC9C,IACA,ICuCIC,EDvCEC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCX,OAAOY,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAE/BC,EAAS,SAAAC,GACX,MAAqC,mBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,GAAOA,EAAIC,kBAAkBH,WACvC,EACMI,EAAe,SAAHC,EAAoBC,EAAgBC,GAAa,IAA3Cf,EAAIa,EAAJb,KAAMC,EAAIY,EAAJZ,KAC1B,OAAIC,GAAkBD,aAAgBE,KAC9BW,EACOC,EAASd,GAGTe,EAAmBf,EAAMc,GAG/BR,IACJN,aAAgBO,aAAeC,EAAOR,IACnCa,EACOC,EAASd,GAGTe,EAAmB,IAAIb,KAAK,CAACF,IAAQc,GAI7CA,EAASxB,EAAaS,IAASC,GAAQ,IAClD,EACMe,EAAqB,SAACf,EAAMc,GAC9B,IAAME,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,IAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CP,EAAS,KAAOK,GAAW,MAExBH,EAAWM,cAActB,EACpC,EACA,SAASuB,EAAQvB,GACb,OAAIA,aAAgBwB,WACTxB,EAEFA,aAAgBO,YACd,IAAIiB,WAAWxB,GAGf,IAAIwB,WAAWxB,EAAKU,OAAQV,EAAKyB,WAAYzB,EAAK0B,WAEjE,CC9CA,IAHA,IAAMC,EAAQ,mEAERC,EAA+B,oBAAfJ,WAA6B,GAAK,IAAIA,WAAW,KAC9DK,EAAI,EAAGA,EAAIF,EAAMG,OAAQD,IAC9BD,EAAOD,EAAMI,WAAWF,IAAMA,EAkB3B,ICyCHG,EC9DE1B,EAA+C,mBAAhBC,YACxB0B,EAAe,SAACC,EAAeC,GACxC,GAA6B,iBAAlBD,EACP,MAAO,CACHnC,KAAM,UACNC,KAAMoC,EAAUF,EAAeC,IAGvC,IAAMpC,EAAOmC,EAAcG,OAAO,GAClC,MAAa,MAATtC,EACO,CACHA,KAAM,UACNC,KAAMsC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAG1C1C,EAAqBM,GAIjCmC,EAAcJ,OAAS,EACxB,CACE/B,KAAMN,EAAqBM,GAC3BC,KAAMkC,EAAcK,UAAU,IAEhC,CACExC,KAAMN,EAAqBM,IARxBD,CAUf,EACMwC,EAAqB,SAACtC,EAAMmC,GAC9B,GAAI7B,EAAuB,CACvB,IAAMkC,EFTQ,SAACC,GACnB,IAA8DZ,EAAUa,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOX,OAAeiB,EAAMN,EAAOX,OAAWkB,EAAI,EACnC,MAA9BP,EAAOA,EAAOX,OAAS,KACvBgB,IACkC,MAA9BL,EAAOA,EAAOX,OAAS,IACvBgB,KAGR,IAAMG,EAAc,IAAI1C,YAAYuC,GAAeI,EAAQ,IAAI1B,WAAWyB,GAC1E,IAAKpB,EAAI,EAAGA,EAAIkB,EAAKlB,GAAK,EACtBa,EAAWd,EAAOa,EAAOV,WAAWF,IACpCc,EAAWf,EAAOa,EAAOV,WAAWF,EAAI,IACxCe,EAAWhB,EAAOa,EAAOV,WAAWF,EAAI,IACxCgB,EAAWjB,EAAOa,EAAOV,WAAWF,EAAI,IACxCqB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CACX,CEVwBE,CAAOnD,GACvB,OAAOoC,EAAUI,EAASL,GAG1B,MAAO,CAAEM,QAAQ,EAAMzC,KAAAA,EAE/B,EACMoC,EAAY,SAACpC,EAAMmC,GACrB,MACS,SADDA,EAEInC,aAAgBE,KAETF,EAIA,IAAIE,KAAK,CAACF,IAIjBA,aAAgBO,YAETP,EAIAA,EAAKU,MAG5B,ED1DM0C,EAAYC,OAAOC,aAAa,IA4B/B,SAASC,IACZ,OAAO,IAAIC,gBAAgB,CACvBC,mBAAUC,EAAQC,IFmBnB,SAA8BD,EAAQ5C,GACrCb,GAAkByD,EAAO1D,gBAAgBE,KAClCwD,EAAO1D,KACT4D,cACAC,KAAKtC,GACLsC,KAAK/C,GAELR,IACJoD,EAAO1D,gBAAgBO,aAAeC,EAAOkD,EAAO1D,OAC9Cc,EAASS,EAAQmC,EAAO1D,OAEnCW,EAAa+C,GAAQ,GAAO,SAAAI,GACnBjE,IACDA,EAAe,IAAIkE,aAEvBjD,EAASjB,EAAamE,OAAOF,MAErC,CEnCYG,CAAqBP,GAAQ,SAAAxB,GACzB,IACIgC,EADEC,EAAgBjC,EAAcJ,OAGpC,GAAIqC,EAAgB,IAChBD,EAAS,IAAI1C,WAAW,GACxB,IAAI4C,SAASF,EAAOxD,QAAQ2D,SAAS,EAAGF,QAEvC,GAAIA,EAAgB,MAAO,CAC5BD,EAAS,IAAI1C,WAAW,GACxB,IAAM8C,EAAO,IAAIF,SAASF,EAAOxD,QACjC4D,EAAKD,SAAS,EAAG,KACjBC,EAAKC,UAAU,EAAGJ,OAEjB,CACDD,EAAS,IAAI1C,WAAW,GACxB,IAAM8C,EAAO,IAAIF,SAASF,EAAOxD,QACjC4D,EAAKD,SAAS,EAAG,KACjBC,EAAKE,aAAa,EAAGC,OAAON,IAG5BT,EAAO1D,MAA+B,iBAAhB0D,EAAO1D,OAC7BkE,EAAO,IAAM,KAEjBP,EAAWe,QAAQR,GACnBP,EAAWe,QAAQxC,QAInC,CAEA,SAASyC,EAAYC,GACjB,OAAOA,EAAOC,QAAO,SAACC,EAAKC,GAAK,OAAKD,EAAMC,EAAMjD,SAAQ,EAC7D,CACA,SAASkD,EAAaJ,EAAQK,GAC1B,GAAIL,EAAO,GAAG9C,SAAWmD,EACrB,OAAOL,EAAOM,QAIlB,IAFA,IAAMxE,EAAS,IAAIc,WAAWyD,GAC1BE,EAAI,EACCtD,EAAI,EAAGA,EAAIoD,EAAMpD,IACtBnB,EAAOmB,GAAK+C,EAAO,GAAGO,KAClBA,IAAMP,EAAO,GAAG9C,SAChB8C,EAAOM,QACPC,EAAI,GAMZ,OAHIP,EAAO9C,QAAUqD,EAAIP,EAAO,GAAG9C,SAC/B8C,EAAO,GAAKA,EAAO,GAAGQ,MAAMD,IAEzBzE,CACX,CE/EO,SAAS2E,EAAQ5E,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIb,KAAOyF,EAAQlF,UACtBM,EAAIb,GAAOyF,EAAQlF,UAAUP,GAE/B,OAAOa,CACT,CAhBkB6E,CAAM7E,EACxB,CA0BA4E,EAAQlF,UAAUoF,GAClBF,EAAQlF,UAAUqF,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,IACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACT,EAYAN,EAAQlF,UAAU2F,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,WAKjB,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACT,EAYAN,EAAQlF,UAAU4F,IAClBV,EAAQlF,UAAU+F,eAClBb,EAAQlF,UAAUgG,mBAClBd,EAAQlF,UAAUiG,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,GAGjC,GAAKK,UAAUnE,OAEjB,OADA6D,KAAKC,WAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,WAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAUnE,OAEjB,cADO6D,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI9D,EAAI,EAAGA,EAAIyE,EAAUxE,OAAQD,IAEpC,IADAwE,EAAKC,EAAUzE,MACJ6D,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO1E,EAAG,GACpB,MAUJ,OAJyB,IAArByE,EAAUxE,eACL6D,KAAKC,WAAW,IAAMH,GAGxBE,IACT,EAUAN,EAAQlF,UAAUqG,KAAO,SAASf,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,GAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAUnE,OAAS,GACpCwE,EAAYX,KAAKC,WAAW,IAAMH,GAE7B5D,EAAI,EAAGA,EAAIoE,UAAUnE,OAAQD,IACpC4E,EAAK5E,EAAI,GAAKoE,UAAUpE,GAG1B,GAAIyE,EAEG,CAAIzE,EAAI,EAAb,IAAK,IAAWkB,GADhBuD,EAAYA,EAAUlB,MAAM,IACItD,OAAQD,EAAIkB,IAAOlB,EACjDyE,EAAUzE,GAAGmE,MAAML,KAAMc,EADK3E,CAKlC,OAAO6D,IACT,EAGAN,EAAQlF,UAAUwG,aAAetB,EAAQlF,UAAUqG,KAUnDnB,EAAQlF,UAAUyG,UAAY,SAASnB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,GAC9BD,KAAKC,WAAW,IAAMH,IAAU,EACzC,EAUAJ,EAAQlF,UAAU0G,aAAe,SAASpB,GACxC,QAAUE,KAAKiB,UAAUnB,GAAO3D,MAClC,ECxKO,IAAMgF,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKzG,GAAc,QAAA0G,EAAAlB,UAAAnE,OAANsF,MAAIV,MAAAS,IAAAA,OAAAE,IAAAA,EAAAF,EAAAE,IAAJD,EAAIC,KAAApB,UAAAoB,GAC7B,OAAOD,EAAKvC,QAAO,SAACC,EAAKwC,GAIrB,OAHI7G,EAAI8G,eAAeD,KACnBxC,EAAIwC,GAAK7G,EAAI6G,IAEVxC,IACR,GACP,CAEA,IAAM0C,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsBpH,EAAKqH,GACnCA,EAAKC,iBACLtH,EAAIuH,aAAeR,EAAmBS,KAAKR,GAC3ChH,EAAIyH,eAAiBP,EAAqBM,KAAKR,KAG/ChH,EAAIuH,aAAeP,EAAWC,WAAWO,KAAKR,GAC9ChH,EAAIyH,eAAiBT,EAAWG,aAAaK,KAAKR,GAE1D,CCIO,SAAStE,EAAOgF,GAGnB,IAFA,IAAIC,EAAM,GACNC,EAAQF,EAAG9G,MAAM,KACZQ,EAAI,EAAGyG,EAAID,EAAMvG,OAAQD,EAAIyG,EAAGzG,IAAK,CAC1C,IAAI0G,EAAOF,EAAMxG,GAAGR,MAAM,KAC1B+G,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,IAE/D,OAAOH,CACX,KC9BqBK,ECCRC,WAAcC,GAAAC,EAAAF,EAAAC,GAAA,IAAAE,EAAAC,EAAAJ,GACvB,SAAAA,EAAYK,EAAQC,EAAaC,GAAS,IAAAC,EAIT,OAJSC,OAAAT,IACtCQ,EAAAL,EAAAxI,UAAM0I,IACDC,YAAcA,EACnBE,EAAKD,QAAUA,EACfC,EAAKnJ,KAAO,iBAAiBmJ,EAChC,OAAAR,CAAA,EAAAU,EAN+BC,QAQvBC,WAASC,GAAAX,EAAAU,EAAAC,GAAA,IAAAC,EAAAV,EAAAQ,GAOlB,SAAAA,EAAYxB,GAAM,IAAA2B,EAMY,OANZN,OAAAG,IACdG,EAAAD,EAAAnJ,YACKqJ,UAAW,EAChB7B,EAAqB8B,EAAAF,GAAO3B,GAC5B2B,EAAK3B,KAAOA,EACZ2B,EAAKG,MAAQ9B,EAAK8B,MAClBH,EAAKI,OAAS/B,EAAK+B,OAAOJ,EAkH7B,OAhHDK,EAAAR,IAAA1J,cAAAmK,eASQhB,EAAQC,EAAaC,GAEzB,OADAe,EAAAC,EAAAX,EAAAnJ,gCAAAE,UAAmB,QAAS,IAAIqI,EAAeK,EAAQC,EAAaC,IAC7DtD,QAEX/F,WAAAmK,iBAMI,OAFApE,KAAKuE,WAAa,UAClBvE,KAAKwE,SACExE,QAEX/F,YAAAmK,iBAQI,MAJwB,YAApBpE,KAAKuE,YAAgD,SAApBvE,KAAKuE,aACtCvE,KAAKyE,UACLzE,KAAK0E,WAEF1E,QAEX/F,WAAAmK,eAKKO,GACuB,SAApB3E,KAAKuE,YACLvE,KAAK4E,MAAMD,MAMnB1K,aAAAmK,iBAMIpE,KAAKuE,WAAa,OAClBvE,KAAK+D,UAAW,EAChBM,EAAAC,EAAAX,EAAAnJ,gCAAAE,UAAmB,WAEvBT,aAAAmK,eAMO/J,GACH,IAAM0D,EAASzB,EAAajC,EAAM2F,KAAKkE,OAAO1H,YAC9CwD,KAAK6E,SAAS9G,MAElB9D,eAAAmK,eAKSrG,GACLsG,EAAAC,EAAAX,EAAAnJ,gCAAAE,UAAmB,SAAUqD,MAEjC9D,cAAAmK,eAKQU,GACJ9E,KAAKuE,WAAa,SAClBF,EAAAC,EAAAX,EAAAnJ,gCAAAE,UAAmB,QAASoK,MAEhC7K,YAAAmK,eAKMW,OAAY9K,gBAAAmK,eACRY,GAAoB,IAAZf,EAAK3D,UAAAnE,eAAA8I,IAAA3E,aAAAA,aAAG,GACtB,OAAQ0E,EACJ,MACAhF,KAAKkF,YACLlF,KAAKmF,QACLnF,KAAKmC,KAAKiD,KACVpF,KAAKqF,OAAOpB,MACnBhK,gBAAAmK,iBAEG,IAAMkB,EAAWtF,KAAKmC,KAAKmD,SAC3B,OAAkC,IAA3BA,EAASC,QAAQ,KAAcD,EAAW,IAAMA,EAAW,OACrErL,YAAAmK,iBAEG,OAAIpE,KAAKmC,KAAKqD,OACRxF,KAAKmC,KAAKsD,QAAUC,OAA0B,MAAnB1F,KAAKmC,KAAKqD,QACjCxF,KAAKmC,KAAKsD,QAAqC,KAA3BC,OAAO1F,KAAKmC,KAAKqD,OACpC,IAAMxF,KAAKmC,KAAKqD,KAGhB,MAEdvL,aAAAmK,eACMH,GACH,IAAM0B,EFjIP,SAAgB7K,GACnB,IAAI8K,EAAM,GACV,IAAK,IAAI1J,KAAKpB,EACNA,EAAI8G,eAAe1F,KACf0J,EAAIzJ,SACJyJ,GAAO,KACXA,GAAOC,mBAAmB3J,GAAK,IAAM2J,mBAAmB/K,EAAIoB,KAGpE,OAAO0J,CACX,CEuH6BvH,CAAO4F,GAC5B,OAAO0B,EAAaxJ,OAAS,IAAMwJ,EAAe,OACrDhC,CAAA,EA/H0BjE,GDVzBoG,EAAW,mEAAmEpK,MAAM,IAAkBqK,EAAM,GAC9GC,EAAO,EAAG9J,EAAI,EAQX,SAASmC,EAAO4H,GACnB,IAAI9H,EAAU,GACd,GACIA,EAAU2H,EAASG,EAZ6E,IAY7D9H,EACnC8H,EAAMC,KAAKC,MAAMF,EAb+E,UAc3FA,EAAM,GACf,OAAO9H,CACX,CAqBO,SAASiI,IACZ,IAAMC,EAAMhI,GAAQ,IAAIiI,MACxB,OAAID,IAAQvD,GACDkD,EAAO,EAAGlD,EAAOuD,GACrBA,EAAM,IAAMhI,EAAO2H,IAC9B,CAIA,KAAO9J,EA9CiG,GA8CrFA,IACf6J,EAAID,EAAS5J,IAAMA,EEhDvB,IAAIkI,GAAQ,EACZ,IACIA,EAAkC,oBAAnBmC,gBACX,oBAAqB,IAAIA,cAI7B,CAFJ,MAAOC,GAEH,CAEG,IAAMC,GAAUrC,ECPhB,SAASsC,GAAIvE,GAChB,IAAMwE,EAAUxE,EAAKwE,QAErB,IACI,GAAI,oBAAuBJ,kBAAoBI,GAAWF,IACtD,OAAO,IAAIF,eAGnB,MAAOK,IACP,IAAKD,EACD,IACI,OAAO,IAAI7E,EAAW,CAAC,UAAU+E,OAAO,UAAUC,KAAK,OAAM,qBAEjE,MAAOF,IAEf,CCXA,SAASG,MACT,IAAMC,GAIK,MAHK,IAAIT,GAAe,CAC3BI,SAAS,IAEMM,aAEVC,YAAOC,GAAAlE,EAAAiE,EAAAC,GAAA,IAAAjE,EAAAC,EAAA+D,GAOhB,SAAAA,EAAY/E,GAAM,IAAAoB,EAGd,GAHcC,OAAA0D,IACd3D,EAAAL,EAAAxI,UAAMyH,IACDiF,SAAU,EACS,oBAAbC,SAA0B,CACjC,IAAMC,EAAQ,WAAaD,SAASE,SAChC/B,EAAO6B,SAAS7B,KAEfA,IACDA,EAAO8B,EAAQ,MAAQ,MAE3B/D,EAAKiE,GACoB,oBAAbH,UACJlF,EAAKmD,WAAa+B,SAAS/B,UAC3BE,IAASrD,EAAKqD,KAK1B,IAAMiC,EAActF,GAAQA,EAAKsF,YAIhC,OAHDlE,EAAKrI,eAAiB8L,KAAYS,EAC9BlE,EAAKpB,KAAKuF,kBACVnE,EAAKoE,eAAYC,GACpBrE,EAIJ,OAHAY,EAAA+C,IAAAjN,aAIDmK,iBAOIpE,KAAK6H,UAET5N,YAAAmK,eAMMW,GAAS,IAAAjB,OACX9D,KAAKuE,WAAa,UAClB,IAAMuD,EAAQ,WACVhE,EAAKS,WAAa,SAClBQ,KAEJ,GAAI/E,KAAKoH,UAAYpH,KAAK+D,SAAU,CAChC,IAAIgE,EAAQ,EACR/H,KAAKoH,UACLW,IACA/H,KAAKG,KAAK,gBAAgB,aACpB4H,GAASD,QAGd9H,KAAK+D,WACNgE,IACA/H,KAAKG,KAAK,SAAS,aACb4H,GAASD,aAKnBA,OAGR7N,WAAAmK,iBAMIpE,KAAKoH,SAAU,EACfpH,KAAKgI,SACLhI,KAAKgB,aAAa,WAEtB/G,aAAAmK,eAKO/J,GAAM,IAAA4N,QVpFK,SAACC,EAAgB1L,GAGnC,IAFA,IAAM2L,EAAiBD,EAAexM,MAAM+B,GACtCkH,EAAU,GACPzI,EAAI,EAAGA,EAAIiM,EAAehM,OAAQD,IAAK,CAC5C,IAAMkM,EAAgB9L,EAAa6L,EAAejM,GAAIM,GAEtD,GADAmI,EAAQzE,KAAKkI,GACc,UAAvBA,EAAchO,KACd,MAGR,OAAOuK,CACX,EUwFQ0D,CAAchO,EAAM2F,KAAKkE,OAAO1H,YAAYxC,SAd3B,SAAC+D,GAMd,GAJI,YAAckK,EAAK1D,YAA8B,SAAhBxG,EAAO3D,MACxC6N,EAAKK,SAGL,UAAYvK,EAAO3D,KAEnB,OADA6N,EAAKvD,QAAQ,CAAErB,YAAa,oCACrB,EAGX4E,EAAKpD,SAAS9G,MAKd,WAAaiC,KAAKuE,aAElBvE,KAAKoH,SAAU,EACfpH,KAAKgB,aAAa,gBACd,SAAWhB,KAAKuE,YAChBvE,KAAK6H,WAMjB5N,cAAAmK,iBAKU,IAAAmE,OACAC,EAAQ,WACVD,EAAK3D,MAAM,CAAC,CAAExK,KAAM,YAEpB,SAAW4F,KAAKuE,WAChBiE,IAKAxI,KAAKG,KAAK,OAAQqI,MAG1BvO,YAAAmK,eAMMO,GAAS,IAAA8D,OACXzI,KAAK+D,UAAW,EVxJF,SAACY,EAASxJ,GAE5B,IAAMgB,EAASwI,EAAQxI,OACjBgM,EAAiB,IAAIpH,MAAM5E,GAC7BuM,EAAQ,EACZ/D,EAAQ3K,SAAQ,SAAC+D,EAAQ7B,GAErBlB,EAAa+C,GAAQ,GAAO,SAAAxB,GACxB4L,EAAejM,GAAKK,IACdmM,IAAUvM,GACZhB,EAASgN,EAAerB,KAAKrJ,SAI7C,CU2IQkL,CAAchE,GAAS,SAACtK,GACpBoO,EAAKG,QAAQvO,GAAM,WACfoO,EAAK1E,UAAW,EAChB0E,EAAKzH,aAAa,kBAI9B/G,UAAAmK,iBAMI,IAAMY,EAAShF,KAAKmC,KAAKsD,OAAS,QAAU,OACtCxB,EAAQjE,KAAKiE,OAAS,GAQ5B,OANI,IAAUjE,KAAKmC,KAAK0G,oBACpB5E,EAAMjE,KAAKmC,KAAK2G,gBAAkB1C,KAEjCpG,KAAK9E,gBAAmB+I,EAAM8E,MAC/B9E,EAAM+E,IAAM,GAEThJ,KAAKiJ,UAAUjE,EAAQf,MAElChK,cAAAmK,iBAMmB,IAAXjC,EAAI7B,UAAAnE,eAAA8I,IAAA3E,aAAAA,aAAG,GAEX,OADA4I,EAAc/G,EAAM,CAAEqF,GAAIxH,KAAKwH,GAAIG,UAAW3H,KAAK2H,WAAa3H,KAAKmC,MAC9D,IAAIgH,GAAQnJ,KAAKoJ,MAAOjH,MAEnClI,cAAAmK,eAOQ/J,EAAM0F,GAAI,IAAAsJ,OACRC,EAAMtJ,KAAKuJ,QAAQ,CACrBC,OAAQ,OACRnP,KAAMA,IAEViP,EAAI1J,GAAG,UAAWG,GAClBuJ,EAAI1J,GAAG,SAAS,SAAC6J,EAAWnG,GACxB+F,EAAKK,QAAQ,iBAAkBD,EAAWnG,SAGlDrJ,aAAAmK,iBAKS,IAAAuF,OACCL,EAAMtJ,KAAKuJ,UACjBD,EAAI1J,GAAG,OAAQI,KAAK4J,OAAOtH,KAAKtC,OAChCsJ,EAAI1J,GAAG,SAAS,SAAC6J,EAAWnG,GACxBqG,EAAKD,QAAQ,iBAAkBD,EAAWnG,MAE9CtD,KAAK6J,QAAUP,KAClBrP,WAAA6P,eA9KG,MAAO,cACV5C,CAAA,EAjCwBvD,GAgNhBwF,YAAOvF,GAAAX,EAAAkG,EAAAvF,GAAA,IAAAC,EAAAV,EAAAgG,GAOhB,SAAAA,EAAYC,EAAKjH,GAAM,IAAA4H,EAOL,OAPKvG,OAAA2F,GAEnBjH,EAAqB8B,EADrB+F,EAAAlG,EAAAnJ,YAC4ByH,GAC5B4H,EAAK5H,KAAOA,EACZ4H,EAAKP,OAASrH,EAAKqH,QAAU,MAC7BO,EAAKX,IAAMA,EACXW,EAAK1P,UAAO4K,IAAc9C,EAAK9H,KAAO8H,EAAK9H,KAAO,KAClD0P,EAAKlQ,SAASkQ,EA+HjB,OA7HD5F,EAAAgF,IAAAlP,aAAAmK,iBAKS,IACD4F,EADCC,OAEC9H,EAAOZ,EAAKvB,KAAKmC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAKwE,UAAY3G,KAAKmC,KAAKqF,GAC3B,IAAM0C,EAAOlK,KAAKkK,IAAM,IAAI3D,GAAepE,GAC3C,IACI+H,EAAIC,KAAKnK,KAAKwJ,OAAQxJ,KAAKoJ,KAAK,GAChC,IACI,GAAIpJ,KAAKmC,KAAKiI,aAEV,IAAK,IAAIlO,KADTgO,EAAIG,uBAAyBH,EAAIG,uBAAsB,GACzCrK,KAAKmC,KAAKiI,aAChBpK,KAAKmC,KAAKiI,aAAaxI,eAAe1F,IACtCgO,EAAII,iBAAiBpO,EAAG8D,KAAKmC,KAAKiI,aAAalO,IAK/D,MAAO0K,IACP,GAAI,SAAW5G,KAAKwJ,OAChB,IACIU,EAAII,iBAAiB,eAAgB,4BAEzC,MAAO1D,IAEX,IACIsD,EAAII,iBAAiB,SAAU,OAEnC,MAAO1D,IACwB,QAA9BoD,EAAKhK,KAAKmC,KAAKwF,iBAA8B,IAAPqC,GAAyBA,EAAGO,WAAWL,GAE1E,oBAAqBA,IACrBA,EAAIxC,gBAAkB1H,KAAKmC,KAAKuF,iBAEhC1H,KAAKmC,KAAKqI,iBACVN,EAAIO,QAAUzK,KAAKmC,KAAKqI,gBAE5BN,EAAIQ,mBAAqB,WACrB,IAAIV,EACmB,IAAnBE,EAAI3F,aAC2B,QAA9ByF,EAAKC,EAAK9H,KAAKwF,iBAA8B,IAAPqC,GAAyBA,EAAGW,aAAaT,IAEhF,IAAMA,EAAI3F,aAEV,MAAQ2F,EAAIU,QAAU,OAASV,EAAIU,OACnCX,EAAKY,SAKLZ,EAAK5H,cAAa,WACd4H,EAAKP,QAA8B,iBAAfQ,EAAIU,OAAsBV,EAAIU,OAAS,KAC5D,KAGXV,EAAIY,KAAK9K,KAAK3F,MAElB,MAAOuM,GAOH,YAHA5G,KAAKqC,cAAa,WACd4H,EAAKP,QAAQ9C,KACd,GAGiB,oBAAbmE,WACP/K,KAAKgL,MAAQ7B,EAAQ8B,gBACrB9B,EAAQ+B,SAASlL,KAAKgL,OAAShL,SAGvC/F,cAAAmK,eAKQoC,GACJxG,KAAKgB,aAAa,QAASwF,EAAKxG,KAAKkK,KACrClK,KAAKmL,SAAQ,MAEjBlR,cAAAmK,eAKQgH,GACJ,QAAI,IAAuBpL,KAAKkK,KAAO,OAASlK,KAAKkK,IAArD,CAIA,GADAlK,KAAKkK,IAAIQ,mBAAqB3D,GAC1BqE,EACA,IACIpL,KAAKkK,IAAImB,QAEb,MAAOzE,IAEa,oBAAbmE,iBACA5B,EAAQ+B,SAASlL,KAAKgL,OAEjChL,KAAKkK,IAAM,SAEfjQ,aAAAmK,iBAMI,IAAM/J,EAAO2F,KAAKkK,IAAIoB,aACT,OAATjR,IACA2F,KAAKgB,aAAa,OAAQ3G,GAC1B2F,KAAKgB,aAAa,WAClBhB,KAAKmL,cAGblR,YAAAmK,iBAMIpE,KAAKmL,cACRhC,CAAA,EA7IwBzJ,GAsJ7B,GAPAyJ,GAAQ8B,cAAgB,EACxB9B,GAAQ+B,SAAW,GAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,SAEvB,GAAgC,mBAArB3L,iBAAiC,CAE7CA,iBADyB,eAAgBiC,EAAa,WAAa,SAChC0J,IAAe,GAG1D,SAASA,KACL,IAAK,IAAItP,KAAKiN,GAAQ+B,SACd/B,GAAQ+B,SAAStJ,eAAe1F,IAChCiN,GAAQ+B,SAAShP,GAAGmP,OAGhC,CCpYO,IAAMI,GACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAEhE,SAACjL,GAAE,OAAKgL,QAAQC,UAAUzN,KAAKwC,IAG/B,SAACA,EAAI2B,GAAY,OAAKA,EAAa3B,EAAI,IAGzCkL,GAAY9J,EAAW8J,WAAa9J,EAAW+J,aAE/CC,GAAoB,cCN3BC,GAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACTC,YAAEhF,GAAAlE,EAAAkJ,EAAAhF,GAAA,IAAAjE,EAAAC,EAAAgJ,GAOX,SAAAA,EAAYhK,GAAM,IAAAoB,EAE0B,OAF1BC,OAAA2I,IACd5I,EAAAL,EAAAxI,UAAMyH,IACDjH,gBAAkBiH,EAAKsF,YAAYlE,EAI3C,OAHAY,EAAAgI,IAAAlS,aAAAmK,iBAKG,GAAKpE,KAAKoM,QAAV,CAIA,IAAMhD,EAAMpJ,KAAKoJ,MACXiD,EAAYrM,KAAKmC,KAAKkK,UAEtBlK,EAAO4J,GACP,GACAxK,EAAKvB,KAAKmC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMnC,KAAKmC,KAAKiI,eACVjI,EAAKmK,QAAUtM,KAAKmC,KAAKiI,cAE7B,IACIpK,KAAKuM,GACyBR,GAIpB,IAAIH,GAAUxC,EAAKiD,EAAWlK,GAH9BkK,EACI,IAAIT,GAAUxC,EAAKiD,GACnB,IAAIT,GAAUxC,GAGhC,MAAO5C,GACH,OAAOxG,KAAKgB,aAAa,QAASwF,GAEtCxG,KAAKuM,GAAG/P,WAAawD,KAAKkE,OAAO1H,WACjCwD,KAAKwM,wBAETvS,wBAAAmK,iBAKoB,IAAAN,OAChB9D,KAAKuM,GAAGE,OAAS,WACT3I,EAAK3B,KAAKuK,WACV5I,EAAKyI,GAAGI,QAAQC,QAEpB9I,EAAKwE,UAETtI,KAAKuM,GAAGM,QAAU,SAACC,GAAU,OAAKhJ,EAAKY,QAAQ,CAC3CrB,YAAa,8BACbC,QAASwJ,KAEb9M,KAAKuM,GAAGQ,UAAY,SAACC,GAAE,OAAKlJ,EAAK8F,OAAOoD,EAAG3S,OAC3C2F,KAAKuM,GAAGU,QAAU,SAACrG,GAAC,OAAK9C,EAAK4F,QAAQ,kBAAmB9C,OAC5D3M,YAAAmK,eACKO,GAAS,IAAAsD,OACXjI,KAAK+D,UAAW,EAGhB,IADA,IAAAmJ,WACShR,GACL,IAAM6B,EAAS4G,EAAQzI,GACjBiR,EAAajR,IAAMyI,EAAQxI,OAAS,EAC1CnB,EAAa+C,EAAQkK,EAAK/M,gBAAgB,SAACb,GAmBvC,IAGQ4N,EAAKsE,GAAGzB,KAAKzQ,GAMrB,MAAOuM,IAEHuG,GAGA1B,IAAS,WACLxD,EAAKlE,UAAW,EAChBkE,EAAKjH,aAAa,WACnBiH,EAAK5F,kBAvCXnG,EAAI,EAAGA,EAAIyI,EAAQxI,OAAQD,IAAKgR,EAAhChR,MA2CZjC,cAAAmK,sBAE0B,IAAZpE,KAAKuM,KACZvM,KAAKuM,GAAG/D,QACRxI,KAAKuM,GAAK,SAGlBtS,UAAAmK,iBAMI,IAAMY,EAAShF,KAAKmC,KAAKsD,OAAS,MAAQ,KACpCxB,EAAQjE,KAAKiE,OAAS,GAS5B,OAPIjE,KAAKmC,KAAK0G,oBACV5E,EAAMjE,KAAKmC,KAAK2G,gBAAkB1C,KAGjCpG,KAAK9E,iBACN+I,EAAM+E,IAAM,GAEThJ,KAAKiJ,UAAUjE,EAAQf,MAElChK,YAAAmK,iBAOI,QAASwH,MACZ3R,WAAA6P,eAjIG,MAAO,gBACVqC,CAAA,EAbmBxI,GCNXyJ,YAAEjG,GAAAlE,EAAAmK,EAAAjG,GAAA,IAAAjE,EAAAC,EAAAiK,GAAA,SAAAA,IAAA,OAAA5J,OAAA4J,GAAAlK,EAAA7C,WAAAC,WAGV,OAHU6D,EAAAiJ,IAAAnT,aAAAmK,iBAIF,IAAAb,OAEuB,mBAAjB8J,eAIXrN,KAAKsN,UAAY,IAAID,aAAarN,KAAKiJ,UAAU,SAAUjJ,KAAKmC,KAAKoL,iBAAiBvN,KAAKwN,OAC3FxN,KAAKsN,UAAUG,OACVvP,MAAK,WACNqF,EAAKmB,oBAEE,SAAC8B,GACRjD,EAAKmG,QAAQ,qBAAsBlD,MAGvCxG,KAAKsN,UAAUI,MAAMxP,MAAK,WACtBqF,EAAK+J,UAAUK,4BAA4BzP,MAAK,SAAC0P,GAC7C,IAAMC,Eb8Df,SAAmCC,EAAYtR,GAC7CH,IACDA,EAAe,IAAI0R,aAEvB,IAAM9O,EAAS,GACX+O,EAAQ,EACRC,GAAkB,EAClBC,GAAW,EACf,OAAO,IAAIrQ,gBAAgB,CACvBC,mBAAUsB,EAAOpB,GAEb,IADAiB,EAAOiB,KAAKd,KACC,CACT,GAAc,IAAV4O,EAA+B,CAC/B,GAAIhP,EAAYC,GAAU,EACtB,MAEJ,IAAMV,EAASc,EAAaJ,EAAQ,GACpCiP,EAAkC,MAAV,IAAZ3P,EAAO,IACnB0P,EAA6B,IAAZ1P,EAAO,GAEpByP,EADAC,EAAiB,IACT,EAEgB,MAAnBA,EACG,EAGA,OAGX,GAAc,IAAVD,EAA2C,CAChD,GAAIhP,EAAYC,GAAU,EACtB,MAEJ,IAAMkP,EAAc9O,EAAaJ,EAAQ,GACzCgP,EAAiB,IAAIxP,SAAS0P,EAAYpT,OAAQoT,EAAYrS,WAAYqS,EAAYhS,QAAQiS,UAAU,GACxGJ,EAAQ,OAEP,GAAc,IAAVA,EAA2C,CAChD,GAAIhP,EAAYC,GAAU,EACtB,MAEJ,IAAMkP,EAAc9O,EAAaJ,EAAQ,GACnCN,EAAO,IAAIF,SAAS0P,EAAYpT,OAAQoT,EAAYrS,WAAYqS,EAAYhS,QAC5EkS,EAAI1P,EAAK2P,UAAU,GACzB,GAAID,EAAInI,KAAKqI,IAAI,EAAG,IAAW,EAAG,CAE9BvQ,EAAWe,QAAQ5E,GACnB,MAEJ8T,EAAiBI,EAAInI,KAAKqI,IAAI,EAAG,IAAM5P,EAAK2P,UAAU,GACtDN,EAAQ,MAEP,CACD,GAAIhP,EAAYC,GAAUgP,EACtB,MAEJ,IAAM5T,EAAOgF,EAAaJ,EAAQgP,GAClCjQ,EAAWe,QAAQzC,EAAa4R,EAAW7T,EAAOgC,EAAamB,OAAOnD,GAAOmC,IAC7EwR,EAAQ,EAEZ,GAAuB,IAAnBC,GAAwBA,EAAiBH,EAAY,CACrD9P,EAAWe,QAAQ5E,GACnB,UAKpB,CajIsCqU,CAA0B9I,OAAO+I,iBAAkBlL,EAAKW,OAAO1H,YAC/EkS,EAASd,EAAOe,SAASC,YAAYf,GAAegB,YACpDC,EAAgBlR,IACtBkR,EAAcH,SAASI,OAAOnB,EAAO7J,UACrCR,EAAKyL,OAASF,EAAc/K,SAASkL,aACxB,SAAPC,IACFR,EACKQ,OACAhR,MAAK,SAAAjD,GAAqB,IAAlBkU,EAAIlU,EAAJkU,KAAM/K,EAAKnJ,EAALmJ,MACX+K,IAGJ5L,EAAKsB,SAAST,GACd8K,eAEO,SAAC1I,OAGhB0I,GACA,IAAMnR,EAAS,CAAE3D,KAAM,QACnBmJ,EAAKU,MAAM8E,MACXhL,EAAO1D,gBAAIwM,OAActD,EAAKU,MAAM8E,WAExCxF,EAAKyL,OAAOpK,MAAM7G,GAAQG,MAAK,WAAA,OAAMqF,EAAK+E,sBAGrDrO,YAAAmK,eACKO,GAAS,IAAAb,OACX9D,KAAK+D,UAAW,EAChB,IADsB,IAAAmJ,WACbhR,GACL,IAAM6B,EAAS4G,EAAQzI,GACjBiR,EAAajR,IAAMyI,EAAQxI,OAAS,EAC1C2H,EAAKkL,OAAOpK,MAAM7G,GAAQG,MAAK,WACvBiP,GACA1B,IAAS,WACL3H,EAAKC,UAAW,EAChBD,EAAK9C,aAAa,WACnB8C,EAAKzB,kBARXnG,EAAI,EAAGA,EAAIyI,EAAQxI,OAAQD,IAAKgR,EAAhChR,MAYZjC,cAAAmK,iBAEG,IAAI4F,EACsB,QAAzBA,EAAKhK,KAAKsN,iBAA8B,IAAPtD,GAAyBA,EAAGxB,WACjEvO,WAAA6P,eAhEG,MAAO,mBACVsD,CAAA,EAHmBzJ,GCAXyL,GAAa,CACtBC,UAAWlD,GACXmD,aAAclC,GACdhG,QAASF,ICaPqI,GAAK,sPACLC,GAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,GAAM7J,GAClB,GAAIA,EAAIzJ,OAAS,IACb,KAAM,eAEV,IAAMuT,EAAM9J,EAAK+J,EAAI/J,EAAIL,QAAQ,KAAMqB,EAAIhB,EAAIL,QAAQ,MAC7C,GAANoK,IAAiB,GAAN/I,IACXhB,EAAMA,EAAIhJ,UAAU,EAAG+S,GAAK/J,EAAIhJ,UAAU+S,EAAG/I,GAAGgJ,QAAQ,KAAM,KAAOhK,EAAIhJ,UAAUgK,EAAGhB,EAAIzJ,SAG9F,IADA,IAwBmB8H,EACb5J,EAzBFwV,EAAIN,GAAGO,KAAKlK,GAAO,IAAKwD,EAAM,GAAIlN,EAAI,GACnCA,KACHkN,EAAIoG,GAAMtT,IAAM2T,EAAE3T,IAAM,GAU5B,OARU,GAANyT,IAAiB,GAAN/I,IACXwC,EAAI2G,OAASL,EACbtG,EAAI4G,KAAO5G,EAAI4G,KAAKpT,UAAU,EAAGwM,EAAI4G,KAAK7T,OAAS,GAAGyT,QAAQ,KAAM,KACpExG,EAAI6G,UAAY7G,EAAI6G,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9ExG,EAAI8G,SAAU,GAElB9G,EAAI+G,UAIR,SAAmBrV,EAAKsK,GACpB,IAAMgL,EAAO,WAAYC,EAAQjL,EAAKwK,QAAQQ,EAAM,KAAK1U,MAAM,KACvC,KAApB0J,EAAK3F,MAAM,EAAG,IAA6B,IAAhB2F,EAAKjJ,QAChCkU,EAAMzP,OAAO,EAAG,GAEE,KAAlBwE,EAAK3F,OAAO,IACZ4Q,EAAMzP,OAAOyP,EAAMlU,OAAS,EAAG,GAEnC,OAAOkU,CACX,CAboBF,CAAU/G,EAAKA,EAAU,MACzCA,EAAIkH,UAaerM,EAbUmF,EAAW,MAclC/O,EAAO,GACb4J,EAAM2L,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACAnW,EAAKmW,GAAMC,MAGZpW,GAnBA+O,CACX,KCrCasH,YAAM9M,GAAAX,EAAAyN,EAAA9M,GAAA,IAAAV,EAAAC,EAAAuN,GAOf,SAAAA,EAAYtH,GAAgB,IAAA7F,EAAXpB,EAAI7B,UAAAnE,eAAA8I,IAAA3E,aAAAA,aAAG,GAgGR,OAhGUkD,OAAAkN,IACtBnN,EAAAL,EAAAxI,YACK8B,WAAasP,GAClBvI,EAAKoN,YAAc,GACfvH,GAAO,WAAQwH,EAAYxH,KAC3BjH,EAAOiH,EACPA,EAAM,MAENA,GACAA,EAAMqG,GAAMrG,GACZjH,EAAKmD,SAAW8D,EAAI4G,KACpB7N,EAAKsD,OAA0B,UAAjB2D,EAAI7B,UAAyC,QAAjB6B,EAAI7B,SAC9CpF,EAAKqD,KAAO4D,EAAI5D,KACZ4D,EAAInF,QACJ9B,EAAK8B,MAAQmF,EAAInF,QAEhB9B,EAAK6N,OACV7N,EAAKmD,SAAWmK,GAAMtN,EAAK6N,MAAMA,MAErC9N,EAAqB8B,EAAAT,GAAOpB,GAC5BoB,EAAKkC,OACD,MAAQtD,EAAKsD,OACPtD,EAAKsD,OACe,oBAAb4B,UAA4B,WAAaA,SAASE,SAC/DpF,EAAKmD,WAAanD,EAAKqD,OAEvBrD,EAAKqD,KAAOjC,EAAKkC,OAAS,MAAQ,MAEtClC,EAAK+B,SACDnD,EAAKmD,WACoB,oBAAb+B,SAA2BA,SAAS/B,SAAW,aAC/D/B,EAAKiC,KACDrD,EAAKqD,OACoB,oBAAb6B,UAA4BA,SAAS7B,KACvC6B,SAAS7B,KACTjC,EAAKkC,OACD,MACA,MAClBlC,EAAK6L,WAAajN,EAAKiN,YAAc,CACjC,UACA,YACA,gBAEJ7L,EAAKoN,YAAc,GACnBpN,EAAKsN,cAAgB,EACrBtN,EAAKpB,KAAO+G,EAAc,CACtB9D,KAAM,aACN0L,OAAO,EACPpJ,iBAAiB,EACjBqJ,SAAS,EACTjI,eAAgB,IAChBkI,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEf7D,iBAAkB,GAClB8D,qBAAqB,GACtBlP,GACHoB,EAAKpB,KAAKiD,KACN7B,EAAKpB,KAAKiD,KAAKwK,QAAQ,MAAO,KACzBrM,EAAKpB,KAAK8O,iBAAmB,IAAM,IACb,iBAApB1N,EAAKpB,KAAK8B,QACjBV,EAAKpB,KAAK8B,MAAQzG,EAAO+F,EAAKpB,KAAK8B,QAGvCV,EAAK+N,GAAK,KACV/N,EAAKgO,SAAW,KAChBhO,EAAKiO,aAAe,KACpBjO,EAAKkO,YAAc,KAEnBlO,EAAKmO,iBAAmB,KACQ,mBAArB7R,mBACH0D,EAAKpB,KAAKkP,sBAIV9N,EAAKoO,0BAA4B,WACzBpO,EAAK+J,YAEL/J,EAAK+J,UAAU9M,qBACf+C,EAAK+J,UAAU9E,UAGvB3I,iBAAiB,eAAgB0D,EAAKoO,2BAA2B,IAE/C,cAAlBpO,EAAK+B,WACL/B,EAAKqO,qBAAuB,WACxBrO,EAAKmB,QAAQ,kBAAmB,CAC5BrB,YAAa,6BAGrBxD,iBAAiB,UAAW0D,EAAKqO,sBAAsB,KAG/DrO,EAAK4G,OAAO5G,EAief,OA/dDY,EAAAuM,IAAAzW,sBAAAmK,eAOgBoJ,GACZ,IAAMvJ,EAAQiF,EAAc,GAAIlJ,KAAKmC,KAAK8B,OAE1CA,EAAM4N,IhBgCU,EgB9BhB5N,EAAMqJ,UAAYE,EAEdxN,KAAKsR,KACLrN,EAAM8E,IAAM/I,KAAKsR,IACrB,IAAMnP,EAAO+G,EAAc,GAAIlJ,KAAKmC,KAAM,CACtC8B,MAAAA,EACAC,OAAQlE,KACRsF,SAAUtF,KAAKsF,SACfG,OAAQzF,KAAKyF,OACbD,KAAMxF,KAAKwF,MACZxF,KAAKmC,KAAKoL,iBAAiBC,IAC9B,OAAO,IAAI4B,GAAW5B,GAAMrL,MAEhClI,WAAAmK,iBAKO,IACCkJ,EADDxJ,OAEH,GAAI9D,KAAKmC,KAAK6O,iBACVN,EAAOoB,wBACmC,IAA1C9R,KAAKoP,WAAW7J,QAAQ,aACxB+H,EAAY,gBAEX,IAAI,IAAMtN,KAAKoP,WAAWjT,OAK3B,YAHA6D,KAAKqC,cAAa,WACdyB,EAAK9C,aAAa,QAAS,6BAC5B,GAIHsM,EAAYtN,KAAKoP,WAAW,GAEhCpP,KAAKuE,WAAa,UAElB,IACI+I,EAAYtN,KAAK+R,gBAAgBzE,GAErC,MAAO1G,GAGH,OAFA5G,KAAKoP,WAAW7P,aAChBS,KAAKmK,OAGTmD,EAAUnD,OACVnK,KAAKgS,aAAa1E,MAEtBrT,mBAAAmK,eAKakJ,GAAW,IAAArF,OAChBjI,KAAKsN,WACLtN,KAAKsN,UAAU9M,qBAGnBR,KAAKsN,UAAYA,EAEjBA,EACK1N,GAAG,QAASI,KAAKiS,QAAQ3P,KAAKtC,OAC9BJ,GAAG,SAAUI,KAAK6E,SAASvC,KAAKtC,OAChCJ,GAAG,QAASI,KAAK0J,QAAQpH,KAAKtC,OAC9BJ,GAAG,SAAS,SAACwD,GAAM,OAAK6E,EAAKvD,QAAQ,kBAAmBtB,SAEjEnJ,YAAAmK,eAMMoJ,GAAM,IAAAjF,OACJ+E,EAAYtN,KAAK+R,gBAAgBvE,GACjC0E,GAAS,EACbxB,EAAOoB,uBAAwB,EAC/B,IAAMK,EAAkB,WAChBD,IAEJ5E,EAAUxC,KAAK,CAAC,CAAE1Q,KAAM,OAAQC,KAAM,WACtCiT,EAAUnN,KAAK,UAAU,SAACiS,GACtB,IAAIF,EAEJ,GAAI,SAAWE,EAAIhY,MAAQ,UAAYgY,EAAI/X,KAAM,CAG7C,GAFAkO,EAAK8J,WAAY,EACjB9J,EAAKvH,aAAa,YAAasM,IAC1BA,EACD,OACJoD,EAAOoB,sBAAwB,cAAgBxE,EAAUE,KACzDjF,EAAK+E,UAAUxF,OAAM,WACboK,GAEA,WAAa3J,EAAKhE,aAEtB4G,IACA5C,EAAKyJ,aAAa1E,GAClBA,EAAUxC,KAAK,CAAC,CAAE1Q,KAAM,aACxBmO,EAAKvH,aAAa,UAAWsM,GAC7BA,EAAY,KACZ/E,EAAK8J,WAAY,EACjB9J,EAAK+J,gBAGR,CACD,IAAM9L,EAAM,IAAI9C,MAAM,eAEtB8C,EAAI8G,UAAYA,EAAUE,KAC1BjF,EAAKvH,aAAa,eAAgBwF,SAI9C,SAAS+L,IACDL,IAGJA,GAAS,EACT/G,IACAmC,EAAU9E,QACV8E,EAAY,MAGhB,IAAML,EAAU,SAACzG,GACb,IAAMgM,EAAQ,IAAI9O,MAAM,gBAAkB8C,GAE1CgM,EAAMlF,UAAYA,EAAUE,KAC5B+E,IACAhK,EAAKvH,aAAa,eAAgBwR,IAEtC,SAASC,IACLxF,EAAQ,oBAGZ,SAASJ,IACLI,EAAQ,iBAGZ,SAASyF,EAAUC,GACXrF,GAAaqF,EAAGnF,OAASF,EAAUE,MACnC+E,IAIR,IAAMpH,EAAU,WACZmC,EAAU/M,eAAe,OAAQ4R,GACjC7E,EAAU/M,eAAe,QAAS0M,GAClCK,EAAU/M,eAAe,QAASkS,GAClClK,EAAKnI,IAAI,QAASyM,GAClBtE,EAAKnI,IAAI,YAAasS,IAE1BpF,EAAUnN,KAAK,OAAQgS,GACvB7E,EAAUnN,KAAK,QAAS8M,GACxBK,EAAUnN,KAAK,QAASsS,GACxBzS,KAAKG,KAAK,QAAS0M,GACnB7M,KAAKG,KAAK,YAAauS,IACwB,IAA3C1S,KAAKuR,SAAShM,QAAQ,iBACb,iBAATiI,EAEAxN,KAAKqC,cAAa,WACT6P,GACD5E,EAAUnD,SAEf,KAGHmD,EAAUnD,UAGlBlQ,aAAAmK,iBAYI,GANApE,KAAKuE,WAAa,OAClBmM,EAAOoB,sBAAwB,cAAgB9R,KAAKsN,UAAUE,KAC9DxN,KAAKgB,aAAa,QAClBhB,KAAKsS,QAGD,SAAWtS,KAAKuE,YAAcvE,KAAKmC,KAAK4O,QAGxC,IAFA,IAAI7U,EAAI,EACFyG,EAAI3C,KAAKuR,SAASpV,OACjBD,EAAIyG,EAAGzG,IACV8D,KAAK4S,MAAM5S,KAAKuR,SAASrV,OAIrCjC,eAAAmK,eAKSrG,GACL,GAAI,YAAciC,KAAKuE,YACnB,SAAWvE,KAAKuE,YAChB,YAAcvE,KAAKuE,WAKnB,OAJAvE,KAAKgB,aAAa,SAAUjD,GAE5BiC,KAAKgB,aAAa,aAClBhB,KAAK6S,mBACG9U,EAAO3D,MACX,IAAK,OACD4F,KAAK8S,YAAYC,KAAKtD,MAAM1R,EAAO1D,OACnC,MACJ,IAAK,OACD2F,KAAKgT,WAAW,QAChBhT,KAAKgB,aAAa,QAClBhB,KAAKgB,aAAa,QAClB,MACJ,IAAK,QACD,IAAMwF,EAAM,IAAI9C,MAAM,gBAEtB8C,EAAIyM,KAAOlV,EAAO1D,KAClB2F,KAAK0J,QAAQlD,GACb,MACJ,IAAK,UACDxG,KAAKgB,aAAa,OAAQjD,EAAO1D,MACjC2F,KAAKgB,aAAa,UAAWjD,EAAO1D,UAOpDJ,kBAAAmK,eAMY/J,GACR2F,KAAKgB,aAAa,YAAa3G,GAC/B2F,KAAKsR,GAAKjX,EAAK0O,IACf/I,KAAKsN,UAAUrJ,MAAM8E,IAAM1O,EAAK0O,IAChC/I,KAAKuR,SAAWvR,KAAKkT,eAAe7Y,EAAKkX,UACzCvR,KAAKwR,aAAenX,EAAKmX,aACzBxR,KAAKyR,YAAcpX,EAAKoX,YACxBzR,KAAK8N,WAAazT,EAAKyT,WACvB9N,KAAKsI,SAED,WAAatI,KAAKuE,YAEtBvE,KAAK6S,sBAET5Y,uBAAAmK,iBAKmB,IAAAqE,OACfzI,KAAKuC,eAAevC,KAAK0R,kBACzB1R,KAAK0R,iBAAmB1R,KAAKqC,cAAa,WACtCoG,EAAK/D,QAAQ,kBACd1E,KAAKwR,aAAexR,KAAKyR,aACxBzR,KAAKmC,KAAKuK,WACV1M,KAAK0R,iBAAiB9E,WAG9B3S,cAAAmK,iBAMIpE,KAAK2Q,YAAY/P,OAAO,EAAGZ,KAAK6Q,eAIhC7Q,KAAK6Q,cAAgB,EACjB,IAAM7Q,KAAK2Q,YAAYxU,OACvB6D,KAAKgB,aAAa,SAGlBhB,KAAKsS,WAGbrY,YAAAmK,iBAMI,GAAI,WAAapE,KAAKuE,YAClBvE,KAAKsN,UAAUvJ,WACd/D,KAAKqS,WACNrS,KAAK2Q,YAAYxU,OAAQ,CACzB,IAAMwI,EAAU3E,KAAKmT,qBACrBnT,KAAKsN,UAAUxC,KAAKnG,GAGpB3E,KAAK6Q,cAAgBlM,EAAQxI,OAC7B6D,KAAKgB,aAAa,aAG1B/G,yBAAAmK,iBAUI,KAH+BpE,KAAK8N,YACR,YAAxB9N,KAAKsN,UAAUE,MACfxN,KAAK2Q,YAAYxU,OAAS,GAE1B,OAAO6D,KAAK2Q,YAGhB,IADA,IZtZmB7V,EYsZfsY,EAAc,EACTlX,EAAI,EAAGA,EAAI8D,KAAK2Q,YAAYxU,OAAQD,IAAK,CAC9C,IAAM7B,EAAO2F,KAAK2Q,YAAYzU,GAAG7B,KAIjC,GAHIA,IACA+Y,GZzZO,iBADItY,EY0ZeT,GZnZ1C,SAAoBuL,GAEhB,IADA,IAAIyN,EAAI,EAAGlX,EAAS,EACXD,EAAI,EAAGyG,EAAIiD,EAAIzJ,OAAQD,EAAIyG,EAAGzG,KACnCmX,EAAIzN,EAAIxJ,WAAWF,IACX,IACJC,GAAU,EAELkX,EAAI,KACTlX,GAAU,EAELkX,EAAI,OAAUA,GAAK,MACxBlX,GAAU,GAGVD,IACAC,GAAU,GAGlB,OAAOA,CACX,CAxBemX,CAAWxY,GAGfoL,KAAKqN,KAPQ,MAOFzY,EAAIiB,YAAcjB,EAAIwE,QYuZ5BpD,EAAI,GAAKkX,EAAcpT,KAAK8N,WAC5B,OAAO9N,KAAK2Q,YAAYlR,MAAM,EAAGvD,GAErCkX,GAAe,EAEnB,OAAOpT,KAAK2Q,eAEhB1W,YAAAmK,eAQMgO,EAAKoB,EAASzT,GAEhB,OADAC,KAAKgT,WAAW,UAAWZ,EAAKoB,EAASzT,GAClCC,QACV/F,WAAAmK,eACIgO,EAAKoB,EAASzT,GAEf,OADAC,KAAKgT,WAAW,UAAWZ,EAAKoB,EAASzT,GAClCC,QAEX/F,iBAAAmK,eASWhK,EAAMC,EAAMmZ,EAASzT,GAS5B,GARI,mBAAsB1F,IACtB0F,EAAK1F,EACLA,OAAO4K,GAEP,mBAAsBuO,IACtBzT,EAAKyT,EACLA,EAAU,MAEV,YAAcxT,KAAKuE,YAAc,WAAavE,KAAKuE,WAAvD,EAGAiP,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,IAAM1V,EAAS,CACX3D,KAAMA,EACNC,KAAMA,EACNmZ,QAASA,GAEbxT,KAAKgB,aAAa,eAAgBjD,GAClCiC,KAAK2Q,YAAYzQ,KAAKnC,GAClBgC,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAKsS,YAETrY,YAAAmK,iBAGQ,IAAAiF,OACEb,EAAQ,WACVa,EAAK3E,QAAQ,gBACb2E,EAAKiE,UAAU9E,SAEbkL,EAAkB,SAAlBA,IACFrK,EAAKjJ,IAAI,UAAWsT,GACpBrK,EAAKjJ,IAAI,eAAgBsT,GACzBlL,KAEEmL,EAAiB,WAEnBtK,EAAKlJ,KAAK,UAAWuT,GACrBrK,EAAKlJ,KAAK,eAAgBuT,IAqB9B,MAnBI,YAAc1T,KAAKuE,YAAc,SAAWvE,KAAKuE,aACjDvE,KAAKuE,WAAa,UACdvE,KAAK2Q,YAAYxU,OACjB6D,KAAKG,KAAK,SAAS,WACXkJ,EAAKgJ,UACLsB,IAGAnL,OAIHxI,KAAKqS,UACVsB,IAGAnL,KAGDxI,QAEX/F,cAAAmK,eAKQoC,GACJkK,EAAOoB,uBAAwB,EAC/B9R,KAAKgB,aAAa,QAASwF,GAC3BxG,KAAK0E,QAAQ,kBAAmB8B,MAEpCvM,cAAAmK,eAKQhB,EAAQC,GACR,YAAcrD,KAAKuE,YACnB,SAAWvE,KAAKuE,YAChB,YAAcvE,KAAKuE,aAEnBvE,KAAKuC,eAAevC,KAAK0R,kBAEzB1R,KAAKsN,UAAU9M,mBAAmB,SAElCR,KAAKsN,UAAU9E,QAEfxI,KAAKsN,UAAU9M,qBACoB,mBAAxBC,sBACPA,oBAAoB,eAAgBT,KAAK2R,2BAA2B,GACpElR,oBAAoB,UAAWT,KAAK4R,sBAAsB,IAG9D5R,KAAKuE,WAAa,SAElBvE,KAAKsR,GAAK,KAEVtR,KAAKgB,aAAa,QAASoC,EAAQC,GAGnCrD,KAAK2Q,YAAc,GACnB3Q,KAAK6Q,cAAgB,MAG7B5W,qBAAAmK,eAMemN,GAIX,IAHA,IAAMqC,EAAmB,GACrB1X,EAAI,EACFsD,EAAI+R,EAASpV,OACZD,EAAIsD,EAAGtD,KACL8D,KAAKoP,WAAW7J,QAAQgM,EAASrV,KAClC0X,EAAiB1T,KAAKqR,EAASrV,IAEvC,OAAO0X,MACVlD,CAAA,EAxkBuBhR,GA0kB5BgR,GAAOnJ,ShBvbiB,kBiBzJR6B,EAAKjH,GAAI,OAAK,IAAIuO,GAAOtH,EAAKjH"}