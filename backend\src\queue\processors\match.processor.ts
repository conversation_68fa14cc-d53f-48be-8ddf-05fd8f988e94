import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Logger } from "@nestjs/common";
import { Job } from "bullmq";
import { QUEUE_MATCH_NAME } from "src/constants/queue";
import { MatchService } from "src/match/match.service";
import { UserService } from "src/user/user.service";

export enum MatchJobName {
  MATCHMAKING = 'matchmaking'
}

@Processor(QUEUE_MATCH_NAME)
export class MatchProcessor extends WorkerHost {
  private readonly logger = new Logger(MatchProcessor.name);

  constructor(
    private readonly matchService: MatchService,
    private readonly userService: UserService
  ) {
    super()
  }

  async process(job: Job<any, any, string>): Promise<any> {
    try {
      // Get user id from job data
      await job.log(`Getting user ${job.data.userId}`);
      const user1 = await this.userService.findOne(job.data.userId);
      
      // Find users with completed personality tests
      await job.log('Finding users with completed personality tests');
      const users = await this.userService.findUsersWithCompletedPersonalityTest();

      let count = 0;
      for (const user of users) {
        if (user.gender === user1.gender || user.id === user1.id) {
          continue;
        }

        await job.log(`Making a match to ${user.name} (${user.id})`);
        await this.matchService.createMatch(job.data.userId);
        count++;
      }

      await job.log(`Matchmaking processed successfully for ${user1.name} (${user1.id}) to ${count} users`);

      return { success: true, message: 'Matchmaking processed successfully' };
    } catch (error) {
      this.logger.error(`Failed to process matchmaking job: ${error.message}`, error.stack);
      throw error;
    }
  }
}
