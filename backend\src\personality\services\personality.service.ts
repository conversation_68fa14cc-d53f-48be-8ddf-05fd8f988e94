import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { BfiAnswer, PersonalityTest, PersonalityTestDocument, SelfDisclosureAnswer } from '../schemas/personality-test.schema';
import { BFI_DIMENSIONS, SELF_DISCLOSURE_CATEGORIES } from '../../constants/personality-questions.constant';
import { QuestionService } from './question.service';
import { BfiAnswers, BfiScores, SelfDisclosureAnswers, SubmitPersonalityTestDto } from '../dto/personality-test.dto';
import { QuestionsResponseDto } from '../dto/question-response.dto';
import { UserService } from 'src/user/user.service';
import { EVENT_PERSONALITY_TEST_COMPLETED } from 'src/constants/event-emitter';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class PersonalityService {
  private readonly logger = new Logger(PersonalityService.name);

  constructor(
    @InjectModel(PersonalityTest.name) 
    private readonly personalityTestModel: Model<PersonalityTestDocument>,
    private readonly questionService: QuestionService,
    private readonly userService: UserService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  /**
   * Submit personality test
   * 
   * @param userId 
   * @param submitDto 
   * @returns 
   */
  async submitTest(userId: string, submitDto: SubmitPersonalityTestDto) {
    const userIdObj = new Types.ObjectId(userId);
    
    const [bfiQuestions, sdQuestions] = await Promise.all([
      this.questionService.findAllBfiQuestions(),
      this.questionService.findAllSelfDisclosureQuestions(),
    ]);
    
    let testResult: PersonalityTestDocument | null = null;

    const bfiScores = await this.calculateBfiScores(submitDto.bfiAnswers);
    const selfDisclosureScore = await this.calculateSelfDisclosureScore(submitDto.selfDisclosureAnswers);
    
    const existingTest = await this.personalityTestModel.findOne({ userId: new Types.ObjectId(userId) }).exec();
    
    if (existingTest) {
      // Update existing test
      testResult = await this.personalityTestModel.findByIdAndUpdate(
        existingTest._id,
        { 
          bfi: bfiScores,
          selfDisclosure: selfDisclosureScore,
          updatedAt: new Date()
        },
        { new: true }
      ).exec();
    } else {
      // Create new test
      const createdTest = new this.personalityTestModel({
        userId: userIdObj,
        bfi: bfiScores,
        selfDisclosure: selfDisclosureScore
      });
      testResult = await createdTest.save();
    }

    const bfiAnswers: BfiAnswer[] = [];
    const selfDisclosureAnswers: SelfDisclosureAnswer[] = [];

    // Map BFI answers
    for (const [questionId, value] of Object.entries(submitDto.bfiAnswers)) {
      const question = bfiQuestions.find(q => q.questionId === questionId);
      if (question && value) {
        bfiAnswers.push({
          questionId,
          value,
          dimension: question.dimension
        });
      }
    }

    // Map Self-Disclosure answers
    for (const [questionId, value] of Object.entries(submitDto.selfDisclosureAnswers)) {
      const question = sdQuestions.find(q => q.questionId === questionId);
      if (question && value && question.category) {
        selfDisclosureAnswers.push({
          questionId,
          value,
          category: question.category
        });
      }
    }

    if (!testResult) {
      throw new BadRequestException('Test not found');
    } else {
      testResult.bfiAnswers = bfiAnswers;
      testResult.selfDisclosureAnswers = selfDisclosureAnswers;

      await testResult.save();
    }

    // Mark user as personality test completed
    await this.userService.markUserAsPersonalityTestCompleted(userId);

    // Emit event
    this.eventEmitter.emit(EVENT_PERSONALITY_TEST_COMPLETED, testResult);

    return testResult;
  }

  /**
   * Calculate BFI scores
   * 
   * @param answers 
   * @returns 
   */
  private async calculateBfiScores(answers: BfiAnswers): Promise<BfiScores> {
    // Get all BFI questions
    const bfiQuestions = await this.questionService.findAllBfiQuestions();

    // Initialize domain scores
    const domainScores: Record<string, number[]> = {
      extraversion: [],
      agreeableness: [],
      conscientiousness: [],
      negativeEmotionality: [],
      openMindedness: [],
    };

    // Calculate domain scores
    bfiQuestions.forEach((question) => {
      const answer = answers[question.questionId];
      this.logger.log(`Answer for question ${question.questionId}: ${answer}`);
      if (answer !== undefined && question.dimension) { // Ensure answer exists and dimension is defined
        domainScores[question.dimension].push(answer);
      }
    });

    // Calculate final scores
    const finalScores: Partial<BfiScores> = {};

    for (const dimension in domainScores) {
      const scores = domainScores[dimension];

      if (scores.length > 0) {
        const sum = scores.reduce((acc, val) => acc + val, 0);
        const average = sum / scores.length;

        // Normalize: (avg - min_scale) / (max_scale - min_scale)
        finalScores[dimension] = (average - 1) / 4;
      } else {
        // Default if no answers for a dimension
        finalScores[dimension] = 0;
      }
    }

    return finalScores as BfiScores;
  }

  /**
   * Calculate self disclosure score
   * 
   * @param answers 
   * @returns 
   */
  private async calculateSelfDisclosureScore(answers: SelfDisclosureAnswers): Promise<number> {
    const sdQuestions = await this.questionService.findAllSelfDisclosureQuestions();

    let totalScore = 0;
    let count = 0;

    sdQuestions.forEach((question) => {
      const answer = answers[question.questionId];

      // Ensure answer exists
      if (answer !== undefined) {
        totalScore += answer;
        count++;
      }
    });

    // Avoid division by zero
    if (count === 0) return 0;

    const averageScore = totalScore / count;
    // Normalize: (avg - min_scale) / (max_scale - min_scale)
    // Min scale = 1, Max scale = 5, so (avg - 1) / (5 - 1)
    return (averageScore - 1) / 4;
  }

  /**
   * Get all personality test questions
   * 
   * @returns 
   */
  async getQuestions(): Promise<QuestionsResponseDto> {
    const [bfiQuestions, sdQuestions] = await Promise.all([
      this.questionService.findAllBfiQuestions(),
      this.questionService.findAllSelfDisclosureQuestions(),
    ]);

    const bfiDimensionsWithQuestions = BFI_DIMENSIONS.map(dimension => ({
      ...dimension,
      questions: bfiQuestions
        .filter(q => q.dimension === dimension.id)
        .sort((a, b) => a.order - b.order)
        .map(({ questionId: id, text }) => ({ id, text }))
    }));

    const selfDisclosureCategoriesWithQuestions = SELF_DISCLOSURE_CATEGORIES.map(category => ({
      ...category,
      questions: sdQuestions
        .filter(q => q.category === category.id)
        .sort((a, b) => a.order - b.order)
        .map(({ questionId: id, text }) => ({ id, text }))
    }));

    return {
      bfi: {
        dimensions: bfiDimensionsWithQuestions
      },
      selfDisclosure: {
        categories: selfDisclosureCategoriesWithQuestions
      }
    };
  }

  async getAllPersonalityTests(): Promise<any[]> {
    return this.personalityTestModel.find().populate('userId').exec();
  }

  async getPersonalityTestByUserId(userId: string): Promise<PersonalityTestDocument | null> {
    return this.personalityTestModel.findOne({ userId: new Types.ObjectId(userId) }).populate('userId').exec();
  }
}
