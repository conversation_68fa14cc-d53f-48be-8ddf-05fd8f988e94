import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum QuestionType {
  BFI = 'bfi',
  SELF_DISCLOSURE = 'self_disclosure',
}

@Schema({ 
  timestamps: true,
  collection: 'personality-questions'
})
export class PersonalityQuestion {
  @Prop({ required: true })
  questionId: string;

  @Prop({ required: true, enum: QuestionType, default: QuestionType.BFI })
  questionType: QuestionType;

  @Prop({ required: true })
  text: string;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: 1 })
  order: number;

  @Prop({ default: null })
  category: string; // For Self-Disclosure questions

  @Prop({ default: null })
  dimension: string; // For BFI questions
}

export const PersonalityQuestionSchema = SchemaFactory.createForClass(PersonalityQuestion);

// Export document type
export type PersonalityQuestionDocument = PersonalityQuestion & Document;
