import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { LoginForm } from "@/components/auth/login-form";
import { OtpVerificationForm } from "@/components/auth/otp-verification-form";

export default function LoginPage() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col items-center space-y-2 text-center">
        <Image 
          src="/logo.png" 
          alt="Pairsona Logo" 
          width={160} 
          height={160}
          className="mb-2"
          priority
        />
        <p className="text-sm text-muted-foreground">
          Temukan pasangan sempurna berdasarkan psikologi
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Masuk</CardTitle>
          <CardDescription>
            Masukkan email atau nomor telepon untuk menerima kode OTP
          </CardDescription>
        </CardHeader>
        <CardContent>
          <LoginForm />
        </CardContent>
        <CardFooter className="flex flex-col items-center space-y-2">
          <div className="text-sm text-muted-foreground">
            Belum punya akun?{" "}
            <Link href="/register" className="text-accent-red hover:underline">
              Daftar
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
