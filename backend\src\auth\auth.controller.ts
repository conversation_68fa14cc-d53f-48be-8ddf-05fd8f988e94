import { Controller, Post, Body, Get, Request, Put, UseInterceptors, Param, Req, Delete, BadRequestException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { IsPublic } from './decorators/is-public.decorator';
import { SignInDto } from './dto/signin.dto';
import { SignupDto } from './dto/signup.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { RequestWithUser } from 'src/common/interfaces/request-with-user.interface';
import { VerifyOtpResponse } from './dto/verify-otp.response';
import { AuthProfileResponseDto } from './dto/auth-profile.response.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { bulkUploadImageInterceptor, uploadImageInterceptor } from 'src/file/interceptors/file-upload.interceptor';
import { FileService } from 'src/file/file.service';
import { UserService } from 'src/user/user.service';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly fileService: FileService,
    private readonly userService: UserService
  ) {}

  /**
   * Signin
   * 
   * @param signinDto - Signin DTO
   * @returns Signin response
   */
  @IsPublic()
  @Post('signin')
  async signin(@Body() signinDto: SignInDto): Promise<{ message: string | null } | undefined> {
    return this.authService.signin(signinDto);
  }

  /**
   * Signup
   * 
   * @param signupDto - Signup DTO
   * @returns Signup response
   */
  @IsPublic()
  @Post('signup')
  async signup(@Body() signupDto: SignupDto): Promise<{ message: string | null } | undefined> {
    return this.authService.signup(signupDto);
  }

  /**
   * Verify OTP
   * 
   * @param verifyOtpDto - Verify OTP DTO
   * @returns Verify OTP response
   */
  @IsPublic()
  @Post('verify-otp')
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto): Promise<VerifyOtpResponse> {
    return this.authService.verifyOtp(verifyOtpDto);
  }

  /**
   * Get profile
   * 
   * @param req - Request object
   * @returns Profile
   */
  @Get('profile')
  async getProfile(@Request() req: RequestWithUser): Promise<AuthProfileResponseDto> {
    return this.authService.getProfile(req.user.userId);
  }

  /**
   * Update profile
   * 
   * @param updateProfileDto - Update profile DTO
   * @param req - Request object
   * @returns Updated profile
   */
  @Put('profile')
  async updateProfile(@Body() updateProfileDto: UpdateProfileDto, @Request() req: RequestWithUser): Promise<AuthProfileResponseDto> {
    return this.authService.updateProfile(req.user.userId, updateProfileDto);
  }

  /**
   * Upload image to AWS S3
   * 
   * @param file - Image file to upload
   * @returns URL of the uploaded image
   */
  @Put('profile/image')
  @UseInterceptors(uploadImageInterceptor('file'))
  async uploadImage(@Req() req: RequestWithUser): Promise<any> {
    const directory = 'profile';
    const image = await this.fileService.uploadImageToS3(directory, req.file as Express.Multer.File);

    await this.userService.updateImage(req.user.userId, {
      url: image.url,
      key: image.key
    });

    return image;
  }

  /**
   * Upload photos to AWS S3
   * 
   * @param files - Array of image files to upload
   * @returns Array of URLs of the uploaded photos
   */
  @Put('profile/photos')
  @UseInterceptors(bulkUploadImageInterceptor('files'))
  async uploadPhotos(@Request() req: RequestWithUser): Promise<any> {
    if (!Array.isArray(req.files)) {
      return false;
    }
    
    const directory = 'photos';
    const photos: { url: string, key: string }[] = [];
    
    for (const file of req.files) {
      const photo = await this.fileService.uploadImageToS3(directory, file as Express.Multer.File);
      photos.push(photo);

      await this.userService.addPhoto(req.user.userId, photo);
    }

    return photos;
  }

  /**
   * Delete photos by ids
   * 
   * @param ids - Photo ids
   * @param req - Request object
   * @returns { message: string }
   */
  @Delete('profile/photos/:ids')
  async deletePhoto(@Param('ids') ids: string, @Request() req: RequestWithUser): Promise<{ message: string }> {
    if (!ids) {
      throw new BadRequestException('Invalid photo ids');
    }

    const photoIds = ids.split(',');

    for (const photoId of photoIds) {
      await this.userService.removePhotoById(req.user.userId, photoId);
    }
    
    return { message: 'Photos deleted successfully' };
  }
}
