import { Is<PERSON>mail, IsNot<PERSON>mpty, <PERSON><PERSON>ength, IsOptional, ValidateIf, IsString, IsArray, IsBoolean, IsE<PERSON> } from 'class-validator';
import { AuthProvider } from './signin.dto';

export class SignupDto {
  @IsNotEmpty()
  provider: AuthProvider;
  
  @IsString()
  @MinLength(3)
  name: string;

  @ValidateIf(o => !o.phoneNumber || o.phoneNumber === '')
  @IsEmail()
  @IsNotEmpty({ message: 'Email is required when phone number is not provided' })
  email?: string;

  @ValidateIf(o => !o.email || o.email === '')
  @IsNotEmpty({ message: 'Phone number is required when email is not provided' })
  phoneNumber?: string;

  @IsOptional()
  @IsString()
  dateOfBirth?: string;

  @IsOptional()
  @IsString()
  gender?: string;

  @IsOptional()
  @IsString()
  religion?: string;

  @IsOptional()
  @IsString()
  occupation?: string;

  @IsOptional()
  @IsBoolean()
  isSmoker?: boolean;

  @IsOptional()
  @IsBoolean()
  acceptDifferentReligion?: boolean;

  @IsOptional()
  @IsString()
  about?: string;
}