import {
  Body,
  Controller,
  Get,
  Post,
  Request
} from '@nestjs/common';
import { AddonService } from '../services/addon.service';
import { AddonResponseDto } from '../dto/addon-response.dto';
import { RequestWithUser } from 'src/common/interfaces/request-with-user.interface';
import { AddonPurchaseRequestDto } from '../dto/addon-purchase-request.dto';
import { InvoiceResponseDto } from '../dto/invoice.dto';

@Controller('plans/addon')
export class AddonController {
  constructor(
    private readonly addonService: AddonService
  ) {}

  /**
   * Get addons
   * @returns Addons
   */
  @Get()
  async getAddons(): Promise<AddonResponseDto[]> {
    return this.addonService.getAddons();
  }

  /**
   * Purchase addons
   * @param req Request
   * @param body Body
   * @returns Invoice
   */
  @Post()
  async purchaseAddon(@Request() req: RequestWithUser, @Body() body: AddonPurchaseRequestDto): Promise<InvoiceResponseDto> {
    const invoice = await this.addonService.purchaseAddons(req.user.userId, body.items);

    return {
      id: invoice.id,
      userId: invoice.userId,
      planId: invoice.planId,
      purchaseId: invoice.purchaseId,
      items: invoice.items,
      totals: invoice.totals,
      paidDate: invoice.paidDate,
      metadata: invoice.metadata,
      status: invoice.status,
      invoiceCode: invoice.invoiceCode,
      invoiceLink: invoice.invoiceLink,
      paymentLink: invoice.paymentLink,
      paymentToken: invoice.paymentToken,
      createdAt: invoice.createdAt,
      updatedAt: invoice.updatedAt
    };
  }
}
