"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Send, Loader2, AlertCircle, Users } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { io, Socket } from "socket.io-client";

interface User {
  id: string;
  name: string;
  image: string | null;
  isOnline: boolean;
}

interface LastMessage {
  id: string;
  senderId: string;
  message: string;
  sentAt: string;
}

interface Chat {
  id: string;
  matchId: string;
  user: User;
  lastMessage: LastMessage | null;
}

interface Message {
  id: string;
  chatId: string;
  senderId: string;
  message: string;
  sentAt: string;
}

interface MessagesResponse {
  data: Message[];
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

export default function ChatPage() {
  const [chats, setChats] = useState<Chat[]>([]);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [socket, setSocket] = useState<Socket | null>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  useEffect(() => {
    fetchChats();
    getCurrentUserId();

    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, [socket]); // Add socket to dependency array

  // Auto-select first chat when chats are loaded
  useEffect(() => {
    if (chats.length > 0 && !selectedChat) {
      handleChatSelect(chats[0]);
    }
  }, [chats, selectedChat]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const getCurrentUserId = async () => {
    try {
      const token = localStorage.getItem('pairsona_token');
      if (!token) return;

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const profile = await response.json();
        setCurrentUserId(profile.id);
      }
    } catch (err) {
      // Silently fail - not critical for chat functionality
    }
  };

  const fetchChats = async () => {
    try {
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/chats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('pairsona_token');
          router.replace('/login');
          return;
        }
        throw new Error('Failed to fetch chats');
      }

      const chatsData = await response.json();
      setChats(chatsData);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load chats');
    } finally {
      setLoading(false);
    }
  };
  const fetchMessages = async (chatId: string) => {
    try {
      setMessagesLoading(true);
      const token = localStorage.getItem('pairsona_token');
      if (!token) throw new Error('No token available');

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/chats/${chatId}/messages?page=1&limit=50`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch messages: ${response.status} ${response.statusText}`);
      }

      const messagesData: MessagesResponse = await response.json();
      setMessages(messagesData.data.reverse()); // Reverse to show oldest first

      return messagesData; // Return data for success verification

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load messages');
      throw err; // Re-throw for handleChatSelect to catch
    } finally {
      setMessagesLoading(false);
    }
  };

  const connectToChat = (chatId: string) => {
    const token = localStorage.getItem('pairsona_token');
    if (!token) return;

    // Disconnect existing socket
    if (socket) {
      socket.disconnect();
    }

    const socketUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL}/chat`;

    const config = {
      transports: ['websocket'],
      auth: {
        authorization: `Bearer ${token}`
      },
      query: {
        chatId: chatId
      }
    };

    const newSocket = io(socketUrl, config);

    newSocket.on('connect', () => {
      // Connection successful
    });

    newSocket.on('message', (message: Message) => {
      setMessages(prev => [...prev, message]);

      // Update last message in chats list
      setChats(prev => prev.map(chat =>
        chat.id === message.chatId
          ? {
              ...chat,
              lastMessage: {
                id: message.id,
                senderId: message.senderId,
                message: message.message,
                sentAt: message.sentAt
              }
            }
          : chat
      ));
    });

    newSocket.on('disconnect', (reason) => {
      if (reason === 'io server disconnect') {
        setError('Unable to connect to chat. Please check your authentication.');
      }
    });

    newSocket.on('connect_error', (error: any) => {
      if (error.message && (error.message.includes('Authentication') || error.message.includes('Unauthorized'))) {
        setError('Authentication failed. Please refresh the page and try again.');
      } else {
        setError('Failed to connect to chat server. Please try again.');
      }
    });

    newSocket.on('error', () => {
      setError('Connection error. Please try again.');
    });

    setSocket(newSocket);
  };

  const handleChatSelect = async (chat: Chat) => {
    setSelectedChat(chat);
    setMessages([]);

    try {
      await fetchMessages(chat.id);
      connectToChat(chat.id);
    } catch (error) {
      setError('Unable to access this chat. Please try another conversation.');
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedChat || !socket || sending) return;

    try {
      setSending(true);

      if (!socket.connected) {
        connectToChat(selectedChat.id);
        setError('Connection lost. Please try again.');
        return;
      }

      socket.emit('sendMessage', {
        message: newMessage.trim()
      });

      setNewMessage('');

    } catch (err) {
      setError('Failed to send message');
    } finally {
      setSending(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatLastMessageTime = (dateString: string) => {
    const now = new Date();
    const messageDate = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return messageDate.toLocaleDateString();
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Chat</h1>
          <p className="text-muted-foreground">Loading your conversations...</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="md:col-span-1 h-[calc(100vh-200px)]">
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageSquare className="h-5 w-5 mr-2 text-[#D0544D]" />
                Conversations
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-full bg-gray-200"></div>
                      <div className="flex-1">
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          <Card className="md:col-span-2 h-[calc(100vh-200px)]">
            <CardContent className="p-8 flex items-center justify-center">
              <Loader2 className="w-8 h-8 animate-spin text-[#D0544D]" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Chat</h1>
          <p className="text-muted-foreground">Connect with your matches.</p>
        </div>
        <Card className="p-6">
          <div className="text-center">
            <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchChats}>Try Again</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Chat</h1>
        <p className="text-muted-foreground">Connect with your matches.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Chat list sidebar */}
        <Card className="md:col-span-1 h-[calc(100vh-200px)]">
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="h-5 w-5 mr-2 text-[#D0544D]" />
              Conversations
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {chats.length === 0 ? (
              <div className="p-8 text-center">
                <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No conversations yet</h3>
                <p className="text-gray-500">Start connecting with your matches to begin chatting!</p>
              </div>
            ) : (
              <div className="divide-y">
                {chats.map((chat) => (
                  <div
                    key={chat.id}
                    className={`p-4 cursor-pointer hover:bg-[#F2E7DB]/50 transition-colors ${
                      selectedChat?.id === chat.id ? 'bg-[#F2E7DB]/30' : ''
                    }`}
                    onClick={() => handleChatSelect(chat)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <Avatar className="w-10 h-10">
                          <AvatarImage src={chat.user.image || undefined} alt={chat.user.name} />
                          <AvatarFallback className="bg-[#D0544D]/20 text-[#D0544D]">
                            {getInitials(chat.user.name)}
                          </AvatarFallback>
                        </Avatar>
                        {chat.user.isOnline && (
                          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">{chat.user.name}</p>
                        <p className="text-xs text-muted-foreground truncate">
                          {chat.lastMessage ? chat.lastMessage.message : 'No messages yet'}
                        </p>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {chat.lastMessage ? formatLastMessageTime(chat.lastMessage.sentAt) : ''}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Chat area */}
        <Card className="md:col-span-2 h-[calc(100vh-200px)] flex flex-col">
          {selectedChat ? (
            <>
              <CardHeader className="border-b">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="relative mr-3">
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={selectedChat.user.image || undefined} alt={selectedChat.user.name} />
                        <AvatarFallback className="bg-[#D0544D]/20 text-[#D0544D]">
                          {getInitials(selectedChat.user.name)}
                        </AvatarFallback>
                      </Avatar>
                      {selectedChat.user.isOnline && (
                        <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                      )}
                    </div>
                    <div>
                      <h3 className="font-semibold">{selectedChat.user.name}</h3>
                      <p className="text-xs text-muted-foreground">
                        {selectedChat.user.isOnline ? 'Online' : 'Offline'}
                      </p>
                    </div>
                  </div>
                </CardTitle>
              </CardHeader>

              <CardContent className="flex-1 overflow-auto p-4">
                {messagesLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="w-6 h-6 animate-spin text-[#D0544D]" />
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.length === 0 ? (
                      <div className="text-center py-8">
                        <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No messages yet</h3>
                        <p className="text-gray-500">Start the conversation by sending a message!</p>
                      </div>
                    ) : (
                      messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex ${message.senderId === currentUserId ? 'justify-end' : 'justify-start'}`}
                        >
                          <div
                            className={`rounded-lg p-3 max-w-[80%] ${
                              message.senderId === currentUserId
                                ? 'bg-[#D0544D] text-white'
                                : 'bg-[#F2E7DB]'
                            }`}
                          >
                            <p className="text-sm">{message.message}</p>
                            <p className={`text-xs mt-1 ${
                              message.senderId === currentUserId
                                ? 'text-white/70'
                                : 'text-muted-foreground'
                            }`}>
                              {formatTime(message.sentAt)}
                            </p>
                          </div>
                        </div>
                      ))
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                )}
              </CardContent>

              <div className="p-4 border-t">
                <div className="flex space-x-2">
                  <Input
                    type="text"
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={handleKeyDown}
                    disabled={sending}
                    className="flex-1 focus:ring-[#D0544D] focus:border-[#D0544D]"
                  />
                  <Button
                    onClick={sendMessage}
                    disabled={!newMessage.trim() || sending}
                    className="bg-[#D0544D] hover:bg-[#D0544D]/90 text-white"
                  >
                    {sending ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      <Send className="h-5 w-5" />
                    )}
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <CardContent className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <MessageSquare className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">Select a conversation</h3>
                <p className="text-gray-500">Choose a conversation from the sidebar to start chatting</p>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
}
