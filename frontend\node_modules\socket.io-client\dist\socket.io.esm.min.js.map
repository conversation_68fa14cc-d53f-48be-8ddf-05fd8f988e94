{"version": 3, "file": "socket.io.esm.min.js", "sources": ["../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-client/node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/index.mjs", "../node_modules/engine.io-client/build/esm/globalThis.browser.js", "../node_modules/engine.io-client/build/esm/util.js", "../node_modules/engine.io-client/build/esm/transport.js", "../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../node_modules/engine.io-client/build/esm/contrib/yeast.js", "../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../node_modules/engine.io-client/build/esm/transports/xmlhttprequest.browser.js", "../node_modules/engine.io-client/build/esm/transports/polling.js", "../node_modules/engine.io-client/build/esm/transports/websocket-constructor.browser.js", "../node_modules/engine.io-client/build/esm/transports/websocket.js", "../node_modules/engine.io-client/build/esm/transports/index.js", "../node_modules/engine.io-client/build/esm/transports/webtransport.js", "../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../node_modules/engine.io-client/build/esm/socket.js", "../node_modules/socket.io-parser/build/esm/is-binary.js", "../node_modules/socket.io-parser/build/esm/binary.js", "../node_modules/socket.io-parser/build/esm/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js", "../build/esm/url.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data\n            .arrayBuffer()\n            .then(toArray)\n            .then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, encoded => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType)\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1)\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type]\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, encodedPacket => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, encodedPacket => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        }\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else if (state === 2 /* READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        }\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nclass TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\nexport function createCookieJar() { }\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { createCookieJar, XHR as XMLHttpRequest, } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n        if (this.opts.withCredentials) {\n            this.cookieJar = createCookieJar();\n        }\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, cookieJar: this.cookieJar }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        var _a;\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, true);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(xhr);\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: Polling,\n};\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"./websocket-constructor.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        // @ts-ignore\n        if (typeof WebTransport !== \"function\") {\n            return;\n        }\n        // @ts-ignore\n        this.transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        this.transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this.transport.ready.then(() => {\n            this.transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this.writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this.writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this.writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this.transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { defaultBinaryType } from \"./transports/websocket-constructor.js\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\n            \"polling\",\n            \"websocket\",\n            \"webtransport\",\n        ];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this.upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            this.resetPingTimeout();\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n        }\n        else if (this.connected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        this.acks[id] = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, [null, ...args]);\n        };\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        // the timeout flag is optional\n        const withErr = this.flags.timeout !== undefined || this._opts.ackTimeout !== undefined;\n        return new Promise((resolve, reject) => {\n            args.push((arg1, arg2) => {\n                if (withErr) {\n                    return arg1 ? reject(arg1) : resolve(arg2);\n                }\n                else {\n                    return resolve(arg1);\n                }\n            });\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "chars", "lookup", "i", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "length", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "createPacketEncoderStream", "TransformStream", "transform", "packet", "controller", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "encodePacketToBinary", "payloadLength", "header", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "TEXT_DECODER", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "globalThisShim", "self", "window", "Function", "pick", "attr", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "setTimeoutFn", "bind", "clearTimeoutFn", "TransportError", "Error", "constructor", "reason", "description", "context", "super", "Transport", "writable", "query", "socket", "onError", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "onPacket", "details", "pause", "onPause", "createUri", "schema", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "str", "encodeURIComponent", "alphabet", "map", "prev", "seed", "num", "Math", "floor", "yeast", "now", "Date", "value", "XMLHttpRequest", "err", "hasCORS", "XHR", "xdomain", "e", "concat", "join", "empty", "hasXHR2", "responseType", "Request", "uri", "method", "undefined", "_a", "xd", "xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "cookieJar", "addCookies", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "status", "onLoad", "document", "index", "requestsCount", "requests", "cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "nextTick", "Promise", "resolve", "WebSocket", "MozWebSocket", "isReactNative", "navigator", "product", "toLowerCase", "transports", "websocket", "forceBase64", "name", "check", "protocols", "headers", "ws", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "timestampRequests", "timestampParam", "b64", "webtransport", "WebTransport", "transport", "transportOptions", "closed", "catch", "ready", "createBidirectionalStream", "stream", "decoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "pow", "createPacketDecoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "writer", "getWriter", "read", "done", "sid", "polling", "location", "isSSL", "protocol", "createCookieJar", "poll", "total", "doPoll", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "count", "encodePayload", "doWrite", "request", "assign", "req", "xhrStatus", "pollXhr", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "Socket", "writeBuffer", "prevBufferLen", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "id", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "beforeunloadEventListener", "offlineEventListener", "createTransport", "EIO", "priorWebsocketSuccess", "setTransport", "onDrain", "probe", "failed", "onTransportOpen", "msg", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "resetPingTimeout", "onHandshake", "JSON", "sendPacket", "code", "filterUpgrades", "getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "options", "compress", "cleanupAndClose", "waitForUpgrade", "filteredUpgrades", "withNativeFile", "File", "hasBinary", "toJSON", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "newData", "reconstructPacket", "_reconstructPacket", "RESERVED_EVENTS", "PacketType", "isObject", "Decoder", "reviver", "add", "reconstructor", "decodeString", "isBinaryEvent", "BINARY_EVENT", "BINARY_ACK", "EVENT", "ACK", "BinaryReconstructor", "takeBinaryData", "start", "buf", "nsp", "next", "payload", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "static", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "destroy", "finishedReconstruction", "reconPack", "binData", "replacer", "encodeAsString", "encodeAsBinary", "stringify", "deconstruction", "unshift", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_opts", "_autoConnect", "disconnected", "subEvents", "subs", "onpacket", "active", "_readyState", "retries", "fromQueue", "volatile", "_addToQueue", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "notifyOutgoingListeners", "ackTimeout", "timer", "emitWithAck", "withErr", "reject", "arg1", "arg2", "tryCount", "pending", "responseArgs", "_drainQueue", "force", "_packet", "_sendConnectPacket", "_pid", "pid", "offset", "_lastOffset", "onconnect", "onevent", "onack", "ondisconnect", "message", "emitEvent", "_anyListeners", "listener", "sent", "emitBuffered", "subDestroy", "onAny", "prependAny", "offAny", "listenersAny", "onAnyOutgoing", "_anyOutgoingListeners", "prependAnyOutgoing", "offAnyOutgoing", "listenersAnyOutgoing", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "rand", "random", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "nsps", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "Encoder", "decoder", "autoConnect", "v", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "maybeReconnectOnOpen", "_reconnecting", "reconnect", "Engine", "skipReconnect", "openSubDestroy", "errorSub", "onping", "ondata", "ondecoded", "_destroy", "_close", "delay", "onreconnect", "attempt", "cache", "parsed", "loc", "test", "href", "url", "sameNamespace", "forceNew", "multiplex"], "mappings": ";;;;;AAAA,MAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,MAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAAQC,IAC9BH,EAAqBH,EAAaM,IAAQA,CAAG,IAEjD,MAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAE/BC,EAASC,GAC0B,mBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,GAAOA,EAAIC,kBAAkBH,YAEjCI,EAAe,EAAGZ,OAAMC,QAAQY,EAAgBC,IAC9CZ,GAAkBD,aAAgBE,KAC9BU,EACOC,EAASb,GAGTc,EAAmBd,EAAMa,GAG/BP,IACJN,aAAgBO,aAAeC,EAAOR,IACnCY,EACOC,EAASb,GAGTc,EAAmB,IAAIZ,KAAK,CAACF,IAAQa,GAI7CA,EAAStB,EAAaQ,IAASC,GAAQ,KAE5Cc,EAAqB,CAACd,EAAMa,KAC9B,MAAME,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,MAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CP,EAAS,KAAOK,GAAW,IACnC,EACWH,EAAWM,cAAcrB,EAAK,EAEzC,SAASsB,EAAQtB,GACb,OAAIA,aAAgBuB,WACTvB,EAEFA,aAAgBO,YACd,IAAIgB,WAAWvB,GAGf,IAAIuB,WAAWvB,EAAKU,OAAQV,EAAKwB,WAAYxB,EAAKyB,WAEjE,CACA,IAAIC,EClDJ,MAAMC,EAAQ,mEAERC,EAA+B,oBAAfL,WAA6B,GAAK,IAAIA,WAAW,KACvE,IAAK,IAAIM,EAAI,EAAGA,EAAIF,GAAcE,IAC9BD,EAAOD,EAAMG,WAAWD,IAAMA,EAkB3B,MCrBDvB,EAA+C,mBAAhBC,YACxBwB,EAAe,CAACC,EAAeC,KACxC,GAA6B,iBAAlBD,EACP,MAAO,CACHjC,KAAM,UACNC,KAAMkC,EAAUF,EAAeC,IAGvC,MAAMlC,EAAOiC,EAAcG,OAAO,GAClC,GAAa,MAATpC,EACA,MAAO,CACHA,KAAM,UACNC,KAAMoC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAI7D,OADmBvC,EAAqBK,GAIjCiC,EAAcM,OAAS,EACxB,CACEvC,KAAML,EAAqBK,GAC3BC,KAAMgC,EAAcK,UAAU,IAEhC,CACEtC,KAAML,EAAqBK,IARxBD,CASN,EAEHsC,EAAqB,CAACpC,EAAMiC,KAC9B,GAAI3B,EAAuB,CACvB,MAAMiC,EDTQ,CAACC,IACnB,IAA8DX,EAAUY,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOF,OAAeQ,EAAMN,EAAOF,OAAWS,EAAI,EACnC,MAA9BP,EAAOA,EAAOF,OAAS,KACvBO,IACkC,MAA9BL,EAAOA,EAAOF,OAAS,IACvBO,KAGR,MAAMG,EAAc,IAAIzC,YAAYsC,GAAeI,EAAQ,IAAI1B,WAAWyB,GAC1E,IAAKnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWb,EAAOY,EAAOV,WAAWD,IACpCa,EAAWd,EAAOY,EAAOV,WAAWD,EAAI,IACxCc,EAAWf,EAAOY,EAAOV,WAAWD,EAAI,IACxCe,EAAWhB,EAAOY,EAAOV,WAAWD,EAAI,IACxCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CAAW,ECTEE,CAAOlD,GACvB,OAAOkC,EAAUK,EAASN,EAC7B,CAEG,MAAO,CAAEO,QAAQ,EAAMxC,OAC1B,EAECkC,EAAY,CAAClC,EAAMiC,IAEZ,SADDA,EAEIjC,aAAgBE,KAETF,EAIA,IAAIE,KAAK,CAACF,IAIjBA,aAAgBO,YAETP,EAIAA,EAAKU,OCvDtByC,EAAYC,OAAOC,aAAa,IA4B/B,SAASC,IACZ,OAAO,IAAIC,gBAAgB,CACvBC,UAAUC,EAAQC,IHmBnB,SAA8BD,EAAQ5C,GACrCZ,GAAkBwD,EAAOzD,gBAAgBE,KAClCuD,EAAOzD,KACT2D,cACAC,KAAKtC,GACLsC,KAAK/C,GAELP,IACJmD,EAAOzD,gBAAgBO,aAAeC,EAAOiD,EAAOzD,OAC9Ca,EAASS,EAAQmC,EAAOzD,OAEnCW,EAAa8C,GAAQ,GAAOI,IACnBnC,IACDA,EAAe,IAAIoC,aAEvBjD,EAASa,EAAaqC,OAAOF,GAAS,GAE9C,CGnCYG,CAAqBP,GAAQzB,IACzB,MAAMiC,EAAgBjC,EAAcM,OACpC,IAAI4B,EAEJ,GAAID,EAAgB,IAChBC,EAAS,IAAI3C,WAAW,GACxB,IAAI4C,SAASD,EAAOxD,QAAQ0D,SAAS,EAAGH,QAEvC,GAAIA,EAAgB,MAAO,CAC5BC,EAAS,IAAI3C,WAAW,GACxB,MAAM8C,EAAO,IAAIF,SAASD,EAAOxD,QACjC2D,EAAKD,SAAS,EAAG,KACjBC,EAAKC,UAAU,EAAGL,EACrB,KACI,CACDC,EAAS,IAAI3C,WAAW,GACxB,MAAM8C,EAAO,IAAIF,SAASD,EAAOxD,QACjC2D,EAAKD,SAAS,EAAG,KACjBC,EAAKE,aAAa,EAAGC,OAAOP,GAC/B,CAEGR,EAAOzD,MAA+B,iBAAhByD,EAAOzD,OAC7BkE,EAAO,IAAM,KAEjBR,EAAWe,QAAQP,GACnBR,EAAWe,QAAQzC,EAAc,GAExC,GAET,CACA,IAAI0C,EACJ,SAASC,EAAYC,GACjB,OAAOA,EAAOC,QAAO,CAACC,EAAKC,IAAUD,EAAMC,EAAMzC,QAAQ,EAC7D,CACA,SAAS0C,EAAaJ,EAAQK,GAC1B,GAAIL,EAAO,GAAGtC,SAAW2C,EACrB,OAAOL,EAAOM,QAElB,MAAMxE,EAAS,IAAIa,WAAW0D,GAC9B,IAAIE,EAAI,EACR,IAAK,IAAItD,EAAI,EAAGA,EAAIoD,EAAMpD,IACtBnB,EAAOmB,GAAK+C,EAAO,GAAGO,KAClBA,IAAMP,EAAO,GAAGtC,SAChBsC,EAAOM,QACPC,EAAI,GAMZ,OAHIP,EAAOtC,QAAU6C,EAAIP,EAAO,GAAGtC,SAC/BsC,EAAO,GAAKA,EAAO,GAAGQ,MAAMD,IAEzBzE,CACX,CC/EO,SAAS2E,EAAQ5E,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIZ,KAAOwF,EAAQlF,UACtBM,EAAIZ,GAAOwF,EAAQlF,UAAUN,GAE/B,OAAOY,CACT,CAhBkB6E,CAAM7E,EACxB,CA0BA4E,EAAQlF,UAAUoF,GAClBF,EAAQlF,UAAUqF,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,GACpCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACT,EAYAN,EAAQlF,UAAU2F,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,UAChB,CAID,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACT,EAYAN,EAAQlF,UAAU4F,IAClBV,EAAQlF,UAAU+F,eAClBb,EAAQlF,UAAUgG,mBAClBd,EAAQlF,UAAUiG,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAGjC,GAAKK,UAAU3D,OAEjB,OADAqD,KAAKC,WAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,WAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAU3D,OAEjB,cADOqD,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI9D,EAAI,EAAGA,EAAIyE,EAAUhE,OAAQT,IAEpC,IADAwE,EAAKC,EAAUzE,MACJ6D,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO1E,EAAG,GACpB,KACD,CASH,OAJyB,IAArByE,EAAUhE,eACLqD,KAAKC,WAAW,IAAMH,GAGxBE,IACT,EAUAN,EAAQlF,UAAUqG,KAAO,SAASf,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAU3D,OAAS,GACpCgE,EAAYX,KAAKC,WAAW,IAAMH,GAE7B5D,EAAI,EAAGA,EAAIoE,UAAU3D,OAAQT,IACpC4E,EAAK5E,EAAI,GAAKoE,UAAUpE,GAG1B,GAAIyE,EAEG,CAAIzE,EAAI,EAAb,IAAK,IAAWiB,GADhBwD,EAAYA,EAAUlB,MAAM,IACI9C,OAAQT,EAAIiB,IAAOjB,EACjDyE,EAAUzE,GAAGmE,MAAML,KAAMc,EADKnE,CAKlC,OAAOqD,IACT,EAGAN,EAAQlF,UAAUwG,aAAetB,EAAQlF,UAAUqG,KAUnDnB,EAAQlF,UAAUyG,UAAY,SAASnB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,CAAA,EAC9BD,KAAKC,WAAW,IAAMH,IAAU,EACzC,EAUAJ,EAAQlF,UAAU0G,aAAe,SAASpB,GACxC,QAAUE,KAAKiB,UAAUnB,GAAOnD,MAClC,ECxKO,MAAMwE,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GCPR,SAASC,EAAKzG,KAAQ0G,GACzB,OAAOA,EAAKtC,QAAO,CAACC,EAAKsC,KACjB3G,EAAI4G,eAAeD,KACnBtC,EAAIsC,GAAK3G,EAAI2G,IAEVtC,IACR,CAAE,EACT,CAEA,MAAMwC,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsBlH,EAAKmH,GACnCA,EAAKC,iBACLpH,EAAIqH,aAAeR,EAAmBS,KAAKR,GAC3C9G,EAAIuH,eAAiBP,EAAqBM,KAAKR,KAG/C9G,EAAIqH,aAAeP,EAAWC,WAAWO,KAAKR,GAC9C9G,EAAIuH,eAAiBT,EAAWG,aAAaK,KAAKR,GAE1D,CCjBA,MAAMU,UAAuBC,MACzBC,YAAYC,EAAQC,EAAaC,GAC7BC,MAAMH,GACNzC,KAAK0C,YAAcA,EACnB1C,KAAK2C,QAAUA,EACf3C,KAAK5F,KAAO,gBACf,EAEE,MAAMyI,UAAkBnD,EAO3B8C,YAAYP,GACRW,QACA5C,KAAK8C,UAAW,EAChBd,EAAsBhC,KAAMiC,GAC5BjC,KAAKiC,KAAOA,EACZjC,KAAK+C,MAAQd,EAAKc,MAClB/C,KAAKgD,OAASf,EAAKe,MACtB,CAUDC,QAAQR,EAAQC,EAAaC,GAEzB,OADAC,MAAM5B,aAAa,QAAS,IAAIsB,EAAeG,EAAQC,EAAaC,IAC7D3C,IACV,CAIDkD,OAGI,OAFAlD,KAAKmD,WAAa,UAClBnD,KAAKoD,SACEpD,IACV,CAIDqD,QAKI,MAJwB,YAApBrD,KAAKmD,YAAgD,SAApBnD,KAAKmD,aACtCnD,KAAKsD,UACLtD,KAAKuD,WAEFvD,IACV,CAMDwD,KAAKC,GACuB,SAApBzD,KAAKmD,YACLnD,KAAK0D,MAAMD,EAKlB,CAMDE,SACI3D,KAAKmD,WAAa,OAClBnD,KAAK8C,UAAW,EAChBF,MAAM5B,aAAa,OACtB,CAOD4C,OAAOvJ,GACH,MAAMyD,EAAS1B,EAAa/B,EAAM2F,KAAKgD,OAAO1G,YAC9C0D,KAAK6D,SAAS/F,EACjB,CAMD+F,SAAS/F,GACL8E,MAAM5B,aAAa,SAAUlD,EAChC,CAMDyF,QAAQO,GACJ9D,KAAKmD,WAAa,SAClBP,MAAM5B,aAAa,QAAS8C,EAC/B,CAMDC,MAAMC,GAAY,CAClBC,UAAUC,EAAQnB,EAAQ,IACtB,OAAQmB,EACJ,MACAlE,KAAKmE,YACLnE,KAAKoE,QACLpE,KAAKiC,KAAKoC,KACVrE,KAAKsE,OAAOvB,EACnB,CACDoB,YACI,MAAMI,EAAWvE,KAAKiC,KAAKsC,SAC3B,OAAkC,IAA3BA,EAASC,QAAQ,KAAcD,EAAW,IAAMA,EAAW,GACrE,CACDH,QACI,OAAIpE,KAAKiC,KAAKwC,OACRzE,KAAKiC,KAAKyC,QAAUC,OAA0B,MAAnB3E,KAAKiC,KAAKwC,QACjCzE,KAAKiC,KAAKyC,QAAqC,KAA3BC,OAAO3E,KAAKiC,KAAKwC,OACpC,IAAMzE,KAAKiC,KAAKwC,KAGhB,EAEd,CACDH,OAAOvB,GACH,MAAM6B,ECjIP,SAAgB9J,GACnB,IAAI+J,EAAM,GACV,IAAK,IAAI3I,KAAKpB,EACNA,EAAI4G,eAAexF,KACf2I,EAAIlI,SACJkI,GAAO,KACXA,GAAOC,mBAAmB5I,GAAK,IAAM4I,mBAAmBhK,EAAIoB,KAGpE,OAAO2I,CACX,CDuH6BzG,CAAO2E,GAC5B,OAAO6B,EAAajI,OAAS,IAAMiI,EAAe,EACrD,EEzIL,MAAMG,EAAW,mEAAmEtJ,MAAM,IAAKkB,EAAS,GAAIqI,EAAM,GAClH,IAAqBC,EAAjBC,EAAO,EAAGhJ,EAAI,EAQX,SAASkC,EAAO+G,GACnB,IAAIjH,EAAU,GACd,GACIA,EAAU6G,EAASI,EAAMxI,GAAUuB,EACnCiH,EAAMC,KAAKC,MAAMF,EAAMxI,SAClBwI,EAAM,GACf,OAAOjH,CACX,CAqBO,SAASoH,IACZ,MAAMC,EAAMnH,GAAQ,IAAIoH,MACxB,OAAID,IAAQN,GACDC,EAAO,EAAGD,EAAOM,GACrBA,EAAM,IAAMnH,EAAO8G,IAC9B,CAIA,KAAOhJ,EAAIS,EAAQT,IACf8I,EAAID,EAAS7I,IAAMA,EChDvB,IAAIuJ,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,cACjC,CACA,MAAOC,GAGP,CACO,MAAMC,EAAUH,ECPhB,SAASI,EAAI5D,GAChB,MAAM6D,EAAU7D,EAAK6D,QAErB,IACI,GAAI,oBAAuBJ,kBAAoBI,GAAWF,GACtD,OAAO,IAAIF,cAElB,CACD,MAAOK,GAAM,CACb,IAAKD,EACD,IACI,OAAO,IAAIlE,EAAW,CAAC,UAAUoE,OAAO,UAAUC,KAAK,OAAM,oBAChE,CACD,MAAOF,GAAM,CAErB,CCXA,SAASG,IAAW,CACpB,MAAMC,EAIK,MAHK,IAAIT,EAAe,CAC3BI,SAAS,IAEMM,aAkNhB,MAAMC,UAAgB3G,EAOzB8C,YAAY8D,EAAKrE,GACbW,QACAZ,EAAsBhC,KAAMiC,GAC5BjC,KAAKiC,KAAOA,EACZjC,KAAKuG,OAAStE,EAAKsE,QAAU,MAC7BvG,KAAKsG,IAAMA,EACXtG,KAAK3F,UAAOmM,IAAcvE,EAAK5H,KAAO4H,EAAK5H,KAAO,KAClD2F,KAAKlG,QACR,CAMDA,SACI,IAAI2M,EACJ,MAAMxE,EAAOV,EAAKvB,KAAKiC,KAAM,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aACjHA,EAAK6D,UAAY9F,KAAKiC,KAAKyE,GAC3B,MAAMC,EAAO3G,KAAK2G,IAAM,IAAIjB,EAAezD,GAC3C,IACI0E,EAAIzD,KAAKlD,KAAKuG,OAAQvG,KAAKsG,KAAK,GAChC,IACI,GAAItG,KAAKiC,KAAK2E,aAAc,CACxBD,EAAIE,uBAAyBF,EAAIE,uBAAsB,GACvD,IAAK,IAAI3K,KAAK8D,KAAKiC,KAAK2E,aAChB5G,KAAKiC,KAAK2E,aAAalF,eAAexF,IACtCyK,EAAIG,iBAAiB5K,EAAG8D,KAAKiC,KAAK2E,aAAa1K,GAG1D,CACJ,CACD,MAAO6J,GAAM,CACb,GAAI,SAAW/F,KAAKuG,OAChB,IACII,EAAIG,iBAAiB,eAAgB,2BACxC,CACD,MAAOf,GAAM,CAEjB,IACIY,EAAIG,iBAAiB,SAAU,MAClC,CACD,MAAOf,GAAM,CACkB,QAA9BU,EAAKzG,KAAKiC,KAAK8E,iBAA8B,IAAPN,GAAyBA,EAAGO,WAAWL,GAE1E,oBAAqBA,IACrBA,EAAIM,gBAAkBjH,KAAKiC,KAAKgF,iBAEhCjH,KAAKiC,KAAKiF,iBACVP,EAAIQ,QAAUnH,KAAKiC,KAAKiF,gBAE5BP,EAAIS,mBAAqB,KACrB,IAAIX,EACmB,IAAnBE,EAAIxD,aAC2B,QAA9BsD,EAAKzG,KAAKiC,KAAK8E,iBAA8B,IAAPN,GAAyBA,EAAGY,aAAaV,IAEhF,IAAMA,EAAIxD,aAEV,MAAQwD,EAAIW,QAAU,OAASX,EAAIW,OACnCtH,KAAKuH,SAKLvH,KAAKmC,cAAa,KACdnC,KAAKiD,QAA8B,iBAAf0D,EAAIW,OAAsBX,EAAIW,OAAS,EAAE,GAC9D,GACN,EAELX,EAAInD,KAAKxD,KAAK3F,KACjB,CACD,MAAO0L,GAOH,YAHA/F,KAAKmC,cAAa,KACdnC,KAAKiD,QAAQ8C,EAAE,GAChB,EAEN,CACuB,oBAAbyB,WACPxH,KAAKyH,MAAQpB,EAAQqB,gBACrBrB,EAAQsB,SAAS3H,KAAKyH,OAASzH,KAEtC,CAMDiD,QAAQ0C,GACJ3F,KAAKgB,aAAa,QAAS2E,EAAK3F,KAAK2G,KACrC3G,KAAK4H,SAAQ,EAChB,CAMDA,QAAQC,GACJ,QAAI,IAAuB7H,KAAK2G,KAAO,OAAS3G,KAAK2G,IAArD,CAIA,GADA3G,KAAK2G,IAAIS,mBAAqBlB,EAC1B2B,EACA,IACI7H,KAAK2G,IAAImB,OACZ,CACD,MAAO/B,GAAM,CAEO,oBAAbyB,iBACAnB,EAAQsB,SAAS3H,KAAKyH,OAEjCzH,KAAK2G,IAAM,IAXV,CAYJ,CAMDY,SACI,MAAMlN,EAAO2F,KAAK2G,IAAIoB,aACT,OAAT1N,IACA2F,KAAKgB,aAAa,OAAQ3G,GAC1B2F,KAAKgB,aAAa,WAClBhB,KAAK4H,UAEZ,CAMDE,QACI9H,KAAK4H,SACR,EASL,GAPAvB,EAAQqB,cAAgB,EACxBrB,EAAQsB,SAAW,CAAA,EAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,QAEvB,GAAgC,mBAArBpI,iBAAiC,CAE7CA,iBADyB,eAAgB+B,EAAa,WAAa,SAChCqG,GAAe,EACrD,CAEL,SAASA,IACL,IAAK,IAAI/L,KAAKmK,EAAQsB,SACdtB,EAAQsB,SAASjG,eAAexF,IAChCmK,EAAQsB,SAASzL,GAAG4L,OAGhC,CCpYO,MAAMI,EACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAE/D1H,GAAOyH,QAAQC,UAAUnK,KAAKyC,GAG/B,CAACA,EAAIyB,IAAiBA,EAAazB,EAAI,GAGzC2H,EAAYzG,EAAWyG,WAAazG,EAAW0G,aCJtDC,EAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cCLf,MAAMC,EAAa,CACtBC,UDKG,cAAiB/F,EAOpBL,YAAYP,GACRW,MAAMX,GACNjC,KAAK/E,gBAAkBgH,EAAK4G,WAC/B,CACGC,WACA,MAAO,WACV,CACD1F,SACI,IAAKpD,KAAK+I,QAEN,OAEJ,MAAMzC,EAAMtG,KAAKsG,MACX0C,EAAYhJ,KAAKiC,KAAK+G,UAEtB/G,EAAOsG,EACP,CAAE,EACFhH,EAAKvB,KAAKiC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMjC,KAAKiC,KAAK2E,eACV3E,EAAKgH,QAAUjJ,KAAKiC,KAAK2E,cAE7B,IACI5G,KAAKkJ,GACyBX,EAIpB,IAAIF,EAAU/B,EAAK0C,EAAW/G,GAH9B+G,EACI,IAAIX,EAAU/B,EAAK0C,GACnB,IAAIX,EAAU/B,EAE/B,CACD,MAAOX,GACH,OAAO3F,KAAKgB,aAAa,QAAS2E,EACrC,CACD3F,KAAKkJ,GAAG5M,WAAa0D,KAAKgD,OAAO1G,WACjC0D,KAAKmJ,mBACR,CAMDA,oBACInJ,KAAKkJ,GAAGE,OAAS,KACTpJ,KAAKiC,KAAKoH,WACVrJ,KAAKkJ,GAAGI,QAAQC,QAEpBvJ,KAAK2D,QAAQ,EAEjB3D,KAAKkJ,GAAGM,QAAWC,GAAezJ,KAAKuD,QAAQ,CAC3Cb,YAAa,8BACbC,QAAS8G,IAEbzJ,KAAKkJ,GAAGQ,UAAaC,GAAO3J,KAAK4D,OAAO+F,EAAGtP,MAC3C2F,KAAKkJ,GAAGU,QAAW7D,GAAM/F,KAAKiD,QAAQ,kBAAmB8C,EAC5D,CACDrC,MAAMD,GACFzD,KAAK8C,UAAW,EAGhB,IAAK,IAAI5G,EAAI,EAAGA,EAAIuH,EAAQ9G,OAAQT,IAAK,CACrC,MAAM4B,EAAS2F,EAAQvH,GACjB2N,EAAa3N,IAAMuH,EAAQ9G,OAAS,EAC1C3B,EAAa8C,EAAQkC,KAAK/E,gBAAiBZ,IAmBvC,IAGQ2F,KAAKkJ,GAAG1F,KAAKnJ,EAKpB,CACD,MAAO0L,GACN,CACG8D,GAGA3B,GAAS,KACLlI,KAAK8C,UAAW,EAChB9C,KAAKgB,aAAa,QAAQ,GAC3BhB,KAAKmC,aACX,GAER,CACJ,CACDmB,eAC2B,IAAZtD,KAAKkJ,KACZlJ,KAAKkJ,GAAG7F,QACRrD,KAAKkJ,GAAK,KAEjB,CAMD5C,MACI,MAAMpC,EAASlE,KAAKiC,KAAKyC,OAAS,MAAQ,KACpC3B,EAAQ/C,KAAK+C,OAAS,GAS5B,OAPI/C,KAAKiC,KAAK6H,oBACV/G,EAAM/C,KAAKiC,KAAK8H,gBAAkBzE,KAGjCtF,KAAK/E,iBACN8H,EAAMiH,IAAM,GAEThK,KAAKiE,UAAUC,EAAQnB,EACjC,CAODgG,QACI,QAASV,CACZ,GCjJD4B,aCFG,cAAiBpH,EAChBiG,WACA,MAAO,cACV,CACD1F,SAEgC,mBAAjB8G,eAIXlK,KAAKmK,UAAY,IAAID,aAAalK,KAAKiE,UAAU,SAAUjE,KAAKiC,KAAKmI,iBAAiBpK,KAAK8I,OAC3F9I,KAAKmK,UAAUE,OACVpM,MAAK,KACN+B,KAAKuD,SAAS,IAEb+G,OAAO3E,IACR3F,KAAKiD,QAAQ,qBAAsB0C,EAAI,IAG3C3F,KAAKmK,UAAUI,MAAMtM,MAAK,KACtB+B,KAAKmK,UAAUK,4BAA4BvM,MAAMwM,IAC7C,MAAMC,Eb8Df,SAAmCC,EAAYrO,GAC7CyC,IACDA,EAAe,IAAI6L,aAEvB,MAAM3L,EAAS,GACf,IAAI4L,EAAQ,EACRC,GAAkB,EAClBC,GAAW,EACf,OAAO,IAAInN,gBAAgB,CACvBC,UAAUuB,EAAOrB,GAEb,IADAkB,EAAOiB,KAAKd,KACC,CACT,GAAc,IAAVyL,EAA+B,CAC/B,GAAI7L,EAAYC,GAAU,EACtB,MAEJ,MAAMV,EAASc,EAAaJ,EAAQ,GACpC8L,EAAkC,MAAV,IAAZxM,EAAO,IACnBuM,EAA6B,IAAZvM,EAAO,GAEpBsM,EADAC,EAAiB,IACT,EAEgB,MAAnBA,EACG,EAGA,CAEf,MACI,GAAc,IAAVD,EAA2C,CAChD,GAAI7L,EAAYC,GAAU,EACtB,MAEJ,MAAM+L,EAAc3L,EAAaJ,EAAQ,GACzC6L,EAAiB,IAAItM,SAASwM,EAAYjQ,OAAQiQ,EAAYnP,WAAYmP,EAAYrO,QAAQsO,UAAU,GACxGJ,EAAQ,CACX,MACI,GAAc,IAAVA,EAA2C,CAChD,GAAI7L,EAAYC,GAAU,EACtB,MAEJ,MAAM+L,EAAc3L,EAAaJ,EAAQ,GACnCP,EAAO,IAAIF,SAASwM,EAAYjQ,OAAQiQ,EAAYnP,WAAYmP,EAAYrO,QAC5EuO,EAAIxM,EAAKyM,UAAU,GACzB,GAAID,EAAI9F,KAAKgG,IAAI,EAAG,IAAW,EAAG,CAE9BrN,EAAWe,QAAQ3E,GACnB,KACH,CACD2Q,EAAiBI,EAAI9F,KAAKgG,IAAI,EAAG,IAAM1M,EAAKyM,UAAU,GACtDN,EAAQ,CACX,KACI,CACD,GAAI7L,EAAYC,GAAU6L,EACtB,MAEJ,MAAMzQ,EAAOgF,EAAaJ,EAAQ6L,GAClC/M,EAAWe,QAAQ1C,EAAa2O,EAAW1Q,EAAO0E,EAAaxB,OAAOlD,GAAOiC,IAC7EuO,EAAQ,CACX,CACD,GAAuB,IAAnBC,GAAwBA,EAAiBH,EAAY,CACrD5M,EAAWe,QAAQ3E,GACnB,KACH,CACJ,CACJ,GAET,CajIsCkR,CAA0B1G,OAAO2G,iBAAkBtL,KAAKgD,OAAO1G,YAC/EiP,EAASd,EAAOe,SAASC,YAAYf,GAAegB,YACpDC,EAAgBhO,IACtBgO,EAAcH,SAASI,OAAOnB,EAAO3H,UACrC9C,KAAK6L,OAASF,EAAc7I,SAASgJ,YACrC,MAAMC,EAAO,KACTR,EACKQ,OACA9N,MAAK,EAAG+N,OAAMvG,YACXuG,IAGJhM,KAAK6D,SAAS4B,GACdsG,IAAM,IAELzB,OAAO3E,IAAD,GACT,EAENoG,IACA,MAAMjO,EAAS,CAAE1D,KAAM,QACnB4F,KAAK+C,MAAMkJ,MACXnO,EAAOzD,KAAO,WAAW2F,KAAK+C,MAAMkJ,SAExCjM,KAAK6L,OAAOnI,MAAM5F,GAAQG,MAAK,IAAM+B,KAAK2D,UAAS,GACrD,IAET,CACDD,MAAMD,GACFzD,KAAK8C,UAAW,EAChB,IAAK,IAAI5G,EAAI,EAAGA,EAAIuH,EAAQ9G,OAAQT,IAAK,CACrC,MAAM4B,EAAS2F,EAAQvH,GACjB2N,EAAa3N,IAAMuH,EAAQ9G,OAAS,EAC1CqD,KAAK6L,OAAOnI,MAAM5F,GAAQG,MAAK,KACvB4L,GACA3B,GAAS,KACLlI,KAAK8C,UAAW,EAChB9C,KAAKgB,aAAa,QAAQ,GAC3BhB,KAAKmC,aACX,GAER,CACJ,CACDmB,UACI,IAAImD,EACsB,QAAzBA,EAAKzG,KAAKmK,iBAA8B,IAAP1D,GAAyBA,EAAGpD,OACjE,GD/DD6I,QHQG,cAAsBrJ,EAOzBL,YAAYP,GAGR,GAFAW,MAAMX,GACNjC,KAAKkM,SAAU,EACS,oBAAbC,SAA0B,CACjC,MAAMC,EAAQ,WAAaD,SAASE,SACpC,IAAI5H,EAAO0H,SAAS1H,KAEfA,IACDA,EAAO2H,EAAQ,MAAQ,MAE3BpM,KAAK0G,GACoB,oBAAbyF,UACJlK,EAAKsC,WAAa4H,SAAS5H,UAC3BE,IAASxC,EAAKwC,IACzB,CAID,MAAMoE,EAAc5G,GAAQA,EAAK4G,YACjC7I,KAAK/E,eAAiBkL,IAAY0C,EAC9B7I,KAAKiC,KAAKgF,kBACVjH,KAAK+G,eAAYuF,EAExB,CACGxD,WACA,MAAO,SACV,CAOD1F,SACIpD,KAAKuM,MACR,CAODxI,MAAMC,GACFhE,KAAKmD,WAAa,UAClB,MAAMY,EAAQ,KACV/D,KAAKmD,WAAa,SAClBa,GAAS,EAEb,GAAIhE,KAAKkM,UAAYlM,KAAK8C,SAAU,CAChC,IAAI0J,EAAQ,EACRxM,KAAKkM,UACLM,IACAxM,KAAKG,KAAK,gBAAgB,aACpBqM,GAASzI,GAC/B,KAEiB/D,KAAK8C,WACN0J,IACAxM,KAAKG,KAAK,SAAS,aACbqM,GAASzI,GAC/B,IAES,MAEGA,GAEP,CAMDwI,OACIvM,KAAKkM,SAAU,EACflM,KAAKyM,SACLzM,KAAKgB,aAAa,OACrB,CAMD4C,OAAOvJ,GTpFW,EAACqS,EAAgBpQ,KACnC,MAAMqQ,EAAiBD,EAAejR,MAAM+B,GACtCiG,EAAU,GAChB,IAAK,IAAIvH,EAAI,EAAGA,EAAIyQ,EAAehQ,OAAQT,IAAK,CAC5C,MAAM0Q,EAAgBxQ,EAAauQ,EAAezQ,GAAII,GAEtD,GADAmH,EAAQvD,KAAK0M,GACc,UAAvBA,EAAcxS,KACd,KAEP,CACD,OAAOqJ,CAAO,ESyFVoJ,CAAcxS,EAAM2F,KAAKgD,OAAO1G,YAAYrC,SAd1B6D,IAMd,GAJI,YAAckC,KAAKmD,YAA8B,SAAhBrF,EAAO1D,MACxC4F,KAAK2D,SAGL,UAAY7F,EAAO1D,KAEnB,OADA4F,KAAKuD,QAAQ,CAAEb,YAAa,oCACrB,EAGX1C,KAAK6D,SAAS/F,EAAO,IAKrB,WAAakC,KAAKmD,aAElBnD,KAAKkM,SAAU,EACflM,KAAKgB,aAAa,gBACd,SAAWhB,KAAKmD,YAChBnD,KAAKuM,OAKhB,CAMDjJ,UACI,MAAMD,EAAQ,KACVrD,KAAK0D,MAAM,CAAC,CAAEtJ,KAAM,UAAW,EAE/B,SAAW4F,KAAKmD,WAChBE,IAKArD,KAAKG,KAAK,OAAQkD,EAEzB,CAODK,MAAMD,GACFzD,KAAK8C,UAAW,ETxJF,EAACW,EAASvI,KAE5B,MAAMyB,EAAS8G,EAAQ9G,OACjBgQ,EAAiB,IAAI5L,MAAMpE,GACjC,IAAImQ,EAAQ,EACZrJ,EAAQxJ,SAAQ,CAAC6D,EAAQ5B,KAErBlB,EAAa8C,GAAQ,GAAOzB,IACxBsQ,EAAezQ,GAAKG,IACdyQ,IAAUnQ,GACZzB,EAASyR,EAAe1G,KAAKzI,GAChC,GACH,GACJ,ES4IEuP,CAActJ,GAAUpJ,IACpB2F,KAAKgN,QAAQ3S,GAAM,KACf2F,KAAK8C,UAAW,EAChB9C,KAAKgB,aAAa,QAAQ,GAC5B,GAET,CAMDsF,MACI,MAAMpC,EAASlE,KAAKiC,KAAKyC,OAAS,QAAU,OACtC3B,EAAQ/C,KAAK+C,OAAS,GAQ5B,OANI,IAAU/C,KAAKiC,KAAK6H,oBACpB/G,EAAM/C,KAAKiC,KAAK8H,gBAAkBzE,KAEjCtF,KAAK/E,gBAAmB8H,EAAMkJ,MAC/BlJ,EAAMiH,IAAM,GAEThK,KAAKiE,UAAUC,EAAQnB,EACjC,CAODkK,QAAQhL,EAAO,IAEX,OADApI,OAAOqT,OAAOjL,EAAM,CAAEyE,GAAI1G,KAAK0G,GAAIK,UAAW/G,KAAK+G,WAAa/G,KAAKiC,MAC9D,IAAIoE,EAAQrG,KAAKsG,MAAOrE,EAClC,CAQD+K,QAAQ3S,EAAM0F,GACV,MAAMoN,EAAMnN,KAAKiN,QAAQ,CACrB1G,OAAQ,OACRlM,KAAMA,IAEV8S,EAAIvN,GAAG,UAAWG,GAClBoN,EAAIvN,GAAG,SAAS,CAACwN,EAAWzK,KACxB3C,KAAKiD,QAAQ,iBAAkBmK,EAAWzK,EAAQ,GAEzD,CAMD8J,SACI,MAAMU,EAAMnN,KAAKiN,UACjBE,EAAIvN,GAAG,OAAQI,KAAK4D,OAAOxB,KAAKpC,OAChCmN,EAAIvN,GAAG,SAAS,CAACwN,EAAWzK,KACxB3C,KAAKiD,QAAQ,iBAAkBmK,EAAWzK,EAAQ,IAEtD3C,KAAKqN,QAAUF,CAClB,IKzMCG,EAAK,sPACLC,EAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,EAAM3I,GAClB,MAAM4I,EAAM5I,EAAK6I,EAAI7I,EAAIL,QAAQ,KAAMuB,EAAIlB,EAAIL,QAAQ,MAC7C,GAANkJ,IAAiB,GAAN3H,IACXlB,EAAMA,EAAInI,UAAU,EAAGgR,GAAK7I,EAAInI,UAAUgR,EAAG3H,GAAG4H,QAAQ,KAAM,KAAO9I,EAAInI,UAAUqJ,EAAGlB,EAAIlI,SAE9F,IAAIiR,EAAIN,EAAGO,KAAKhJ,GAAO,IAAKyB,EAAM,CAAA,EAAIpK,EAAI,GAC1C,KAAOA,KACHoK,EAAIiH,EAAMrR,IAAM0R,EAAE1R,IAAM,GAU5B,OARU,GAANwR,IAAiB,GAAN3H,IACXO,EAAIwH,OAASL,EACbnH,EAAIyH,KAAOzH,EAAIyH,KAAKrR,UAAU,EAAG4J,EAAIyH,KAAKpR,OAAS,GAAGgR,QAAQ,KAAM,KACpErH,EAAI0H,UAAY1H,EAAI0H,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9ErH,EAAI2H,SAAU,GAElB3H,EAAI4H,UAIR,SAAmBpT,EAAKuJ,GACpB,MAAM8J,EAAO,WAAYC,EAAQ/J,EAAKsJ,QAAQQ,EAAM,KAAK1S,MAAM,KACvC,KAApB4I,EAAK5E,MAAM,EAAG,IAA6B,IAAhB4E,EAAK1H,QAChCyR,EAAMxN,OAAO,EAAG,GAEE,KAAlByD,EAAK5E,OAAO,IACZ2O,EAAMxN,OAAOwN,EAAMzR,OAAS,EAAG,GAEnC,OAAOyR,CACX,CAboBF,CAAU5H,EAAKA,EAAU,MACzCA,EAAI+H,SAaR,SAAkB/H,EAAKvD,GACnB,MAAM1I,EAAO,CAAA,EAMb,OALA0I,EAAM4K,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACAlU,EAAKkU,GAAMC,EAEvB,IACWnU,CACX,CArBmBgU,CAAS/H,EAAKA,EAAW,OACjCA,CACX,CClCO,MAAMmI,UAAe/O,EAOxB8C,YAAY8D,EAAKrE,EAAO,IACpBW,QACA5C,KAAK1D,WLJoB,cKKzB0D,KAAK0O,YAAc,GACfpI,GAAO,iBAAoBA,IAC3BrE,EAAOqE,EACPA,EAAM,MAENA,GACAA,EAAMkH,EAAMlH,GACZrE,EAAKsC,SAAW+B,EAAIyH,KACpB9L,EAAKyC,OAA0B,UAAjB4B,EAAI+F,UAAyC,QAAjB/F,EAAI+F,SAC9CpK,EAAKwC,KAAO6B,EAAI7B,KACZ6B,EAAIvD,QACJd,EAAKc,MAAQuD,EAAIvD,QAEhBd,EAAK8L,OACV9L,EAAKsC,SAAWiJ,EAAMvL,EAAK8L,MAAMA,MAErC/L,EAAsBhC,KAAMiC,GAC5BjC,KAAK0E,OACD,MAAQzC,EAAKyC,OACPzC,EAAKyC,OACe,oBAAbyH,UAA4B,WAAaA,SAASE,SAC/DpK,EAAKsC,WAAatC,EAAKwC,OAEvBxC,EAAKwC,KAAOzE,KAAK0E,OAAS,MAAQ,MAEtC1E,KAAKuE,SACDtC,EAAKsC,WACoB,oBAAb4H,SAA2BA,SAAS5H,SAAW,aAC/DvE,KAAKyE,KACDxC,EAAKwC,OACoB,oBAAb0H,UAA4BA,SAAS1H,KACvC0H,SAAS1H,KACTzE,KAAK0E,OACD,MACA,MAClB1E,KAAK2I,WAAa1G,EAAK0G,YAAc,CACjC,UACA,YACA,gBAEJ3I,KAAK0O,YAAc,GACnB1O,KAAK2O,cAAgB,EACrB3O,KAAKiC,KAAOpI,OAAOqT,OAAO,CACtB7I,KAAM,aACNuK,OAAO,EACP3H,iBAAiB,EACjB4H,SAAS,EACT9E,eAAgB,IAChB+E,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEf9E,iBAAkB,CAAE,EACpB+E,qBAAqB,GACtBlN,GACHjC,KAAKiC,KAAKoC,KACNrE,KAAKiC,KAAKoC,KAAKsJ,QAAQ,MAAO,KACzB3N,KAAKiC,KAAK8M,iBAAmB,IAAM,IACb,iBAApB/O,KAAKiC,KAAKc,QACjB/C,KAAKiC,KAAKc,MVrDf,SAAgBqM,GACnB,IAAIC,EAAM,CAAA,EACNC,EAAQF,EAAG3T,MAAM,KACrB,IAAK,IAAIS,EAAI,EAAGqT,EAAID,EAAM3S,OAAQT,EAAIqT,EAAGrT,IAAK,CAC1C,IAAIsT,EAAOF,EAAMpT,GAAGT,MAAM,KAC1B4T,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,GAC9D,CACD,OAAOH,CACX,CU6C8B9R,CAAOyC,KAAKiC,KAAKc,QAGvC/C,KAAK0P,GAAK,KACV1P,KAAK2P,SAAW,KAChB3P,KAAK4P,aAAe,KACpB5P,KAAK6P,YAAc,KAEnB7P,KAAK8P,iBAAmB,KACQ,mBAArBjQ,mBACHG,KAAKiC,KAAKkN,sBAIVnP,KAAK+P,0BAA4B,KACzB/P,KAAKmK,YAELnK,KAAKmK,UAAU3J,qBACfR,KAAKmK,UAAU9G,QAClB,EAELxD,iBAAiB,eAAgBG,KAAK+P,2BAA2B,IAE/C,cAAlB/P,KAAKuE,WACLvE,KAAKgQ,qBAAuB,KACxBhQ,KAAKuD,QAAQ,kBAAmB,CAC5Bb,YAAa,2BACf,EAEN7C,iBAAiB,UAAWG,KAAKgQ,sBAAsB,KAG/DhQ,KAAKkD,MACR,CAQD+M,gBAAgBnH,GACZ,MAAM/F,EAAQlJ,OAAOqT,OAAO,CAAE,EAAElN,KAAKiC,KAAKc,OAE1CA,EAAMmN,IfgCU,Ee9BhBnN,EAAMoH,UAAYrB,EAEd9I,KAAK0P,KACL3M,EAAMkJ,IAAMjM,KAAK0P,IACrB,MAAMzN,EAAOpI,OAAOqT,OAAO,CAAA,EAAIlN,KAAKiC,KAAM,CACtCc,QACAC,OAAQhD,KACRuE,SAAUvE,KAAKuE,SACfG,OAAQ1E,KAAK0E,OACbD,KAAMzE,KAAKyE,MACZzE,KAAKiC,KAAKmI,iBAAiBtB,IAC9B,OAAO,IAAIH,EAAWG,GAAM7G,EAC/B,CAMDiB,OACI,IAAIiH,EACJ,GAAInK,KAAKiC,KAAK6M,iBACVL,EAAO0B,wBACmC,IAA1CnQ,KAAK2I,WAAWnE,QAAQ,aACxB2F,EAAY,gBAEX,IAAI,IAAMnK,KAAK2I,WAAWhM,OAK3B,YAHAqD,KAAKmC,cAAa,KACdnC,KAAKgB,aAAa,QAAS,0BAA0B,GACtD,GAIHmJ,EAAYnK,KAAK2I,WAAW,EAC/B,CACD3I,KAAKmD,WAAa,UAElB,IACIgH,EAAYnK,KAAKiQ,gBAAgB9F,EACpC,CACD,MAAOpE,GAGH,OAFA/F,KAAK2I,WAAWpJ,aAChBS,KAAKkD,MAER,CACDiH,EAAUjH,OACVlD,KAAKoQ,aAAajG,EACrB,CAMDiG,aAAajG,GACLnK,KAAKmK,WACLnK,KAAKmK,UAAU3J,qBAGnBR,KAAKmK,UAAYA,EAEjBA,EACKvK,GAAG,QAASI,KAAKqQ,QAAQjO,KAAKpC,OAC9BJ,GAAG,SAAUI,KAAK6D,SAASzB,KAAKpC,OAChCJ,GAAG,QAASI,KAAKiD,QAAQb,KAAKpC,OAC9BJ,GAAG,SAAU6C,GAAWzC,KAAKuD,QAAQ,kBAAmBd,IAChE,CAOD6N,MAAMxH,GACF,IAAIqB,EAAYnK,KAAKiQ,gBAAgBnH,GACjCyH,GAAS,EACb9B,EAAO0B,uBAAwB,EAC/B,MAAMK,EAAkB,KAChBD,IAEJpG,EAAU3G,KAAK,CAAC,CAAEpJ,KAAM,OAAQC,KAAM,WACtC8P,EAAUhK,KAAK,UAAWsQ,IACtB,IAAIF,EAEJ,GAAI,SAAWE,EAAIrW,MAAQ,UAAYqW,EAAIpW,KAAM,CAG7C,GAFA2F,KAAK0Q,WAAY,EACjB1Q,KAAKgB,aAAa,YAAamJ,IAC1BA,EACD,OACJsE,EAAO0B,sBAAwB,cAAgBhG,EAAUrB,KACzD9I,KAAKmK,UAAUpG,OAAM,KACbwM,GAEA,WAAavQ,KAAKmD,aAEtByE,IACA5H,KAAKoQ,aAAajG,GAClBA,EAAU3G,KAAK,CAAC,CAAEpJ,KAAM,aACxB4F,KAAKgB,aAAa,UAAWmJ,GAC7BA,EAAY,KACZnK,KAAK0Q,WAAY,EACjB1Q,KAAK2Q,QAAO,GAEnB,KACI,CACD,MAAMhL,EAAM,IAAIpD,MAAM,eAEtBoD,EAAIwE,UAAYA,EAAUrB,KAC1B9I,KAAKgB,aAAa,eAAgB2E,EACrC,KACH,EAEN,SAASiL,IACDL,IAGJA,GAAS,EACT3I,IACAuC,EAAU9G,QACV8G,EAAY,KACf,CAED,MAAMP,EAAWjE,IACb,MAAMkL,EAAQ,IAAItO,MAAM,gBAAkBoD,GAE1CkL,EAAM1G,UAAYA,EAAUrB,KAC5B8H,IACA5Q,KAAKgB,aAAa,eAAgB6P,EAAM,EAE5C,SAASC,IACLlH,EAAQ,mBACX,CAED,SAASJ,IACLI,EAAQ,gBACX,CAED,SAASmH,EAAUC,GACX7G,GAAa6G,EAAGlI,OAASqB,EAAUrB,MACnC8H,GAEP,CAED,MAAMhJ,EAAU,KACZuC,EAAU5J,eAAe,OAAQiQ,GACjCrG,EAAU5J,eAAe,QAASqJ,GAClCO,EAAU5J,eAAe,QAASuQ,GAClC9Q,KAAKI,IAAI,QAASoJ,GAClBxJ,KAAKI,IAAI,YAAa2Q,EAAU,EAEpC5G,EAAUhK,KAAK,OAAQqQ,GACvBrG,EAAUhK,KAAK,QAASyJ,GACxBO,EAAUhK,KAAK,QAAS2Q,GACxB9Q,KAAKG,KAAK,QAASqJ,GACnBxJ,KAAKG,KAAK,YAAa4Q,IACwB,IAA3C/Q,KAAK2P,SAASnL,QAAQ,iBACb,iBAATsE,EAEA9I,KAAKmC,cAAa,KACToO,GACDpG,EAAUjH,MACb,GACF,KAGHiH,EAAUjH,MAEjB,CAMDS,SAOI,GANA3D,KAAKmD,WAAa,OAClBsL,EAAO0B,sBAAwB,cAAgBnQ,KAAKmK,UAAUrB,KAC9D9I,KAAKgB,aAAa,QAClBhB,KAAK2Q,QAGD,SAAW3Q,KAAKmD,YAAcnD,KAAKiC,KAAK4M,QAAS,CACjD,IAAI3S,EAAI,EACR,MAAMqT,EAAIvP,KAAK2P,SAAShT,OACxB,KAAOT,EAAIqT,EAAGrT,IACV8D,KAAKsQ,MAAMtQ,KAAK2P,SAASzT,GAEhC,CACJ,CAMD2H,SAAS/F,GACL,GAAI,YAAckC,KAAKmD,YACnB,SAAWnD,KAAKmD,YAChB,YAAcnD,KAAKmD,WAKnB,OAJAnD,KAAKgB,aAAa,SAAUlD,GAE5BkC,KAAKgB,aAAa,aAClBhB,KAAKiR,mBACGnT,EAAO1D,MACX,IAAK,OACD4F,KAAKkR,YAAYC,KAAK3D,MAAM1P,EAAOzD,OACnC,MACJ,IAAK,OACD2F,KAAKoR,WAAW,QAChBpR,KAAKgB,aAAa,QAClBhB,KAAKgB,aAAa,QAClB,MACJ,IAAK,QACD,MAAM2E,EAAM,IAAIpD,MAAM,gBAEtBoD,EAAI0L,KAAOvT,EAAOzD,KAClB2F,KAAKiD,QAAQ0C,GACb,MACJ,IAAK,UACD3F,KAAKgB,aAAa,OAAQlD,EAAOzD,MACjC2F,KAAKgB,aAAa,UAAWlD,EAAOzD,MAMnD,CAOD6W,YAAY7W,GACR2F,KAAKgB,aAAa,YAAa3G,GAC/B2F,KAAK0P,GAAKrV,EAAK4R,IACfjM,KAAKmK,UAAUpH,MAAMkJ,IAAM5R,EAAK4R,IAChCjM,KAAK2P,SAAW3P,KAAKsR,eAAejX,EAAKsV,UACzC3P,KAAK4P,aAAevV,EAAKuV,aACzB5P,KAAK6P,YAAcxV,EAAKwV,YACxB7P,KAAK2K,WAAatQ,EAAKsQ,WACvB3K,KAAK2D,SAED,WAAa3D,KAAKmD,YAEtBnD,KAAKiR,kBACR,CAMDA,mBACIjR,KAAKqC,eAAerC,KAAK8P,kBACzB9P,KAAK8P,iBAAmB9P,KAAKmC,cAAa,KACtCnC,KAAKuD,QAAQ,eAAe,GAC7BvD,KAAK4P,aAAe5P,KAAK6P,aACxB7P,KAAKiC,KAAKoH,WACVrJ,KAAK8P,iBAAiBvG,OAE7B,CAMD8G,UACIrQ,KAAK0O,YAAY9N,OAAO,EAAGZ,KAAK2O,eAIhC3O,KAAK2O,cAAgB,EACjB,IAAM3O,KAAK0O,YAAY/R,OACvBqD,KAAKgB,aAAa,SAGlBhB,KAAK2Q,OAEZ,CAMDA,QACI,GAAI,WAAa3Q,KAAKmD,YAClBnD,KAAKmK,UAAUrH,WACd9C,KAAK0Q,WACN1Q,KAAK0O,YAAY/R,OAAQ,CACzB,MAAM8G,EAAUzD,KAAKuR,qBACrBvR,KAAKmK,UAAU3G,KAAKC,GAGpBzD,KAAK2O,cAAgBlL,EAAQ9G,OAC7BqD,KAAKgB,aAAa,QACrB,CACJ,CAODuQ,qBAII,KAH+BvR,KAAK2K,YACR,YAAxB3K,KAAKmK,UAAUrB,MACf9I,KAAK0O,YAAY/R,OAAS,GAE1B,OAAOqD,KAAK0O,YAEhB,IAAI8C,EAAc,EAClB,IAAK,IAAItV,EAAI,EAAGA,EAAI8D,KAAK0O,YAAY/R,OAAQT,IAAK,CAC9C,MAAM7B,EAAO2F,KAAK0O,YAAYxS,GAAG7B,KAIjC,GAHIA,IACAmX,GZzZO,iBADI1W,EY0ZeT,GZnZ1C,SAAoBwK,GAChB,IAAI4M,EAAI,EAAG9U,EAAS,EACpB,IAAK,IAAIT,EAAI,EAAGqT,EAAI1K,EAAIlI,OAAQT,EAAIqT,EAAGrT,IACnCuV,EAAI5M,EAAI1I,WAAWD,GACfuV,EAAI,IACJ9U,GAAU,EAEL8U,EAAI,KACT9U,GAAU,EAEL8U,EAAI,OAAUA,GAAK,MACxB9U,GAAU,GAGVT,IACAS,GAAU,GAGlB,OAAOA,CACX,CAxBe+U,CAAW5W,GAGfsK,KAAKuM,KAPQ,MAOF7W,EAAIgB,YAAchB,EAAIwE,QYuZ5BpD,EAAI,GAAKsV,EAAcxR,KAAK2K,WAC5B,OAAO3K,KAAK0O,YAAYjP,MAAM,EAAGvD,GAErCsV,GAAe,CAClB,CZhaF,IAAoB1W,EYianB,OAAOkF,KAAK0O,WACf,CASDhL,MAAM+M,EAAKmB,EAAS7R,GAEhB,OADAC,KAAKoR,WAAW,UAAWX,EAAKmB,EAAS7R,GAClCC,IACV,CACDwD,KAAKiN,EAAKmB,EAAS7R,GAEf,OADAC,KAAKoR,WAAW,UAAWX,EAAKmB,EAAS7R,GAClCC,IACV,CAUDoR,WAAWhX,EAAMC,EAAMuX,EAAS7R,GAS5B,GARI,mBAAsB1F,IACtB0F,EAAK1F,EACLA,OAAOmM,GAEP,mBAAsBoL,IACtB7R,EAAK6R,EACLA,EAAU,MAEV,YAAc5R,KAAKmD,YAAc,WAAanD,KAAKmD,WACnD,QAEJyO,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,MAAM/T,EAAS,CACX1D,KAAMA,EACNC,KAAMA,EACNuX,QAASA,GAEb5R,KAAKgB,aAAa,eAAgBlD,GAClCkC,KAAK0O,YAAYxO,KAAKpC,GAClBiC,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAK2Q,OACR,CAIDtN,QACI,MAAMA,EAAQ,KACVrD,KAAKuD,QAAQ,gBACbvD,KAAKmK,UAAU9G,OAAO,EAEpByO,EAAkB,KACpB9R,KAAKI,IAAI,UAAW0R,GACpB9R,KAAKI,IAAI,eAAgB0R,GACzBzO,GAAO,EAEL0O,EAAiB,KAEnB/R,KAAKG,KAAK,UAAW2R,GACrB9R,KAAKG,KAAK,eAAgB2R,EAAgB,EAqB9C,MAnBI,YAAc9R,KAAKmD,YAAc,SAAWnD,KAAKmD,aACjDnD,KAAKmD,WAAa,UACdnD,KAAK0O,YAAY/R,OACjBqD,KAAKG,KAAK,SAAS,KACXH,KAAK0Q,UACLqB,IAGA1O,GACH,IAGArD,KAAK0Q,UACVqB,IAGA1O,KAGDrD,IACV,CAMDiD,QAAQ0C,GACJ8I,EAAO0B,uBAAwB,EAC/BnQ,KAAKgB,aAAa,QAAS2E,GAC3B3F,KAAKuD,QAAQ,kBAAmBoC,EACnC,CAMDpC,QAAQd,EAAQC,GACR,YAAc1C,KAAKmD,YACnB,SAAWnD,KAAKmD,YAChB,YAAcnD,KAAKmD,aAEnBnD,KAAKqC,eAAerC,KAAK8P,kBAEzB9P,KAAKmK,UAAU3J,mBAAmB,SAElCR,KAAKmK,UAAU9G,QAEfrD,KAAKmK,UAAU3J,qBACoB,mBAAxBC,sBACPA,oBAAoB,eAAgBT,KAAK+P,2BAA2B,GACpEtP,oBAAoB,UAAWT,KAAKgQ,sBAAsB,IAG9DhQ,KAAKmD,WAAa,SAElBnD,KAAK0P,GAAK,KAEV1P,KAAKgB,aAAa,QAASyB,EAAQC,GAGnC1C,KAAK0O,YAAc,GACnB1O,KAAK2O,cAAgB,EAE5B,CAOD2C,eAAe3B,GACX,MAAMqC,EAAmB,GACzB,IAAI9V,EAAI,EACR,MAAMsD,EAAImQ,EAAShT,OACnB,KAAOT,EAAIsD,EAAGtD,KACL8D,KAAK2I,WAAWnE,QAAQmL,EAASzT,KAClC8V,EAAiB9R,KAAKyP,EAASzT,IAEvC,OAAO8V,CACV,EAELvD,EAAOpC,SfvbiB,EgB1JxB,MAAM1R,EAA+C,mBAAhBC,YAC/BC,EAAUC,GACyB,mBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,EAAIC,kBAAkBH,YAE1BH,GAAWZ,OAAOW,UAAUC,SAC5BH,GAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBE,GAASC,KAAKH,MAChB0X,GAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBzX,GAASC,KAAKwX,MAMf,SAASnH,GAASjQ,GACrB,OAASH,IAA0BG,aAAeF,aAAeC,EAAOC,KACnER,IAAkBQ,aAAeP,MACjC0X,IAAkBnX,aAAeoX,IAC1C,CACO,SAASC,GAAUrX,EAAKsX,GAC3B,IAAKtX,GAAsB,iBAARA,EACf,OAAO,EAEX,GAAIiG,MAAMsR,QAAQvX,GAAM,CACpB,IAAK,IAAIoB,EAAI,EAAGqT,EAAIzU,EAAI6B,OAAQT,EAAIqT,EAAGrT,IACnC,GAAIiW,GAAUrX,EAAIoB,IACd,OAAO,EAGf,OAAO,CACV,CACD,GAAI6O,GAASjQ,GACT,OAAO,EAEX,GAAIA,EAAIsX,QACkB,mBAAftX,EAAIsX,QACU,IAArB9R,UAAU3D,OACV,OAAOwV,GAAUrX,EAAIsX,UAAU,GAEnC,IAAK,MAAMlY,KAAOY,EACd,GAAIjB,OAAOW,UAAUkH,eAAehH,KAAKI,EAAKZ,IAAQiY,GAAUrX,EAAIZ,IAChE,OAAO,EAGf,OAAO,CACX,CCzCO,SAASoY,GAAkBxU,GAC9B,MAAMyU,EAAU,GACVC,EAAa1U,EAAOzD,KACpBoY,EAAO3U,EAGb,OAFA2U,EAAKpY,KAAOqY,GAAmBF,EAAYD,GAC3CE,EAAKE,YAAcJ,EAAQ5V,OACpB,CAAEmB,OAAQ2U,EAAMF,QAASA,EACpC,CACA,SAASG,GAAmBrY,EAAMkY,GAC9B,IAAKlY,EACD,OAAOA,EACX,GAAI0Q,GAAS1Q,GAAO,CAChB,MAAMuY,EAAc,CAAEC,cAAc,EAAM1N,IAAKoN,EAAQ5V,QAEvD,OADA4V,EAAQrS,KAAK7F,GACNuY,CACV,CACI,GAAI7R,MAAMsR,QAAQhY,GAAO,CAC1B,MAAMyY,EAAU,IAAI/R,MAAM1G,EAAKsC,QAC/B,IAAK,IAAIT,EAAI,EAAGA,EAAI7B,EAAKsC,OAAQT,IAC7B4W,EAAQ5W,GAAKwW,GAAmBrY,EAAK6B,GAAIqW,GAE7C,OAAOO,CACV,CACI,GAAoB,iBAATzY,KAAuBA,aAAgBmL,MAAO,CAC1D,MAAMsN,EAAU,CAAA,EAChB,IAAK,MAAM5Y,KAAOG,EACVR,OAAOW,UAAUkH,eAAehH,KAAKL,EAAMH,KAC3C4Y,EAAQ5Y,GAAOwY,GAAmBrY,EAAKH,GAAMqY,IAGrD,OAAOO,CACV,CACD,OAAOzY,CACX,CASO,SAAS0Y,GAAkBjV,EAAQyU,GAGtC,OAFAzU,EAAOzD,KAAO2Y,GAAmBlV,EAAOzD,KAAMkY,UACvCzU,EAAO6U,YACP7U,CACX,CACA,SAASkV,GAAmB3Y,EAAMkY,GAC9B,IAAKlY,EACD,OAAOA,EACX,GAAIA,IAA8B,IAAtBA,EAAKwY,aAAuB,CAIpC,GAHyC,iBAAbxY,EAAK8K,KAC7B9K,EAAK8K,KAAO,GACZ9K,EAAK8K,IAAMoN,EAAQ5V,OAEnB,OAAO4V,EAAQlY,EAAK8K,KAGpB,MAAM,IAAI5C,MAAM,sBAEvB,CACI,GAAIxB,MAAMsR,QAAQhY,GACnB,IAAK,IAAI6B,EAAI,EAAGA,EAAI7B,EAAKsC,OAAQT,IAC7B7B,EAAK6B,GAAK8W,GAAmB3Y,EAAK6B,GAAIqW,QAGzC,GAAoB,iBAATlY,EACZ,IAAK,MAAMH,KAAOG,EACVR,OAAOW,UAAUkH,eAAehH,KAAKL,EAAMH,KAC3CG,EAAKH,GAAO8Y,GAAmB3Y,EAAKH,GAAMqY,IAItD,OAAOlY,CACX,CC5EA,MAAM4Y,GAAkB,CACpB,UACA,gBACA,aACA,gBACA,cACA,kBAOS5G,GAAW,EACjB,IAAI6G,IACX,SAAWA,GACPA,EAAWA,EAAoB,QAAI,GAAK,UACxCA,EAAWA,EAAuB,WAAI,GAAK,aAC3CA,EAAWA,EAAkB,MAAI,GAAK,QACtCA,EAAWA,EAAgB,IAAI,GAAK,MACpCA,EAAWA,EAA0B,cAAI,GAAK,gBAC9CA,EAAWA,EAAyB,aAAI,GAAK,eAC7CA,EAAWA,EAAuB,WAAI,GAAK,YAC9C,CARD,CAQGA,KAAeA,GAAa,CAAE,IA0EjC,SAASC,GAAS1N,GACd,MAAiD,oBAA1C5L,OAAOW,UAAUC,SAASC,KAAK+K,EAC1C,CAMO,MAAM2N,WAAgB1T,EAMzB8C,YAAY6Q,GACRzQ,QACA5C,KAAKqT,QAAUA,CAClB,CAMDC,IAAIxY,GACA,IAAIgD,EACJ,GAAmB,iBAARhD,EAAkB,CACzB,GAAIkF,KAAKuT,cACL,MAAM,IAAIhR,MAAM,mDAEpBzE,EAASkC,KAAKwT,aAAa1Y,GAC3B,MAAM2Y,EAAgB3V,EAAO1D,OAAS8Y,GAAWQ,aAC7CD,GAAiB3V,EAAO1D,OAAS8Y,GAAWS,YAC5C7V,EAAO1D,KAAOqZ,EAAgBP,GAAWU,MAAQV,GAAWW,IAE5D7T,KAAKuT,cAAgB,IAAIO,GAAoBhW,GAElB,IAAvBA,EAAO6U,aACP/P,MAAM5B,aAAa,UAAWlD,IAKlC8E,MAAM5B,aAAa,UAAWlD,EAErC,KACI,KAAIiN,GAASjQ,KAAQA,EAAI+B,OAe1B,MAAM,IAAI0F,MAAM,iBAAmBzH,GAbnC,IAAKkF,KAAKuT,cACN,MAAM,IAAIhR,MAAM,oDAGhBzE,EAASkC,KAAKuT,cAAcQ,eAAejZ,GACvCgD,IAEAkC,KAAKuT,cAAgB,KACrB3Q,MAAM5B,aAAa,UAAWlD,GAMzC,CACJ,CAOD0V,aAAa3O,GACT,IAAI3I,EAAI,EAER,MAAMkB,EAAI,CACNhD,KAAMuK,OAAOE,EAAIrI,OAAO,KAE5B,QAA2BgK,IAAvB0M,GAAW9V,EAAEhD,MACb,MAAM,IAAImI,MAAM,uBAAyBnF,EAAEhD,MAG/C,GAAIgD,EAAEhD,OAAS8Y,GAAWQ,cACtBtW,EAAEhD,OAAS8Y,GAAWS,WAAY,CAClC,MAAMK,EAAQ9X,EAAI,EAClB,KAA2B,MAApB2I,EAAIrI,SAASN,IAAcA,GAAK2I,EAAIlI,SAC3C,MAAMsX,EAAMpP,EAAInI,UAAUsX,EAAO9X,GACjC,GAAI+X,GAAOtP,OAAOsP,IAA0B,MAAlBpP,EAAIrI,OAAON,GACjC,MAAM,IAAIqG,MAAM,uBAEpBnF,EAAEuV,YAAchO,OAAOsP,EAC1B,CAED,GAAI,MAAQpP,EAAIrI,OAAON,EAAI,GAAI,CAC3B,MAAM8X,EAAQ9X,EAAI,EAClB,OAASA,GAAG,CAER,GAAI,MADM2I,EAAIrI,OAAON,GAEjB,MACJ,GAAIA,IAAM2I,EAAIlI,OACV,KACP,CACDS,EAAE8W,IAAMrP,EAAInI,UAAUsX,EAAO9X,EAChC,MAEGkB,EAAE8W,IAAM,IAGZ,MAAMC,EAAOtP,EAAIrI,OAAON,EAAI,GAC5B,GAAI,KAAOiY,GAAQxP,OAAOwP,IAASA,EAAM,CACrC,MAAMH,EAAQ9X,EAAI,EAClB,OAASA,GAAG,CACR,MAAMuV,EAAI5M,EAAIrI,OAAON,GACrB,GAAI,MAAQuV,GAAK9M,OAAO8M,IAAMA,EAAG,GAC3BvV,EACF,KACH,CACD,GAAIA,IAAM2I,EAAIlI,OACV,KACP,CACDS,EAAEsS,GAAK/K,OAAOE,EAAInI,UAAUsX,EAAO9X,EAAI,GAC1C,CAED,GAAI2I,EAAIrI,SAASN,GAAI,CACjB,MAAMkY,EAAUpU,KAAKqU,SAASxP,EAAIyP,OAAOpY,IACzC,IAAIkX,GAAQmB,eAAenX,EAAEhD,KAAMga,GAI/B,MAAM,IAAI7R,MAAM,mBAHhBnF,EAAE/C,KAAO+Z,CAKhB,CACD,OAAOhX,CACV,CACDiX,SAASxP,GACL,IACI,OAAOsM,KAAK3D,MAAM3I,EAAK7E,KAAKqT,QAC/B,CACD,MAAOtN,GACH,OAAO,CACV,CACJ,CACDyO,sBAAsBpa,EAAMga,GACxB,OAAQha,GACJ,KAAK8Y,GAAWuB,QACZ,OAAOtB,GAASiB,GACpB,KAAKlB,GAAWwB,WACZ,YAAmBlO,IAAZ4N,EACX,KAAKlB,GAAWyB,cACZ,MAA0B,iBAAZP,GAAwBjB,GAASiB,GACnD,KAAKlB,GAAWU,MAChB,KAAKV,GAAWQ,aACZ,OAAQ3S,MAAMsR,QAAQ+B,KACK,iBAAfA,EAAQ,IACW,iBAAfA,EAAQ,KAC6B,IAAzCnB,GAAgBzO,QAAQ4P,EAAQ,KAChD,KAAKlB,GAAWW,IAChB,KAAKX,GAAWS,WACZ,OAAO5S,MAAMsR,QAAQ+B,GAEhC,CAIDQ,UACQ5U,KAAKuT,gBACLvT,KAAKuT,cAAcsB,yBACnB7U,KAAKuT,cAAgB,KAE5B,EAUL,MAAMO,GACFtR,YAAY1E,GACRkC,KAAKlC,OAASA,EACdkC,KAAKuS,QAAU,GACfvS,KAAK8U,UAAYhX,CACpB,CASDiW,eAAegB,GAEX,GADA/U,KAAKuS,QAAQrS,KAAK6U,GACd/U,KAAKuS,QAAQ5V,SAAWqD,KAAK8U,UAAUnC,YAAa,CAEpD,MAAM7U,EAASiV,GAAkB/S,KAAK8U,UAAW9U,KAAKuS,SAEtD,OADAvS,KAAK6U,yBACE/W,CACV,CACD,OAAO,IACV,CAID+W,yBACI7U,KAAK8U,UAAY,KACjB9U,KAAKuS,QAAU,EAClB,gDAlSmB,sCAcjB,MAMH/P,YAAYwS,GACRhV,KAAKgV,SAAWA,CACnB,CAOD5W,OAAOtD,GACH,OAAIA,EAAIV,OAAS8Y,GAAWU,OAAS9Y,EAAIV,OAAS8Y,GAAWW,MACrD1B,GAAUrX,GAWX,CAACkF,KAAKiV,eAAena,IAVbkF,KAAKkV,eAAe,CACvB9a,KAAMU,EAAIV,OAAS8Y,GAAWU,MACxBV,GAAWQ,aACXR,GAAWS,WACjBO,IAAKpZ,EAAIoZ,IACT7Z,KAAMS,EAAIT,KACVqV,GAAI5U,EAAI4U,IAKvB,CAIDuF,eAAena,GAEX,IAAI+J,EAAM,GAAK/J,EAAIV,KAmBnB,OAjBIU,EAAIV,OAAS8Y,GAAWQ,cACxB5Y,EAAIV,OAAS8Y,GAAWS,aACxB9O,GAAO/J,EAAI6X,YAAc,KAIzB7X,EAAIoZ,KAAO,MAAQpZ,EAAIoZ,MACvBrP,GAAO/J,EAAIoZ,IAAM,KAGjB,MAAQpZ,EAAI4U,KACZ7K,GAAO/J,EAAI4U,IAGX,MAAQ5U,EAAIT,OACZwK,GAAOsM,KAAKgE,UAAUra,EAAIT,KAAM2F,KAAKgV,WAElCnQ,CACV,CAMDqQ,eAAepa,GACX,MAAMsa,EAAiB9C,GAAkBxX,GACnC2X,EAAOzS,KAAKiV,eAAeG,EAAetX,QAC1CyU,EAAU6C,EAAe7C,QAE/B,OADAA,EAAQ8C,QAAQ5C,GACTF,CACV,gBCpGE,SAAS3S,GAAG9E,EAAK6O,EAAI5J,GAExB,OADAjF,EAAI8E,GAAG+J,EAAI5J,GACJ,WACHjF,EAAIsF,IAAIuJ,EAAI5J,EACpB,CACA,CCEA,MAAMkT,GAAkBpZ,OAAOyb,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACbpV,eAAgB,IA0Bb,MAAMkO,WAAe/O,EAIxB8C,YAAYoT,EAAI1B,EAAKjS,GACjBW,QAeA5C,KAAK6V,WAAY,EAKjB7V,KAAK8V,WAAY,EAIjB9V,KAAK+V,cAAgB,GAIrB/V,KAAKgW,WAAa,GAOlBhW,KAAKiW,OAAS,GAKdjW,KAAKkW,UAAY,EACjBlW,KAAKmW,IAAM,EACXnW,KAAKoW,KAAO,GACZpW,KAAKqW,MAAQ,GACbrW,KAAK4V,GAAKA,EACV5V,KAAKkU,IAAMA,EACPjS,GAAQA,EAAKqU,OACbtW,KAAKsW,KAAOrU,EAAKqU,MAErBtW,KAAKuW,MAAQ1c,OAAOqT,OAAO,CAAE,EAAEjL,GAC3BjC,KAAK4V,GAAGY,cACRxW,KAAKkD,MACZ,CAeGuT,mBACA,OAAQzW,KAAK6V,SAChB,CAMDa,YACI,GAAI1W,KAAK2W,KACL,OACJ,MAAMf,EAAK5V,KAAK4V,GAChB5V,KAAK2W,KAAO,CACR/W,GAAGgW,EAAI,OAAQ5V,KAAKoJ,OAAOhH,KAAKpC,OAChCJ,GAAGgW,EAAI,SAAU5V,KAAK4W,SAASxU,KAAKpC,OACpCJ,GAAGgW,EAAI,QAAS5V,KAAK4J,QAAQxH,KAAKpC,OAClCJ,GAAGgW,EAAI,QAAS5V,KAAKwJ,QAAQpH,KAAKpC,OAEzC,CAkBG6W,aACA,QAAS7W,KAAK2W,IACjB,CAWDpB,UACI,OAAIvV,KAAK6V,YAET7V,KAAK0W,YACA1W,KAAK4V,GAAkB,eACxB5V,KAAK4V,GAAG1S,OACR,SAAWlD,KAAK4V,GAAGkB,aACnB9W,KAAKoJ,UALEpJ,IAOd,CAIDkD,OACI,OAAOlD,KAAKuV,SACf,CAgBD/R,QAAQ1C,GAGJ,OAFAA,EAAKuU,QAAQ,WACbrV,KAAKa,KAAKR,MAAML,KAAMc,GACfd,IACV,CAkBDa,KAAK8I,KAAO7I,GACR,GAAImS,GAAgBvR,eAAeiI,GAC/B,MAAM,IAAIpH,MAAM,IAAMoH,EAAGlP,WAAa,8BAG1C,GADAqG,EAAKuU,QAAQ1L,GACT3J,KAAKuW,MAAMQ,UAAY/W,KAAKqW,MAAMW,YAAchX,KAAKqW,MAAMY,SAE3D,OADAjX,KAAKkX,YAAYpW,GACVd,KAEX,MAAMlC,EAAS,CACX1D,KAAM8Y,GAAWU,MACjBvZ,KAAMyG,EAEVhD,QAAiB,IAGjB,GAFAA,EAAO8T,QAAQC,UAAmC,IAAxB7R,KAAKqW,MAAMxE,SAEjC,mBAAsB/Q,EAAKA,EAAKnE,OAAS,GAAI,CAC7C,MAAM+S,EAAK1P,KAAKmW,MACVgB,EAAMrW,EAAKsW,MACjBpX,KAAKqX,qBAAqB3H,EAAIyH,GAC9BrZ,EAAO4R,GAAKA,CACf,CACD,MAAM4H,EAAsBtX,KAAK4V,GAAG2B,QAChCvX,KAAK4V,GAAG2B,OAAOpN,WACfnK,KAAK4V,GAAG2B,OAAOpN,UAAUrH,SAY7B,OAXsB9C,KAAKqW,MAAMY,YAAcK,IAAwBtX,KAAK6V,aAGnE7V,KAAK6V,WACV7V,KAAKwX,wBAAwB1Z,GAC7BkC,KAAKlC,OAAOA,IAGZkC,KAAKgW,WAAW9V,KAAKpC,IAEzBkC,KAAKqW,MAAQ,GACNrW,IACV,CAIDqX,qBAAqB3H,EAAIyH,GACrB,IAAI1Q,EACJ,MAAMU,EAAwC,QAA7BV,EAAKzG,KAAKqW,MAAMlP,eAA4B,IAAPV,EAAgBA,EAAKzG,KAAKuW,MAAMkB,WACtF,QAAgBjR,IAAZW,EAEA,YADAnH,KAAKoW,KAAK1G,GAAMyH,GAIpB,MAAMO,EAAQ1X,KAAK4V,GAAGzT,cAAa,YACxBnC,KAAKoW,KAAK1G,GACjB,IAAK,IAAIxT,EAAI,EAAGA,EAAI8D,KAAKgW,WAAWrZ,OAAQT,IACpC8D,KAAKgW,WAAW9Z,GAAGwT,KAAOA,GAC1B1P,KAAKgW,WAAWpV,OAAO1E,EAAG,GAGlCib,EAAIzc,KAAKsF,KAAM,IAAIuC,MAAM,2BAA2B,GACrD4E,GACHnH,KAAKoW,KAAK1G,GAAM,IAAI5O,KAEhBd,KAAK4V,GAAGvT,eAAeqV,GACvBP,EAAI9W,MAAML,KAAM,CAAC,QAASc,GAAM,CAEvC,CAiBD6W,YAAYhO,KAAO7I,GAEf,MAAM8W,OAAiCpR,IAAvBxG,KAAKqW,MAAMlP,cAAmDX,IAA1BxG,KAAKuW,MAAMkB,WAC/D,OAAO,IAAItP,SAAQ,CAACC,EAASyP,KACzB/W,EAAKZ,MAAK,CAAC4X,EAAMC,IACTH,EACOE,EAAOD,EAAOC,GAAQ1P,EAAQ2P,GAG9B3P,EAAQ0P,KAGvB9X,KAAKa,KAAK8I,KAAO7I,EAAK,GAE7B,CAMDoW,YAAYpW,GACR,IAAIqW,EACiC,mBAA1BrW,EAAKA,EAAKnE,OAAS,KAC1Bwa,EAAMrW,EAAKsW,OAEf,MAAMtZ,EAAS,CACX4R,GAAI1P,KAAKkW,YACT8B,SAAU,EACVC,SAAS,EACTnX,OACAuV,MAAOxc,OAAOqT,OAAO,CAAE8J,WAAW,GAAQhX,KAAKqW,QAEnDvV,EAAKZ,MAAK,CAACyF,KAAQuS,KACf,GAAIpa,IAAWkC,KAAKiW,OAAO,GAEvB,OAkBJ,OAhByB,OAARtQ,EAET7H,EAAOka,SAAWhY,KAAKuW,MAAMQ,UAC7B/W,KAAKiW,OAAO1W,QACR4X,GACAA,EAAIxR,KAKZ3F,KAAKiW,OAAO1W,QACR4X,GACAA,EAAI,QAASe,IAGrBpa,EAAOma,SAAU,EACVjY,KAAKmY,aAAa,IAE7BnY,KAAKiW,OAAO/V,KAAKpC,GACjBkC,KAAKmY,aACR,CAODA,YAAYC,GAAQ,GAChB,IAAKpY,KAAK6V,WAAoC,IAAvB7V,KAAKiW,OAAOtZ,OAC/B,OAEJ,MAAMmB,EAASkC,KAAKiW,OAAO,GACvBnY,EAAOma,UAAYG,IAGvBta,EAAOma,SAAU,EACjBna,EAAOka,WACPhY,KAAKqW,MAAQvY,EAAOuY,MACpBrW,KAAKa,KAAKR,MAAML,KAAMlC,EAAOgD,MAChC,CAODhD,OAAOA,GACHA,EAAOoW,IAAMlU,KAAKkU,IAClBlU,KAAK4V,GAAGyC,QAAQva,EACnB,CAMDsL,SAC4B,mBAAbpJ,KAAKsW,KACZtW,KAAKsW,MAAMjc,IACP2F,KAAKsY,mBAAmBje,EAAK,IAIjC2F,KAAKsY,mBAAmBtY,KAAKsW,KAEpC,CAODgC,mBAAmBje,GACf2F,KAAKlC,OAAO,CACR1D,KAAM8Y,GAAWuB,QACjBpa,KAAM2F,KAAKuY,KACL1e,OAAOqT,OAAO,CAAEsL,IAAKxY,KAAKuY,KAAME,OAAQzY,KAAK0Y,aAAere,GAC5DA,GAEb,CAODuP,QAAQjE,GACC3F,KAAK6V,WACN7V,KAAKgB,aAAa,gBAAiB2E,EAE1C,CAQD6D,QAAQ/G,EAAQC,GACZ1C,KAAK6V,WAAY,SACV7V,KAAK0P,GACZ1P,KAAKgB,aAAa,aAAcyB,EAAQC,EAC3C,CAODkU,SAAS9Y,GAEL,GADsBA,EAAOoW,MAAQlU,KAAKkU,IAG1C,OAAQpW,EAAO1D,MACX,KAAK8Y,GAAWuB,QACR3W,EAAOzD,MAAQyD,EAAOzD,KAAK4R,IAC3BjM,KAAK2Y,UAAU7a,EAAOzD,KAAK4R,IAAKnO,EAAOzD,KAAKme,KAG5CxY,KAAKgB,aAAa,gBAAiB,IAAIuB,MAAM,8LAEjD,MACJ,KAAK2Q,GAAWU,MAChB,KAAKV,GAAWQ,aACZ1T,KAAK4Y,QAAQ9a,GACb,MACJ,KAAKoV,GAAWW,IAChB,KAAKX,GAAWS,WACZ3T,KAAK6Y,MAAM/a,GACX,MACJ,KAAKoV,GAAWwB,WACZ1U,KAAK8Y,eACL,MACJ,KAAK5F,GAAWyB,cACZ3U,KAAK4U,UACL,MAAMjP,EAAM,IAAIpD,MAAMzE,EAAOzD,KAAK0e,SAElCpT,EAAItL,KAAOyD,EAAOzD,KAAKA,KACvB2F,KAAKgB,aAAa,gBAAiB2E,GAG9C,CAODiT,QAAQ9a,GACJ,MAAMgD,EAAOhD,EAAOzD,MAAQ,GACxB,MAAQyD,EAAO4R,IACf5O,EAAKZ,KAAKF,KAAKmX,IAAIrZ,EAAO4R,KAE1B1P,KAAK6V,UACL7V,KAAKgZ,UAAUlY,GAGfd,KAAK+V,cAAc7V,KAAKrG,OAAOyb,OAAOxU,GAE7C,CACDkY,UAAUlY,GACN,GAAId,KAAKiZ,eAAiBjZ,KAAKiZ,cAActc,OAAQ,CACjD,MAAMsE,EAAYjB,KAAKiZ,cAAcxZ,QACrC,IAAK,MAAMyZ,KAAYjY,EACnBiY,EAAS7Y,MAAML,KAAMc,EAE5B,CACD8B,MAAM/B,KAAKR,MAAML,KAAMc,GACnBd,KAAKuY,MAAQzX,EAAKnE,QAA2C,iBAA1BmE,EAAKA,EAAKnE,OAAS,KACtDqD,KAAK0Y,YAAc5X,EAAKA,EAAKnE,OAAS,GAE7C,CAMDwa,IAAIzH,GACA,MAAMtO,EAAOpB,KACb,IAAImZ,GAAO,EACX,OAAO,YAAarY,GAEZqY,IAEJA,GAAO,EACP/X,EAAKtD,OAAO,CACR1D,KAAM8Y,GAAWW,IACjBnE,GAAIA,EACJrV,KAAMyG,IAEtB,CACK,CAOD+X,MAAM/a,GACF,MAAMqZ,EAAMnX,KAAKoW,KAAKtY,EAAO4R,IACzB,mBAAsByH,IACtBA,EAAI9W,MAAML,KAAMlC,EAAOzD,aAChB2F,KAAKoW,KAAKtY,EAAO4R,IAI/B,CAMDiJ,UAAUjJ,EAAI8I,GACVxY,KAAK0P,GAAKA,EACV1P,KAAK8V,UAAY0C,GAAOxY,KAAKuY,OAASC,EACtCxY,KAAKuY,KAAOC,EACZxY,KAAK6V,WAAY,EACjB7V,KAAKoZ,eACLpZ,KAAKgB,aAAa,WAClBhB,KAAKmY,aAAY,EACpB,CAMDiB,eACIpZ,KAAK+V,cAAc9b,SAAS6G,GAASd,KAAKgZ,UAAUlY,KACpDd,KAAK+V,cAAgB,GACrB/V,KAAKgW,WAAW/b,SAAS6D,IACrBkC,KAAKwX,wBAAwB1Z,GAC7BkC,KAAKlC,OAAOA,EAAO,IAEvBkC,KAAKgW,WAAa,EACrB,CAMD8C,eACI9Y,KAAK4U,UACL5U,KAAKwJ,QAAQ,uBAChB,CAQDoL,UACQ5U,KAAK2W,OAEL3W,KAAK2W,KAAK1c,SAASof,GAAeA,MAClCrZ,KAAK2W,UAAOnQ,GAEhBxG,KAAK4V,GAAa,SAAE5V,KACvB,CAiBDyV,aAUI,OATIzV,KAAK6V,WACL7V,KAAKlC,OAAO,CAAE1D,KAAM8Y,GAAWwB,aAGnC1U,KAAK4U,UACD5U,KAAK6V,WAEL7V,KAAKwJ,QAAQ,wBAEVxJ,IACV,CAMDqD,QACI,OAAOrD,KAAKyV,YACf,CAUD5D,SAASA,GAEL,OADA7R,KAAKqW,MAAMxE,SAAWA,EACf7R,IACV,CAUGiX,eAEA,OADAjX,KAAKqW,MAAMY,UAAW,EACfjX,IACV,CAcDmH,QAAQA,GAEJ,OADAnH,KAAKqW,MAAMlP,QAAUA,EACdnH,IACV,CAYDsZ,MAAMJ,GAGF,OAFAlZ,KAAKiZ,cAAgBjZ,KAAKiZ,eAAiB,GAC3CjZ,KAAKiZ,cAAc/Y,KAAKgZ,GACjBlZ,IACV,CAYDuZ,WAAWL,GAGP,OAFAlZ,KAAKiZ,cAAgBjZ,KAAKiZ,eAAiB,GAC3CjZ,KAAKiZ,cAAc5D,QAAQ6D,GACpBlZ,IACV,CAmBDwZ,OAAON,GACH,IAAKlZ,KAAKiZ,cACN,OAAOjZ,KAEX,GAAIkZ,EAAU,CACV,MAAMjY,EAAYjB,KAAKiZ,cACvB,IAAK,IAAI/c,EAAI,EAAGA,EAAI+E,EAAUtE,OAAQT,IAClC,GAAIgd,IAAajY,EAAU/E,GAEvB,OADA+E,EAAUL,OAAO1E,EAAG,GACb8D,IAGlB,MAEGA,KAAKiZ,cAAgB,GAEzB,OAAOjZ,IACV,CAKDyZ,eACI,OAAOzZ,KAAKiZ,eAAiB,EAChC,CAcDS,cAAcR,GAGV,OAFAlZ,KAAK2Z,sBAAwB3Z,KAAK2Z,uBAAyB,GAC3D3Z,KAAK2Z,sBAAsBzZ,KAAKgZ,GACzBlZ,IACV,CAcD4Z,mBAAmBV,GAGf,OAFAlZ,KAAK2Z,sBAAwB3Z,KAAK2Z,uBAAyB,GAC3D3Z,KAAK2Z,sBAAsBtE,QAAQ6D,GAC5BlZ,IACV,CAmBD6Z,eAAeX,GACX,IAAKlZ,KAAK2Z,sBACN,OAAO3Z,KAEX,GAAIkZ,EAAU,CACV,MAAMjY,EAAYjB,KAAK2Z,sBACvB,IAAK,IAAIzd,EAAI,EAAGA,EAAI+E,EAAUtE,OAAQT,IAClC,GAAIgd,IAAajY,EAAU/E,GAEvB,OADA+E,EAAUL,OAAO1E,EAAG,GACb8D,IAGlB,MAEGA,KAAK2Z,sBAAwB,GAEjC,OAAO3Z,IACV,CAKD8Z,uBACI,OAAO9Z,KAAK2Z,uBAAyB,EACxC,CAQDnC,wBAAwB1Z,GACpB,GAAIkC,KAAK2Z,uBAAyB3Z,KAAK2Z,sBAAsBhd,OAAQ,CACjE,MAAMsE,EAAYjB,KAAK2Z,sBAAsBla,QAC7C,IAAK,MAAMyZ,KAAYjY,EACnBiY,EAAS7Y,MAAML,KAAMlC,EAAOzD,KAEnC,CACJ,ECzzBE,SAAS0f,GAAQ9X,GACpBA,EAAOA,GAAQ,GACfjC,KAAKga,GAAK/X,EAAKgY,KAAO,IACtBja,KAAKka,IAAMjY,EAAKiY,KAAO,IACvBla,KAAKma,OAASlY,EAAKkY,QAAU,EAC7Bna,KAAKoa,OAASnY,EAAKmY,OAAS,GAAKnY,EAAKmY,QAAU,EAAInY,EAAKmY,OAAS,EAClEpa,KAAKqa,SAAW,CACpB,CAOAN,GAAQvf,UAAU8f,SAAW,WACzB,IAAIN,EAAKha,KAAKga,GAAK5U,KAAKgG,IAAIpL,KAAKma,OAAQna,KAAKqa,YAC9C,GAAIra,KAAKoa,OAAQ,CACb,IAAIG,EAAOnV,KAAKoV,SACZC,EAAYrV,KAAKC,MAAMkV,EAAOva,KAAKoa,OAASJ,GAChDA,EAAoC,IAAN,EAAxB5U,KAAKC,MAAa,GAAPkV,IAAuBP,EAAKS,EAAYT,EAAKS,CACjE,CACD,OAAgC,EAAzBrV,KAAK6U,IAAID,EAAIha,KAAKka,IAC7B,EAMAH,GAAQvf,UAAUkgB,MAAQ,WACtB1a,KAAKqa,SAAW,CACpB,EAMAN,GAAQvf,UAAUmgB,OAAS,SAAUV,GACjCja,KAAKga,GAAKC,CACd,EAMAF,GAAQvf,UAAUogB,OAAS,SAAUV,GACjCla,KAAKka,IAAMA,CACf,EAMAH,GAAQvf,UAAUqgB,UAAY,SAAUT,GACpCpa,KAAKoa,OAASA,CAClB,EC3DO,MAAMU,WAAgBpb,EACzB8C,YAAY8D,EAAKrE,GACb,IAAIwE,EACJ7D,QACA5C,KAAK+a,KAAO,GACZ/a,KAAK2W,KAAO,GACRrQ,GAAO,iBAAoBA,IAC3BrE,EAAOqE,EACPA,OAAME,IAEVvE,EAAOA,GAAQ,IACVoC,KAAOpC,EAAKoC,MAAQ,aACzBrE,KAAKiC,KAAOA,EACZD,EAAsBhC,KAAMiC,GAC5BjC,KAAKgb,cAAmC,IAAtB/Y,EAAK+Y,cACvBhb,KAAKib,qBAAqBhZ,EAAKgZ,sBAAwBC,KACvDlb,KAAKmb,kBAAkBlZ,EAAKkZ,mBAAqB,KACjDnb,KAAKob,qBAAqBnZ,EAAKmZ,sBAAwB,KACvDpb,KAAKqb,oBAAwD,QAAnC5U,EAAKxE,EAAKoZ,2BAAwC,IAAP5U,EAAgBA,EAAK,IAC1FzG,KAAKsb,QAAU,IAAIvB,GAAQ,CACvBE,IAAKja,KAAKmb,oBACVjB,IAAKla,KAAKob,uBACVhB,OAAQpa,KAAKqb,wBAEjBrb,KAAKmH,QAAQ,MAAQlF,EAAKkF,QAAU,IAAQlF,EAAKkF,SACjDnH,KAAK8W,YAAc,SACnB9W,KAAKsG,IAAMA,EACX,MAAMiV,EAAUtZ,EAAKuZ,QAAUA,GAC/Bxb,KAAKyb,QAAU,IAAIF,EAAQG,QAC3B1b,KAAK2b,QAAU,IAAIJ,EAAQnI,QAC3BpT,KAAKwW,cAAoC,IAArBvU,EAAK2Z,YACrB5b,KAAKwW,cACLxW,KAAKkD,MACZ,CACD8X,aAAaa,GACT,OAAKvb,UAAU3D,QAEfqD,KAAK8b,gBAAkBD,EAChB7b,MAFIA,KAAK8b,aAGnB,CACDb,qBAAqBY,GACjB,YAAUrV,IAANqV,EACO7b,KAAK+b,uBAChB/b,KAAK+b,sBAAwBF,EACtB7b,KACV,CACDmb,kBAAkBU,GACd,IAAIpV,EACJ,YAAUD,IAANqV,EACO7b,KAAKgc,oBAChBhc,KAAKgc,mBAAqBH,EACF,QAAvBpV,EAAKzG,KAAKsb,eAA4B,IAAP7U,GAAyBA,EAAGkU,OAAOkB,GAC5D7b,KACV,CACDqb,oBAAoBQ,GAChB,IAAIpV,EACJ,YAAUD,IAANqV,EACO7b,KAAKic,sBAChBjc,KAAKic,qBAAuBJ,EACJ,QAAvBpV,EAAKzG,KAAKsb,eAA4B,IAAP7U,GAAyBA,EAAGoU,UAAUgB,GAC/D7b,KACV,CACDob,qBAAqBS,GACjB,IAAIpV,EACJ,YAAUD,IAANqV,EACO7b,KAAKkc,uBAChBlc,KAAKkc,sBAAwBL,EACL,QAAvBpV,EAAKzG,KAAKsb,eAA4B,IAAP7U,GAAyBA,EAAGmU,OAAOiB,GAC5D7b,KACV,CACDmH,QAAQ0U,GACJ,OAAKvb,UAAU3D,QAEfqD,KAAKmc,SAAWN,EACT7b,MAFIA,KAAKmc,QAGnB,CAODC,wBAESpc,KAAKqc,eACNrc,KAAK8b,eACqB,IAA1B9b,KAAKsb,QAAQjB,UAEbra,KAAKsc,WAEZ,CAQDpZ,KAAKnD,GACD,IAAKC,KAAK8W,YAAYtS,QAAQ,QAC1B,OAAOxE,KACXA,KAAKuX,OAAS,IAAIgF,EAAOvc,KAAKsG,IAAKtG,KAAKiC,MACxC,MAAMe,EAAShD,KAAKuX,OACdnW,EAAOpB,KACbA,KAAK8W,YAAc,UACnB9W,KAAKwc,eAAgB,EAErB,MAAMC,EAAiB7c,GAAGoD,EAAQ,QAAQ,WACtC5B,EAAKgI,SACLrJ,GAAMA,GAClB,IACckD,EAAW0C,IACb3F,KAAK4H,UACL5H,KAAK8W,YAAc,SACnB9W,KAAKgB,aAAa,QAAS2E,GACvB5F,EACAA,EAAG4F,GAIH3F,KAAKoc,sBACR,EAGCM,EAAW9c,GAAGoD,EAAQ,QAASC,GACrC,IAAI,IAAUjD,KAAKmc,SAAU,CACzB,MAAMhV,EAAUnH,KAAKmc,SAEfzE,EAAQ1X,KAAKmC,cAAa,KAC5Bsa,IACAxZ,EAAQ,IAAIV,MAAM,YAClBS,EAAOK,OAAO,GACf8D,GACCnH,KAAKiC,KAAKoH,WACVqO,EAAMnO,QAEVvJ,KAAK2W,KAAKzW,MAAK,KACXF,KAAKqC,eAAeqV,EAAM,GAEjC,CAGD,OAFA1X,KAAK2W,KAAKzW,KAAKuc,GACfzc,KAAK2W,KAAKzW,KAAKwc,GACR1c,IACV,CAODuV,QAAQxV,GACJ,OAAOC,KAAKkD,KAAKnD,EACpB,CAMDqJ,SAEIpJ,KAAK4H,UAEL5H,KAAK8W,YAAc,OACnB9W,KAAKgB,aAAa,QAElB,MAAMgC,EAAShD,KAAKuX,OACpBvX,KAAK2W,KAAKzW,KAAKN,GAAGoD,EAAQ,OAAQhD,KAAK2c,OAAOva,KAAKpC,OAAQJ,GAAGoD,EAAQ,OAAQhD,KAAK4c,OAAOxa,KAAKpC,OAAQJ,GAAGoD,EAAQ,QAAShD,KAAK4J,QAAQxH,KAAKpC,OAAQJ,GAAGoD,EAAQ,QAAShD,KAAKwJ,QAAQpH,KAAKpC,OAAQJ,GAAGI,KAAK2b,QAAS,UAAW3b,KAAK6c,UAAUza,KAAKpC,OACtP,CAMD2c,SACI3c,KAAKgB,aAAa,OACrB,CAMD4b,OAAOviB,GACH,IACI2F,KAAK2b,QAAQrI,IAAIjZ,EACpB,CACD,MAAO0L,GACH/F,KAAKwJ,QAAQ,cAAezD,EAC/B,CACJ,CAMD8W,UAAU/e,GAENoK,GAAS,KACLlI,KAAKgB,aAAa,SAAUlD,EAAO,GACpCkC,KAAKmC,aACX,CAMDyH,QAAQjE,GACJ3F,KAAKgB,aAAa,QAAS2E,EAC9B,CAOD3C,OAAOkR,EAAKjS,GACR,IAAIe,EAAShD,KAAK+a,KAAK7G,GAQvB,OAPKlR,EAIIhD,KAAKwW,eAAiBxT,EAAO6T,QAClC7T,EAAOuS,WAJPvS,EAAS,IAAIyL,GAAOzO,KAAMkU,EAAKjS,GAC/BjC,KAAK+a,KAAK7G,GAAOlR,GAKdA,CACV,CAOD8Z,SAAS9Z,GACL,MAAM+X,EAAOlhB,OAAOG,KAAKgG,KAAK+a,MAC9B,IAAK,MAAM7G,KAAO6G,EAAM,CAEpB,GADe/a,KAAK+a,KAAK7G,GACd2C,OACP,MAEP,CACD7W,KAAK+c,QACR,CAOD1E,QAAQva,GACJ,MAAM6O,EAAiB3M,KAAKyb,QAAQrd,OAAON,GAC3C,IAAK,IAAI5B,EAAI,EAAGA,EAAIyQ,EAAehQ,OAAQT,IACvC8D,KAAKuX,OAAO7T,MAAMiJ,EAAezQ,GAAI4B,EAAO8T,QAEnD,CAMDhK,UACI5H,KAAK2W,KAAK1c,SAASof,GAAeA,MAClCrZ,KAAK2W,KAAKha,OAAS,EACnBqD,KAAK2b,QAAQ/G,SAChB,CAMDmI,SACI/c,KAAKwc,eAAgB,EACrBxc,KAAKqc,eAAgB,EACrBrc,KAAKwJ,QAAQ,gBACTxJ,KAAKuX,QACLvX,KAAKuX,OAAOlU,OACnB,CAMDoS,aACI,OAAOzV,KAAK+c,QACf,CAMDvT,QAAQ/G,EAAQC,GACZ1C,KAAK4H,UACL5H,KAAKsb,QAAQZ,QACb1a,KAAK8W,YAAc,SACnB9W,KAAKgB,aAAa,QAASyB,EAAQC,GAC/B1C,KAAK8b,gBAAkB9b,KAAKwc,eAC5Bxc,KAAKsc,WAEZ,CAMDA,YACI,GAAItc,KAAKqc,eAAiBrc,KAAKwc,cAC3B,OAAOxc,KACX,MAAMoB,EAAOpB,KACb,GAAIA,KAAKsb,QAAQjB,UAAYra,KAAK+b,sBAC9B/b,KAAKsb,QAAQZ,QACb1a,KAAKgB,aAAa,oBAClBhB,KAAKqc,eAAgB,MAEpB,CACD,MAAMW,EAAQhd,KAAKsb,QAAQhB,WAC3Bta,KAAKqc,eAAgB,EACrB,MAAM3E,EAAQ1X,KAAKmC,cAAa,KACxBf,EAAKob,gBAETxc,KAAKgB,aAAa,oBAAqBI,EAAKka,QAAQjB,UAEhDjZ,EAAKob,eAETpb,EAAK8B,MAAMyC,IACHA,GACAvE,EAAKib,eAAgB,EACrBjb,EAAKkb,YACLtc,KAAKgB,aAAa,kBAAmB2E,IAGrCvE,EAAK6b,aACR,IACH,GACHD,GACChd,KAAKiC,KAAKoH,WACVqO,EAAMnO,QAEVvJ,KAAK2W,KAAKzW,MAAK,KACXF,KAAKqC,eAAeqV,EAAM,GAEjC,CACJ,CAMDuF,cACI,MAAMC,EAAUld,KAAKsb,QAAQjB,SAC7Bra,KAAKqc,eAAgB,EACrBrc,KAAKsb,QAAQZ,QACb1a,KAAKgB,aAAa,YAAakc,EAClC,EC9VL,MAAMC,GAAQ,CAAA,EACd,SAASlhB,GAAOqK,EAAKrE,GACE,iBAARqE,IACPrE,EAAOqE,EACPA,OAAME,GAGV,MAAM4W,ECHH,SAAa9W,EAAKjC,EAAO,GAAIgZ,GAChC,IAAIviB,EAAMwL,EAEV+W,EAAMA,GAA4B,oBAAblR,UAA4BA,SAC7C,MAAQ7F,IACRA,EAAM+W,EAAIhR,SAAW,KAAOgR,EAAItP,MAEjB,iBAARzH,IACH,MAAQA,EAAI9J,OAAO,KAEf8J,EADA,MAAQA,EAAI9J,OAAO,GACb6gB,EAAIhR,SAAW/F,EAGf+W,EAAItP,KAAOzH,GAGpB,sBAAsBgX,KAAKhX,KAExBA,OADA,IAAuB+W,EACjBA,EAAIhR,SAAW,KAAO/F,EAGtB,WAAaA,GAI3BxL,EAAM0S,EAAMlH,IAGXxL,EAAI2J,OACD,cAAc6Y,KAAKxiB,EAAIuR,UACvBvR,EAAI2J,KAAO,KAEN,eAAe6Y,KAAKxiB,EAAIuR,YAC7BvR,EAAI2J,KAAO,QAGnB3J,EAAIuJ,KAAOvJ,EAAIuJ,MAAQ,IACvB,MACM0J,GADkC,IAA3BjT,EAAIiT,KAAKvJ,QAAQ,KACV,IAAM1J,EAAIiT,KAAO,IAAMjT,EAAIiT,KAS/C,OAPAjT,EAAI4U,GAAK5U,EAAIuR,SAAW,MAAQ0B,EAAO,IAAMjT,EAAI2J,KAAOJ,EAExDvJ,EAAIyiB,KACAziB,EAAIuR,SACA,MACA0B,GACCsP,GAAOA,EAAI5Y,OAAS3J,EAAI2J,KAAO,GAAK,IAAM3J,EAAI2J,MAChD3J,CACX,CD7CmB0iB,CAAIlX,GADnBrE,EAAOA,GAAQ,IACcoC,MAAQ,cAC/ByJ,EAASsP,EAAOtP,OAChB4B,EAAK0N,EAAO1N,GACZrL,EAAO+Y,EAAO/Y,KACdoZ,EAAgBN,GAAMzN,IAAOrL,KAAQ8Y,GAAMzN,GAAU,KAK3D,IAAIkG,EAaJ,OAjBsB3T,EAAKyb,UACvBzb,EAAK,0BACL,IAAUA,EAAK0b,WACfF,EAGA7H,EAAK,IAAIkF,GAAQhN,EAAQ7L,IAGpBkb,GAAMzN,KACPyN,GAAMzN,GAAM,IAAIoL,GAAQhN,EAAQ7L,IAEpC2T,EAAKuH,GAAMzN,IAEX0N,EAAOra,QAAUd,EAAKc,QACtBd,EAAKc,MAAQqa,EAAO/O,UAEjBuH,EAAG5S,OAAOoa,EAAO/Y,KAAMpC,EAClC,CAGApI,OAAOqT,OAAOjR,GAAQ,CAClB6e,WACArM,UACAmH,GAAI3Z,GACJsZ,QAAStZ"}