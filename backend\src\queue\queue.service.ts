import { InjectQueue } from '@nestjs/bullmq';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Queue } from 'bullmq';
import { MatchJobName } from './processors/match.processor'
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EVENT_IMAGE_UPLOADED, EVENT_PERSONALITY_TEST_COMPLETED } from 'src/constants/event-emitter';
import { ImageJobName } from './processors/image.processor';
import { QUEUE_MATCH_NAME } from 'src/constants/queue';

@Injectable()
export class QueueService implements OnModuleInit {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    @InjectQueue(QUEUE_MATCH_NAME) private readonly matchQueue: Queue,
    @InjectQueue(QUEUE_MATCH_NAME) private readonly imageQueue: Queue,
    private readonly eventEmitter: EventEmitter2
  ) {}

  onModuleInit() {
    // on user complete personality test
    this.eventEmitter.on(EVENT_PERSONALITY_TEST_COMPLETED, (data) => {
      // this.logger.log(`User completed personality test: ${JSON.stringify(data)}`);
      this.createMatchQueue(MatchJobName.MATCHMAKING, data);
    });

    // on user upload image
    this.eventEmitter.on(EVENT_IMAGE_UPLOADED, (data) => {
      // this.logger.log(`User uploaded image: ${JSON.stringify(data)}`);
      this.createImageQueue(ImageJobName.RESIZE, data);
    });
  }

  async createMatchQueue(jobName: MatchJobName.MATCHMAKING, data: any) {
    return await this.matchQueue.add(jobName, data);
  }

  async createImageQueue(jobName: ImageJobName.RESIZE, data: any) {
    return await this.imageQueue.add(jobName, data);
  }
}
