import { BadRequestException, forwardRef, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { AddonResponseDto } from '../dto/addon-response.dto';
import { ItemPurchaseRequestDto } from '../dto/addon-purchase-request.dto';
import { Purchase, PurchaseDocument, PurchaseItem, PurchaseStatus } from '../schemas/purchase.schema';
import { InvoiceService } from './invoice.service';
import { InvoiceItem } from '../schemas/invoice.schema';
import { SubscriptionService } from './subscription.service';
import { Resource, SubscriptionStatus } from '../schemas/subscription.schema';

export interface AddonPrice {
  quantity: string;
  price: number;
}

export interface Addon {
  code: string;
  name: string;
  description: string;
  price: number;
  minQuantity: number;
  maxQuantity: number;
  prices: AddonPrice[]
}

export const addons = [
  {
    id: 'message',
    code: 'message',
    name: 'Message Quota',
    description: 'Message Quota',
    price: 100,
    minQuantity: 100,
    maxQuantity: 10000,
    prices: [
      {
        quantity: 500,
        price: 85
      },
      {
        quantity: 1000,
        price: 75
      },
      {
        quantity: 10000,
        price: 50
      }
    ]
  }
]

@Injectable()
export class AddonService {
  private readonly logger = new Logger(AddonService.name);

  constructor(
    @InjectModel(Purchase.name) private purchaseModel: Model<PurchaseDocument>,
    @Inject(forwardRef(() => InvoiceService)) private readonly invoiceService: InvoiceService,
    @Inject(forwardRef(() => SubscriptionService)) private readonly subscriptionService: SubscriptionService
  ) {}

  /**
   * Get addons
   * @returns Addons
   */
  getAddons(): AddonResponseDto[] {
    return addons;
  }

  /**
   * Purchase addons
   * @param userId User ID
   * @param purchaseData Purchase data
   * @returns Invoice
   */
  async purchaseAddons(userId: string, purchaseData: ItemPurchaseRequestDto[]) {
    try {
      // check if user has an active subscription
      const subscription = await this.subscriptionService.getUserSubscription(userId);
      
      if (!subscription || subscription.status !== SubscriptionStatus.ACTIVE) {
        throw new BadRequestException('User does not have an active subscription');
      }

      if (!purchaseData || !Array.isArray(purchaseData)) {
        throw new BadRequestException('Invalid purchase data');
      }
      
      const items: PurchaseItem[] = [];
      let total = 0;
      
      purchaseData.forEach(data => {
        const item = addons.find(a => a.id === data.id);

        if (item) {
          if (data.quantity < item.minQuantity || data.quantity > item.maxQuantity) {
            throw new BadRequestException(`Invalid quantity for ${item.name}`);
          }

          let itemPrice = item.price;
          
          if (item.prices.length > 0) {
            // Sort prices by quantity in descending order and find the first tier where user quantity >= tier quantity
            const applicablePrices = item.prices
              .filter(p => data.quantity >= parseInt(p.quantity.toString()))
              .sort((a, b) => parseInt(b.quantity.toString()) - parseInt(a.quantity.toString()));
            
            // Use the highest applicable tier price, or default price if no tier applies
            itemPrice = applicablePrices.length > 0 ? applicablePrices[0].price : item.price;
          }
          
          items.push({
            id: item.id,
            name: item.name,
            description: item.description,
            price: itemPrice,
            quantity: data.quantity
          });

          total += itemPrice * data.quantity;
        }
      });

      const purchase = await this.purchaseModel.create({
        userId,
        items,
        total,
        description: 'Addons purchase',
        status: PurchaseStatus.UNPAID
      });

      // check if invoice already exists
      const pendingInvoice = await this.invoiceService.getPendingInvoiceForPurchase(userId, purchase.id);
      
      if (pendingInvoice) {
        return await this.invoiceService.generateInvoicePayment(pendingInvoice.id);
      }

      const invoiceItems: InvoiceItem[] = [];

      items.forEach(item => {
        invoiceItems.push({
          id: item.id,
          description: item.name,
          quantity: item.quantity,
          price: item.price
        });
      });

      const invoice = await this.invoiceService.createPurchaseInvoice(userId, purchase.id, invoiceItems);
      purchase.invoiceId = invoice.id;
      await purchase.save();
      return await this.invoiceService.generateInvoicePayment(invoice.id);
    } catch (error) {
      this.logger.error(`Error purchasing addons: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Activate addons
   * @param purchaseId Purchase ID
   * @param invoiceId Invoice ID
   */
  async activateAddons(purchaseId: string, invoiceId: string) {
    try {
      const purchase = await this.purchaseModel.findOne({
        _id: new Types.ObjectId(purchaseId),
        status: PurchaseStatus.UNPAID,
        invoiceId
      }).exec();

      if (!purchase) {
        throw new NotFoundException(`Purchase ${purchaseId} not found`);
      }

      // Update purchase status
      await this.purchaseModel.updateOne({
        _id: new Types.ObjectId(purchaseId)
      }, { status: PurchaseStatus.PAID }).exec();

      const subscription = await this.subscriptionService.getUserSubscription(purchase.userId);

      if (!subscription) {
        throw new NotFoundException(`Subscription not found for user ${purchase.userId}`);
      }

      const resource: Resource = {
        psychologistConsultations: 0,
        chats: 0
      };

      purchase.items.forEach(item => {
        if (item.id === 'message') {
          resource.chats = item.quantity;
        } else if (item.id === 'assistant') {
          resource.psychologistConsultations = item.quantity;
        }
      });

      // Add addons to subscription
      await this.subscriptionService.addAddonsByUserId(subscription.userId, resource);

      this.logger.log(`Addons activated for user ${purchase.userId}`);
    } catch (error) {
      this.logger.error(`Error activating addons: ${error.message}`, error.stack);
      throw error;
    }
  }
}
