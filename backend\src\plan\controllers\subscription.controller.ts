import { BadRequestException, Body, Controller, Get, Post, Request } from '@nestjs/common';
import { SubscriptionService, SubscriptionUsageType } from '../services/subscription.service';
import { EnrollSubscriptionDto, SubscriptionResourcesDto, UserSubscriptionResponseDto } from '../dto/subscription.dto';
import { RequestWithUser } from 'src/common/interfaces/request-with-user.interface';
import { InvoiceResponseDto } from '../dto/invoice.dto';

@Controller('plans/subscription')
export class SubscriptionController {
  constructor(
    private readonly subscriptionService: SubscriptionService
  ) {}

  /**
   * Get user subscription
   * @param req 
   * @returns 
   */
  @Get()
  async getUserSubscription(@Request() req: RequestWithUser): Promise<UserSubscriptionResponseDto> {
    return this.getUserSubscriptionResponse(req.user.userId);
  }

  @Get('resources')
  async getResources(@Request() req: RequestWithUser): Promise<SubscriptionResourcesDto> {
    return {
      psychologistConsultations: await this.subscriptionService.getAvailableResources(req.user.userId, SubscriptionUsageType.CONSULTATIONS),
      chats: await this.subscriptionService.getAvailableResources(req.user.userId, SubscriptionUsageType.CHATS)
    }
  }

  /**
   * Enroll user in a plan
   * @param body 
   * @param req 
   * @returns 
   */
  @Post()
  async enrollSubscription(@Body() body: EnrollSubscriptionDto, @Request() req: RequestWithUser): Promise<InvoiceResponseDto> {
    try {
      const invoice = await this.subscriptionService.enrollSubscription(req.user.userId, body.planId, body.isTrial);

      return {
        id: invoice.id,
        userId: invoice.userId,
        planId: invoice.planId,
        items: invoice.items,
        totals: invoice.totals,
        paidDate: invoice.paidDate,
        metadata: invoice.metadata,
        status: invoice.status,
        invoiceCode: invoice.invoiceCode,
        invoiceLink: invoice.invoiceLink,
        paymentLink: invoice.paymentLink,
        paymentToken: invoice.paymentToken,
        createdAt: invoice.createdAt,
        updatedAt: invoice.updatedAt
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Get user subscription response
   * @param userId 
   * @returns 
   */
  private async getUserSubscriptionResponse(userId: string): Promise<UserSubscriptionResponseDto> {
    const subscription = await this.subscriptionService.getUserSubscription(userId);
    
    if (!subscription) {
      throw new BadRequestException('User has no subscription');
    }
    
    return {
      id: subscription.id,
      userId: subscription.userId,
      plan: {
        id: subscription.planId,
        name: subscription.planRef.name,
        description: subscription.planRef.description,
        psychologistConsultations: subscription.resources.psychologistConsultations,
        chats: subscription.resources.chats
      },
      usage: subscription.usage,
      resources: subscription.resources,
      addOnUsage: subscription.addOnUsage,
      addOnResources: {
        psychologistConsultations: subscription.addOnResources.psychologistConsultations + subscription.addOnUsage.psychologistConsultationsCount,
        chats: subscription.addOnResources.chats + subscription.addOnUsage.chatsCount
      },
      startDate: subscription.startDate.toISOString(),
      endDate: subscription.endDate.toISOString(),
      status: subscription.status,
      autoRenew: subscription.autoRenew
    };
  }
}
