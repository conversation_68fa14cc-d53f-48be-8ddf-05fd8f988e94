import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PlanController } from './controllers/plan.controller';
import { PlanService } from './services/plan.service';
import { Plan, PlanSchema } from './schemas/plan.schema';
import { Subscription, SubscriptionSchema } from './schemas/subscription.schema';
import { SubscriptionService } from './services/subscription.service';
import { Invoice, InvoiceSchema } from './schemas/invoice.schema';
import { InvoiceService } from './services/invoice.service';
import { InvoiceController } from './controllers/invoice.controller';
import { SubscriptionController } from './controllers/subscription.controller';
import { UserModule } from 'src/user/user.module';
import { MidtransService } from './services/midtrans.service';
import { ScheduleModule } from '@nestjs/schedule';
import { AddonService } from './services/addon.service';
import { Purchase, PurchaseSchema } from './schemas/purchase.schema';
import { AddonController } from './controllers/addon.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Plan.name, schema: PlanSchema },
      { name: Invoice.name, schema: InvoiceSchema },
      { name: Subscription.name, schema: SubscriptionSchema },
      { name: Purchase.name, schema: PurchaseSchema }
    ]),
    ScheduleModule.forRoot(),
    UserModule
  ],
  controllers: [PlanController, InvoiceController, SubscriptionController, AddonController],
  providers: [PlanService, SubscriptionService, InvoiceService, MidtransService, AddonService],
  exports: [PlanService, SubscriptionService, InvoiceService, MidtransService, AddonService]
})
export class PlanModule {}
