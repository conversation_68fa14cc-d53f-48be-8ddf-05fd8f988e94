import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'src/user/schemas/user.schema';

export type UserOnlineDocument = UserOnline & Document;

@Schema({ collection: 'user-onlines', timestamps: true })
export class UserOnline {
  @Prop({ type: Types.ObjectId, ref: User.name, required: true })
  userId: Types.ObjectId;

  @Prop({ type: Boolean, default: false })
  isOnline: boolean;

  @Prop({ type: Date })
  lastOnlineAt: Date;
}

export const UserOnlineSchema = SchemaFactory.createForClass(UserOnline);

// Create compound index to ensure one userId can only have one isOnline status
UserOnlineSchema.index({ userId: 1, isOnline: 1 }, { unique: true });
