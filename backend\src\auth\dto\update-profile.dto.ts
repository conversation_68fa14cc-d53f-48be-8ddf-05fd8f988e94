import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsBoolean } from 'class-validator';

export class UpdateProfileDto {
  @IsString()
  @MinLength(3)
  name: string;

  @IsOptional()
  @IsString()
  dateOfBirth?: string;

  @IsOptional()
  @IsString()
  gender?: string;

  @IsOptional()
  @IsString()
  religion?: string;

  @IsOptional()
  @IsString()
  occupation?: string;

  @IsOptional()
  @IsBoolean()
  isSmoker?: boolean;

  @IsOptional()
  @IsBoolean()
  acceptDifferentReligion?: boolean;

  @IsOptional()
  @IsString()
  about?: string;
}