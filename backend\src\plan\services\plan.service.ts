import { Injectable, Logger, NotFoundException, BadRequestException, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Plan, PlanDocument, PlanStatus } from '../schemas/plan.schema';
import { CreatePlanDto } from '../dto/create-plan.dto';
import { UpdatePlanDto } from '../dto/update-plan.dto'
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Subscription, SubscriptionDocument, SubscriptionStatus } from '../schemas/subscription.schema';

@Injectable()
export class PlanService implements OnModuleInit {
  private readonly logger = new Logger(PlanService.name);

  constructor(
    @InjectModel(Plan.name) private planModel: Model<PlanDocument>,
    @InjectModel(Subscription.name) private subscriptionModel: Model<SubscriptionDocument>,
    private readonly eventEmitter: EventEmitter2
  ) {}

  onModuleInit() {}
  
  async createPlan(createPlanDto: CreatePlanDto): Promise<PlanDocument> {
    try {
      // Check if plan with the same code already exists
      const existingPlan = await this.planModel.findOne({ code: createPlanDto.code }).exec();
      if (existingPlan) {
        throw new BadRequestException(`Plan with code ${createPlanDto.code} already exists`);
      }

      // Create new plan
      const newPlan = new this.planModel(createPlanDto);
      const savedPlan = await newPlan.save();

      // If this is the first plan or marked as default, make it the default
      const plansCount = await this.planModel.countDocuments().exec();
      if (plansCount === 1 || createPlanDto.isDefault) {
        await this.setDefaultPlan(savedPlan.id);
      }

      this.eventEmitter.emit('plan.created', savedPlan);
      return savedPlan;
    } catch (error) {
      this.logger.error(`Error creating plan: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findAllPlans(includeHidden = false): Promise<PlanDocument[]> {
    const query = includeHidden ? {} : { isHidden: false };
    return this.planModel.find(query).sort({ sortOrder: 1 }).exec();
  }

  async findActivePlans(): Promise<PlanDocument[]> {
    return this.planModel.find({ 
      status: PlanStatus.ACTIVE,
      isHidden: false 
    }).sort({ sortOrder: 1 }).exec();
  }

  async findOnePlan(id: string): Promise<PlanDocument> {
    const plan = await this.planModel.findById(id).exec();
    if (!plan) {
      throw new NotFoundException(`Plan with ID ${id} not found`);
    }
    return plan;
  }

  async findPlanByCode(code: string): Promise<PlanDocument> {
    const plan = await this.planModel.findOne({ code }).exec();
    if (!plan) {
      throw new NotFoundException(`Plan with code ${code} not found`);
    }
    return plan;
  }

  async getDefaultPlan(): Promise<PlanDocument> {
    const plan = await this.planModel.findOne({ isDefault: true }).exec();
    if (!plan) {
      throw new NotFoundException('No default plan found');
    }
    return plan;
  }

  async updatePlan(id: string, updatePlanDto: UpdatePlanDto): Promise<PlanDocument> {
    try {
      const plan = await this.findOnePlan(id);
      
      // If updating the code, check if it's unique
      if (updatePlanDto.code && updatePlanDto.code !== plan.code) {
        const existingPlan = await this.planModel.findOne({ code: updatePlanDto.code }).exec();
        if (existingPlan) {
          throw new BadRequestException(`Plan with code ${updatePlanDto.code} already exists`);
        }
      }
      
      // Update plan
      Object.assign(plan, updatePlanDto);
      const updatedPlan = await plan.save();
      
      // If setting as default, update other plans
      if (updatePlanDto.isDefault === true) {
        await this.setDefaultPlan(id);
      }
      
      this.eventEmitter.emit('plan.updated', updatedPlan);
      return updatedPlan;
    } catch (error) {
      this.logger.error(`Error updating plan: ${error.message}`, error.stack);
      throw error;
    }
  }

  async removePlan(id: string): Promise<PlanDocument> {
    try {
      const plan = await this.findOnePlan(id);
      
      // Check if there are active user plans using this plan
      const activeSubscriptions = await this.subscriptionModel.countDocuments({ 
        planId: id, 
        status: { $in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIAL] } 
      }).exec();
      
      if (activeSubscriptions > 0) {
        throw new BadRequestException(`Cannot delete plan with active subscriptions`);
      }
      
      // If it's the default plan, throw error
      if (plan.isDefault) {
        throw new BadRequestException(`Cannot delete the default plan`);
      }
      
      // Set status to deprecated instead of deleting
      plan.status = PlanStatus.DEPRECATED;
      plan.isHidden = true;
      const updatedPlan = await plan.save();
      
      this.eventEmitter.emit('plan.removed', updatedPlan);
      return updatedPlan;
    } catch (error) {
      this.logger.error(`Error removing plan: ${error.message}`, error.stack);
      throw error;
    }
  }

  async setDefaultPlan(id: string): Promise<PlanDocument> {
    try {
      // Remove default flag from all plans
      await this.planModel.updateMany({}, { isDefault: false }).exec();
      
      // Set the specified plan as default
      const plan = await this.findOnePlan(id);
      plan.isDefault = true;
      return await plan.save();
    } catch (error) {
      this.logger.error(`Error setting default plan: ${error.message}`, error.stack);
      throw error;
    }
  }
}
