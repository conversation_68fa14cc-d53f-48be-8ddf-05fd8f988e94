import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsEnum, IsOptional, IsBoolean, Min, IsObject } from 'class-validator';
import { PlanInterval, PlanStatus } from '../schemas/plan.schema';

export class CreatePlanDto {
  @IsString()
  code: string;

  @IsString()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsNumber()
  @Min(0)
  price: number;

  @IsEnum(PlanInterval)
  interval: PlanInterval;

  @IsNumber()
  @Min(0)
  psychologistConsultations: number;

  @IsNumber()
  @Min(0)
  chats: number;

  @IsObject()
  @IsOptional()
  features?: Record<string, any>;

  @IsEnum(PlanStatus)
  @IsOptional()
  status?: PlanStatus;

  @IsNumber()
  @IsOptional()
  sortOrder?: number;

  @IsBoolean()
  @IsOptional()
  isPopular?: boolean;

  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;

  @IsBoolean()
  @IsOptional()
  isHidden?: boolean;
}
