import { BadRequestException, Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as midtransClient from 'midtrans-client';

export type ItemDetails = {
  id: string;
  price: number;
  quantity: number;
  name: string;
}

export enum TransactionStatus {
  CAPTURE = 'capture',
  CHALLENGE = 'challenge',
  ACCEPT = 'accept',
  SETTLEMENT = 'settlement',
  DENY = 'deny',
  CANCEL = 'cancel',
  EXPIRE = 'expire',
  PENDING = 'pending'
}

@Injectable()
export class MidtransService {
  private readonly logger = new Logger(MidtransService.name);
  private readonly snapClient: midtransClient.Snap;

  constructor(private readonly configService: ConfigService) {
    this.snapClient = new midtransClient.Snap({
      isProduction : this.configService.get('app.midtrans.environment') === 'production',
      serverKey : this.configService.get('app.midtrans.serverKey'),
      clientKey : this.configService.get('app.midtrans.clientKey')
    });
  }

  async createSnapTransaction(params: midtransClient.SnapCreateTransactionParameter) {
    const response = await this.snapClient.createTransaction(params);
    return response;
  }

  async handleNotification(notificationJson: any) {
    return await this.snapClient.transaction.notification(notificationJson)
  }

  async validateTransaction(transactionId: string) {
    try {
      return await this.snapClient.transaction.status(transactionId);
    } catch(error) {
      throw new BadRequestException(error.message);
    }
  }
}