# Personality Test API Documentation

This document provides detailed information about the Personality Test API endpoints, request/response formats, and examples.

## Table of Contents
- [Get All Questions](#get-all-questions)
- [Submit Personality Test](#submit-personality-test)
- [Data Types](#data-types)
  - [BFI Scores](#bfi-scores)
  - [BFI Answer](#bfi-answer)
  - [Self-Disclosure Answer](#self-disclosure-answer)
  - [Personality Test Response](#personality-test-response)

## Base URL
```
{{base_url}}/personality
```

## Authentication
All endpoints require JWT authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Get All Questions

Retrieves all personality test questions including BFI (Big Five Inventory) and Self-Disclosure questions.

### Endpoint
```
GET /questions
```

### Response
```json
{
  "bfi": {
    "dimensions": [
      {
        "id": "extraversion",
        "name": "Ekstraversi",
        "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> tingkat kenyamanan seseorang dalam berinteraksi dengan orang lain dan kecenderungan untuk mencari rangsangan dari lingkungan sosial.",
        "questions": [
          {
            "id": "BFI_E1",
            "text": "<PERSON>a ramah."
          },
          {
            "id": "BFI_E2",
            "text": "Saya bersikap tegas."
          },
          {
            "id": "BFI_E3",
            "text": "Saya penuh energi."
          },
          {
            "id": "BFI_E4",
            "text": "Saya bukan orang yang menyendiri."
          },
          {
            "id": "BFI_E5",
            "text": "Saya suka menjadi pusat perhatian."
          },
          {
            "id": "BFI_E6",
            "text": "Saya merasa nyaman saat berbicara di depan orang banyak."
          }
        ]
      },
      ...
    ]
  },
  "selfDisclosure": {
    "categories": [
      {
        "id": "keyakinan_nilai",
        "name": "Keyakinan & Nilai",
        "description": "Mengungkapkan pandangan pribadi tentang agama, politik, moral, dan ide-ide idealis.",
        "questions": [
          {
            "id": "SD_KN1",
            "text": "Saya sering menyampaikan pandangan religius/pribadi."
          },
          {
            "id": "SD_KN2",
            "text": "Saya membicarakan pendapat politik."
          },
          {
            "id": "SD_KN3",
            "text": "Saya menyampaikan nilai-nilai moral."
          },
          {
            "id": "SD_KN4",
            "text": "Saya berbagi gagasan idealis."
          }
        ]
      },
      ...
    ]
  }
}
```

## Submit Personality Test

Submits the user's answers to the personality test.

### Endpoint
```
POST /test
```

### Request Body

```typescript
{
  "bfiAnswers": {
    "BFI_E1": 4,
    "BFI_E2": 2,
    // ... all BFI questions must be answered with values 1-5
  },
  "selfDisclosureAnswers": {
    "SD_KN1": 3,
    "SD_KN2": 4,
    // ... all Self-Disclosure questions must be answered with values 1-5
  }
}
```

#### Request Body Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| bfiAnswers | Object | Yes | Key-value pairs of questionId and answer (1-5) |
| selfDisclosureAnswers | Object | Yes | Key-value pairs of questionId and answer (1-5) |

### Response

```json
{
  "id": "60d5ec9f2f8e4c3d4c8b4567",
  "userId": "60d5ec9f2f8e4c3d4c8b4567",
  "bfi": {
    "extraversion": 3.4,
    "agreeableness": 4.1,
    "conscientiousness": 3.8,
    "neuroticism": 2.5,
    "openness": 4.2
  },
  "selfDisclosure": 3.7,
  "bfiAnswers": [
    {
      "questionId": "bfi_1",
      "value": 4,
      "dimension": "extraversion"
    }
    // ... more BFI answers
  ],
  "selfDisclosureAnswers": [
    {
      "questionId": "sd_1",
      "value": 3,
      "category": "anxiety"
    }
    // ... more Self-Disclosure answers
  ],
  "createdAt": "2023-05-15T10:30:00.000Z",
  "updatedAt": "2023-05-15T10:30:00.000Z"
}
```

## Data Types

### BFI Scores
Represents the Big Five personality traits scores.

```typescript
{
  extraversion: number;  // 1-5
  agreeableness: number;  // 1-5
  conscientiousness: number;  // 1-5
  neuroticism: number;  // 1-5
  openness: number;  // 1-5
}
```

### BFI Answer
Represents a single BFI question answer.

```typescript
{
  id: string;  // Format: 'BFI_<Dimension><Number>' (e.g., 'BFI_E1' for Extraversion question 1)
  text: string;  // The question text
  value?: 1 | 2 | 3 | 4 | 5;  // Answer value (when submitting responses)
}
```

### Self-Disclosure Answer
Represents a single Self-Disclosure question answer.

```typescript
{
  id: string;  // Format: 'SD_<Category><Number>' (e.g., 'SD_KN1' for Keyakinan & Nilai question 1)
  text: string;  // The question text
  value?: 1 | 2 | 3 | 4 | 5;  // Answer value (when submitting responses)
}
```

### Personality Test Response
Response object when submitting a personality test.

```typescript
{
  id: string;  // Test ID
  userId: string;  // User ID who took the test
  bfi: BFI;  // BFI scores
  selfDisclosure: number;  // Average self-disclosure score (1-5)
  bfiAnswers: BfiAnswer[];  // All BFI answers
  selfDisclosureAnswers: SelfDisclosureAnswer[];  // All Self-Disclosure answers
  createdAt: string;  // ISO date string
  updatedAt: string;  // ISO date string
}
```

## Question Types and Structure

### BFI (Big Five Inventory)
The BFI section contains 5 personality dimensions, each with 6 questions:

| Dimension ID | Name | Description |
|--------------|------|-------------|
| extraversion | Ekstraversi | Menggambarkan tingkat kenyamanan seseorang dalam berinteraksi dengan orang lain dan kecenderungan untuk mencari rangsangan dari lingkungan sosial. |
| agreeableness | Kesetujuan | Menggambarkan kecenderungan untuk bersikap kooperatif dan peduli terhadap orang lain. |
| conscientiousness | Keteraturan | Menggambarkan tingkat disiplin diri, ketelitian, dan dorongan untuk mencapai tujuan. |
| negativeEmotionality | Stabilitas Emosi | Menggambarkan kecenderungan untuk mengalami emosi negatif seperti kecemasan, kemarahan, atau depresi. |
| openMindedness | Keterbukaan terhadap Pengalaman | Menggambarkan apresiasi terhadap seni, emosi, petualangan, dan ide-ide yang tidak biasa. |

### Self-Disclosure

| Category ID | Name | Description |
|-------------|------|-------------|
| keyakinan_nilai | Keyakinan & Nilai | Mengungkapkan pandangan pribadi tentang agama, politik, moral, dan ide-ide idealis. |
| hubungan_interpersonal | Hubungan Interpersonal | Berbagi informasi tentang hubungan dengan keluarga, teman, dan interaksi sosial lainnya. |
| urusan_pribadi | Urusan Pribadi | Mengungkapkan pengalaman pribadi yang sensitif, masalah kesehatan, atau kerentanan diri. |
| ketertarikan_minat | Ketertarikan & Minat | Berbagi tentang hobi, preferensi hiburan, dan rencana pribadi. |
| perasaan_intim | Perasaan Intim | Mengungkapkan perasaan terdalam, ketakutan, harapan, dan konflik emosional. |

## Error Responses

### 400 Bad Request
Returned when the request is invalid (missing required fields, invalid values, etc.)

```json
{
  "statusCode": 400,
  "message": "Some required BFI questions are not answered",
  "missingQuestions": ["bfi_1", "bfi_2"]
}
```

### 401 Unauthorized
Returned when the JWT token is missing or invalid

```json
{
  "statusCode": 401,
  "message": "Unauthorized"
}
```

### 500 Internal Server Error
Returned when an unexpected error occurs

```json
{
  "statusCode": 500,
  "message": "Internal server error"
}
```

## Notes

- All answer values must be integers between 1 and 5 (inclusive)
- All questions must be answered for both BFI and Self-Disclosure sections
- The BFI scores are calculated based on the user's answers and normalized to a 1-5 scale
- The Self-Disclosure score is the average of all Self-Disclosure answers