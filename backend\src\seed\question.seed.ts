import { Injectable, Logger } from '@nestjs/common';
import { QuestionService } from '../personality/services/question.service';
import { BFI_QUESTIONS, SELF_DISCLOSURE_QUESTIONS } from '../constants/personality-questions.constant';
import { QuestionType } from 'src/personality/schemas/question.schema';

@Injectable()
export class QuestionSeeder {
  private readonly logger = new Logger(QuestionSeeder.name);

  constructor(private readonly questionService: QuestionService) {}

  async seed() {
    try {
      // Seed BFI questions
      const bfiQuestions = BFI_QUESTIONS.map((q, index) => ({
        questionId: q.id,
        questionType: QuestionType.BFI,
        text: q.text,
        dimension: q.dimension,
        order: index + 1,
      }));
      
      await this.questionService.seedBfiQuestions(bfiQuestions);
      this.logger.log(`Seeded ${bfiQuestions.length} BFI questions`);

      // Seed Self-Disclosure questions
      const sdQuestions = SELF_DISCLOSURE_QUESTIONS.map((q, index) => ({
        questionId: q.id,
        questionType: QuestionType.SELF_DISCLOSURE,
        text: q.text,
        category: q.category,
        order: index + 1,
      }));
      
      await this.questionService.seedSelfDisclosureQuestions(sdQuestions);
      this.logger.log(`Seeded ${sdQuestions.length} Self-Disclosure questions`);
    } catch (error) {
      this.logger.error('Error seeding questions', error.stack);
      throw error;
    }
  }
}
