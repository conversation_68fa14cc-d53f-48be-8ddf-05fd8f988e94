"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/chat/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/chat/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/dashboard/chat/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatPage() {\n    _s();\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [selectedChat, setSelectedChat] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [messagesLoading, setMessagesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [currentUserId, setCurrentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            fetchChats();\n            getCurrentUserId();\n            return ({\n                \"ChatPage.useEffect\": ()=>{\n                    if (socket) {\n                        console.log('Cleaning up socket connection');\n                        socket.disconnect();\n                    }\n                }\n            })[\"ChatPage.useEffect\"];\n        }\n    }[\"ChatPage.useEffect\"], [\n        socket\n    ]); // Add socket to dependency array\n    // Auto-select first chat when chats are loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            if (chats.length > 0 && !selectedChat) {\n                console.log('Auto-selecting first chat:', chats[0]);\n                handleChatSelect(chats[0]);\n            }\n        }\n    }[\"ChatPage.useEffect\"], [\n        chats,\n        selectedChat\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatPage.useEffect\"], [\n        messages\n    ]);\n    const getCurrentUserId = async ()=>{\n        try {\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) return;\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/auth/profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const profile = await response.json();\n                setCurrentUserId(profile.id);\n            }\n        } catch (err) {\n            console.error('Failed to get current user ID:', err);\n        }\n    };\n    const fetchChats = async ()=>{\n        try {\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) {\n                router.replace('/login');\n                return;\n            }\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/chats\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                if (response.status === 401) {\n                    localStorage.removeItem('pairsona_token');\n                    router.replace('/login');\n                    return;\n                }\n                throw new Error('Failed to fetch chats');\n            }\n            const chatsData = await response.json();\n            console.log('Fetched chats:', chatsData);\n            setChats(chatsData);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load chats');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchMessages = async (chatId)=>{\n        try {\n            setMessagesLoading(true);\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) return;\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/chats/\").concat(chatId, \"/messages?page=1&limit=50\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch messages');\n            }\n            const messagesData = await response.json();\n            setMessages(messagesData.data.reverse()); // Reverse to show oldest first\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load messages');\n        } finally{\n            setMessagesLoading(false);\n        }\n    };\n    const connectToChat = function(chatOrMatchId) {\n        let isMatchId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        const token = localStorage.getItem('pairsona_token');\n        if (!token) return;\n        // Disconnect existing socket\n        if (socket) {\n            socket.disconnect();\n        }\n        console.log('🚀 Connecting to chat with ID:', chatOrMatchId, isMatchId ? '(matchId)' : '(chatId)');\n        console.log('🌐 API Base URL:', \"https://api.pairsona.id\");\n        // Use the exact format from backend documentation\n        const socketUrl = \"\".concat(\"https://api.pairsona.id\", \"/chat\");\n        console.log('🌐 Socket URL:', socketUrl);\n        // Based on the backend docs example, let's try the EXACT format\n        const config = {\n            transports: [\n                'websocket'\n            ],\n            auth: {\n                token: \"Bearer \".concat(token) // Keep Bearer as shown in docs\n            },\n            query: isMatchId ? {\n                matchId: chatOrMatchId\n            } : {\n                chatId: chatOrMatchId\n            }\n        };\n        console.log('📋 Socket.IO config:', JSON.stringify(config, null, 2));\n        const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_7__.io)(socketUrl, config);\n        newSocket.on('connect', ()=>{\n            console.log('✅ Connected to chat server');\n            console.log('Socket ID:', newSocket.id);\n        });\n        newSocket.on('message', (message)=>{\n            console.log('📨 New message received:', message);\n            setMessages((prev)=>[\n                    ...prev,\n                    message\n                ]);\n            // Update last message in chats list\n            setChats((prev)=>prev.map((chat)=>chat.id === message.chatId ? {\n                        ...chat,\n                        lastMessage: {\n                            id: message.id,\n                            senderId: message.senderId,\n                            message: message.message,\n                            sentAt: message.sentAt\n                        }\n                    } : chat));\n        });\n        newSocket.on('disconnect', (reason)=>{\n            console.log('❌ Disconnected from chat server, reason:', reason);\n            // If disconnected by server and we were using chatId, try with matchId\n            if (reason === 'io server disconnect' && !isMatchId && selectedChat) {\n                console.log('🔄 Server disconnected, trying with matchId...');\n                setTimeout(()=>{\n                    connectToChat(selectedChat.matchId, true);\n                }, 1000);\n            } else if (reason === 'io server disconnect') {\n                console.log('❌ Connection failed with both chatId and matchId');\n                setError('Unable to connect to chat. This chat may not be accessible.');\n            }\n        });\n        newSocket.on('connect_error', (error)=>{\n            console.error('🚫 Socket connection error:', error);\n            console.error('Error details:', error.message);\n            console.error('Full error object:', JSON.stringify(error, null, 2));\n            // Check if it's an authentication error\n            if (error.message && (error.message.includes('Authentication') || error.message.includes('Unauthorized'))) {\n                setError('Authentication failed. Please refresh the page and try again.');\n            } else {\n                setError('Failed to connect to chat server. Please try again.');\n            }\n        });\n        newSocket.on('error', (error)=>{\n            console.error('⚠️ Socket error:', error);\n            setError('Connection error. Please try again.');\n        });\n        // Listen for authentication errors specifically\n        newSocket.on('auth_error', (error)=>{\n            console.error('🔐 Authentication error:', error);\n            setError('Authentication failed. Please refresh the page.');\n        });\n        setSocket(newSocket);\n    };\n    const handleChatSelect = (chat)=>{\n        console.log('Selecting chat:', chat);\n        setSelectedChat(chat);\n        setMessages([]);\n        fetchMessages(chat.id);\n        // Use chat.id for WebSocket connection as per documentation\n        // The backend accepts either chatId or matchId\n        console.log('🔄 Connecting with chatId:', chat.id);\n        console.log('🔄 Available matchId:', chat.matchId);\n        connectToChat(chat.id, false); // Start with chatId\n    };\n    const sendMessage = async ()=>{\n        if (!newMessage.trim() || !selectedChat || !socket || sending) return;\n        console.log('Sending message:', newMessage.trim());\n        console.log('Socket connected:', socket.connected);\n        try {\n            setSending(true);\n            if (!socket.connected) {\n                console.error('Socket not connected, attempting to reconnect...');\n                connectToChat(selectedChat.id, false, false);\n                setError('Connection lost. Please try again.');\n                return;\n            }\n            // Send message using exact format from documentation\n            socket.emit('sendMessage', {\n                message: newMessage.trim()\n            });\n            console.log('Message emitted successfully');\n            setNewMessage('');\n        } catch (err) {\n            console.error('Error sending message:', err);\n            setError('Failed to send message');\n        } finally{\n            setSending(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const formatTime = (dateString)=>{\n        return new Date(dateString).toLocaleTimeString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    const formatLastMessageTime = (dateString)=>{\n        const now = new Date();\n        const messageDate = new Date(dateString);\n        const diffInHours = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return 'Now';\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n        return messageDate.toLocaleDateString();\n    };\n    const getInitials = (name)=>{\n        return name.split(' ').map((n)=>n[0]).join('').toUpperCase();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Chat\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Loading your conversations...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"md:col-span-1 h-[calc(100vh-200px)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2 text-[#D0544D]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Conversations\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: Array.from({\n                                            length: 3\n                                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 rounded-full bg-gray-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, i, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"md:col-span-2 h-[calc(100vh-200px)]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-8 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-8 h-8 animate-spin text-[#D0544D]\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Chat\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Connect with your matches.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 mb-4\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchChats,\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n            lineNumber: 389,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Chat\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Connect with your matches.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"md:col-span-1 h-[calc(100vh-200px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-[#D0544D]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Conversations\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-0\",\n                                children: chats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No conversations yet\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Start connecting with your matches to begin chatting!\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divide-y\",\n                                    children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 cursor-pointer hover:bg-[#F2E7DB]/50 transition-colors \".concat((selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id) === chat.id ? 'bg-[#F2E7DB]/30' : ''),\n                                            onClick: ()=>handleChatSelect(chat),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                                className: \"w-10 h-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                                        src: chat.user.image || undefined,\n                                                                        alt: chat.user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                        className: \"bg-[#D0544D]/20 text-[#D0544D]\",\n                                                                        children: getInitials(chat.user.name)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            chat.user.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium truncate\",\n                                                                children: chat.user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground truncate\",\n                                                                children: chat.lastMessage ? chat.lastMessage.message : 'No messages yet'\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: chat.lastMessage ? formatLastMessageTime(chat.lastMessage.sentAt) : ''\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, chat.id, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"md:col-span-2 h-[calc(100vh-200px)] flex flex-col\",\n                        children: selectedChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                    className: \"border-b\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mr-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                            className: \"w-8 h-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                                    src: selectedChat.user.image || undefined,\n                                                                    alt: selectedChat.user.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                    className: \"bg-[#D0544D]/20 text-[#D0544D]\",\n                                                                    children: getInitials(selectedChat.user.name)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedChat.user.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: selectedChat.user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: selectedChat.user.isOnline ? 'Online' : 'Offline'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"flex-1 overflow-auto p-4\",\n                                    children: messagesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 animate-spin text-[#D0544D]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                        children: \"No messages yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"Start the conversation by sending a message!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 23\n                                            }, this) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(message.senderId === currentUserId ? 'justify-end' : 'justify-start'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg p-3 max-w-[80%] \".concat(message.senderId === currentUserId ? 'bg-[#D0544D] text-white' : 'bg-[#F2E7DB]'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: message.message\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1 \".concat(message.senderId === currentUserId ? 'text-white/70' : 'text-muted-foreground'),\n                                                                children: formatTime(message.sentAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, message.id, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 25\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: messagesEndRef\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                type: \"text\",\n                                                placeholder: \"Type a message...\",\n                                                value: newMessage,\n                                                onChange: (e)=>setNewMessage(e.target.value),\n                                                onKeyDown: handleKeyDown,\n                                                disabled: sending,\n                                                className: \"flex-1 focus:ring-[#D0544D] focus:border-[#D0544D]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: sendMessage,\n                                                disabled: !newMessage.trim() || sending,\n                                                className: \"bg-[#D0544D] hover:bg-[#D0544D]/90 text-white\",\n                                                children: sending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"flex-1 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"mx-auto h-16 w-16 text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-medium text-gray-900 mb-2\",\n                                        children: \"Select a conversation\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Choose a conversation from the sidebar to start chatting\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                lineNumber: 412,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n        lineNumber: 406,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"S6ZjTg6MVYCshZyj22E3f9HdYt8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/chat/page.tsx\n"));

/***/ })

});