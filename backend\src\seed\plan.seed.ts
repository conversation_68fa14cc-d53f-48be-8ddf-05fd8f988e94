import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Plan, PlanDocument } from '../plan/schemas/plan.schema';
import { planSeeds } from './data/plan-seed';

@Injectable()
export class PlanSeeder {
  private readonly logger = new Logger(PlanSeeder.name);

  constructor(
    @InjectModel(Plan.name) private planModel: Model<PlanDocument>,
  ) {}

  async seed() {
    const count = await this.planModel.countDocuments().exec();
    
    if (count === 0) {
      this.logger.log('Seeding plans...');
      
      try {
        // Insert all plans
        await this.planModel.insertMany(planSeeds);
        this.logger.log(`Successfully seeded ${planSeeds.length} plans`);
        
        // Set the default plan (Gold)
        const defaultPlan = await this.planModel.findOne({ code: 'gold' }).exec();
        if (defaultPlan) {
          await this.planModel.updateMany({}, { isDefault: false }).exec();
          await this.planModel.findByIdAndUpdate(defaultPlan._id, { isDefault: true }).exec();
          this.logger.log('Default plan set to Gold');
        }
      } catch (error) {
        this.logger.error('Error seeding plans', error.stack);
      }
    } else {
      this.logger.log('Plans collection is not empty, skipping seeding');
    }
  }
}
