import { Injectable, OnModuleInit } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { ChatDocument } from './schemas/chat.schema';
import { InjectModel } from '@nestjs/mongoose';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { EVENT_MATCH_ACCEPTED } from 'src/constants/event-emitter';
import { CreateMessageDto } from './dto/chat-message.dto';
import { ChatMessageDocument } from './schemas/chat-message.schema';
import { MatchDocument } from 'src/match/schemas/match.schema';
import { PaginatedResponseDto, PaginationDto } from 'src/common/dto/pagination.dto';
import { ChatMessageResponseDto } from './dto/chat-message-response.dto';

@Injectable()
export class ChatService implements OnModuleInit {
  constructor(
    @InjectModel('Chat') private readonly chatModel: Model<ChatDocument>,
    @InjectModel('ChatMessage') private readonly chatMessageModel: Model<ChatMessageDocument>,
    private readonly eventEmitter: EventEmitter2
  ) {}

  onModuleInit() {
    this.eventEmitter.on(EVENT_MATCH_ACCEPTED, this.handleMatchAccepted.bind(this));
  }

  private handleMatchAccepted(match: any) {
    this.createChat(match.user1Id, match.user2Id, match.matchId);
  }

  async getChatsByUserId(userId: string): Promise<ChatDocument[]> {
    return await this.chatModel.find({
      $or: [
        { user1Id: Types.ObjectId.createFromHexString(userId) },
        { user2Id: Types.ObjectId.createFromHexString(userId) }
      ]
    })
    .populate('user1Id')
    .populate('user2Id')
    .populate('lastMessage')
    .sort({ createdAt: -1 })
    .exec();
  }

  async createChat(user1Id: string, user2Id: string, matchId: string): Promise<ChatDocument> {
    return await this.chatModel.findOneAndUpdate({
      $or: [
        { user1Id: Types.ObjectId.createFromHexString(user1Id) },
        { user1Id: Types.ObjectId.createFromHexString(user2Id) },
        { matchId: matchId }
      ]
    }, {
      user1Id: Types.ObjectId.createFromHexString(user1Id),
      user2Id: Types.ObjectId.createFromHexString(user2Id),
      matchId
    }, {
      upsert: true,
      new: true,
    });
  }

  async sendMessage(message: CreateMessageDto) {
    const lastMessage = await this.chatMessageModel.create({
      chatId: message.chatId,
      senderId: message.senderId,
      message: message.message,
      media: message.media,
    });

    await this.chatModel.updateOne({
      _id: Types.ObjectId.createFromHexString(message.chatId),
    }, {
      lastMessage: lastMessage._id,
    });

    return lastMessage;
  }

  async getChatById(chatId: string): Promise<ChatDocument | null> {
    return await this.chatModel.findOne({
      $or: [
        { _id: Types.ObjectId.createFromHexString(chatId) },
        { matchId: chatId }
      ]
    }).exec();
  }

  async getChatByMatchId(matchId: string): Promise<ChatDocument | null> {
    return await this.chatModel.findOne({ matchId }).exec();
  }

  async getChatMessages(chatId: string, params: PaginationDto): Promise<PaginatedResponseDto<ChatMessageResponseDto>> {
    if (!Types.ObjectId.isValid(chatId)) {
      throw new Error('Invalid chat id or match id');
    }

    const chat = await this.getChatById(chatId);

    if (!chat) {
      throw new Error('Chat not found');
    }

    const { page = 1, limit = 10 } = params;
    const skip = (page - 1) * limit;
    const messages = await this.chatMessageModel.find({ chatId: chat.id }).sort({ createdAt: -1 }).skip(skip).limit(limit).exec();
    const totalItems = await this.chatMessageModel.countDocuments({ chatId: chat.id }).exec();
    
    return {
      data: messages.map(message => ({
        id: message.id,
        chatId: message.chatId,
        senderId: message.senderId,
        message: message.message,
        media: message.media,
        sentAt: message.createdAt
      })),
      totalItems,
      itemsPerPage: limit,
      totalPages: Math.ceil(totalItems / limit),
      currentPage: page
    };
  }
}
