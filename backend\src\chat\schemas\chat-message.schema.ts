import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ChatMessageDocument = ChatMessage & Document;

export enum MediaType {
  IMAGE = 'image',
  VIDEO = 'video'
};

export class Media {
  @Prop({ type: String })
  url: string;

  @Prop({ type: String })
  key: string;

  @Prop({ type: MediaType, default: MediaType.IMAGE })
  type: string;
}

@Schema({ collection: 'chat-messages', timestamps: true })
export class ChatMessage {
  @Prop({ type: String })
  chatId: string;

  @Prop({ type: String })
  senderId: string;

  @Prop()
  message: string;

  @Prop({ type: Media, default: null })
  media: Media;

  @Prop({ type: Boolean, default: false })
  isRead: boolean;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  createdAt: Date;
  updatedAt: Date;
}

export const ChatMessageSchema = SchemaFactory.createForClass(ChatMessage);
