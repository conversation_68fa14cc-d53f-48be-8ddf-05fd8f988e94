"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/chat/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/chat/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/dashboard/chat/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatPage() {\n    _s();\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [selectedChat, setSelectedChat] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [messagesLoading, setMessagesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [currentUserId, setCurrentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            fetchChats();\n            getCurrentUserId();\n            return ({\n                \"ChatPage.useEffect\": ()=>{\n                    if (socket) {\n                        console.log('Cleaning up socket connection');\n                        socket.disconnect();\n                    }\n                }\n            })[\"ChatPage.useEffect\"];\n        }\n    }[\"ChatPage.useEffect\"], []);\n    // Auto-select first chat when chats are loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            if (chats.length > 0 && !selectedChat) {\n                console.log('Auto-selecting first chat:', chats[0]);\n                handleChatSelect(chats[0]);\n            }\n        }\n    }[\"ChatPage.useEffect\"], [\n        chats,\n        selectedChat\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatPage.useEffect\"], [\n        messages\n    ]);\n    const getCurrentUserId = async ()=>{\n        try {\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) return;\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/auth/profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const profile = await response.json();\n                setCurrentUserId(profile.id);\n            }\n        } catch (err) {\n            console.error('Failed to get current user ID:', err);\n        }\n    };\n    const fetchChats = async ()=>{\n        try {\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) {\n                router.replace('/login');\n                return;\n            }\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/chats\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                if (response.status === 401) {\n                    localStorage.removeItem('pairsona_token');\n                    router.replace('/login');\n                    return;\n                }\n                throw new Error('Failed to fetch chats');\n            }\n            const chatsData = await response.json();\n            console.log('Fetched chats:', chatsData);\n            setChats(chatsData);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load chats');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchMessages = async (chatId)=>{\n        try {\n            setMessagesLoading(true);\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) return;\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/chats/\").concat(chatId, \"/messages?page=1&limit=50\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch messages');\n            }\n            const messagesData = await response.json();\n            setMessages(messagesData.data.reverse()); // Reverse to show oldest first\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load messages');\n        } finally{\n            setMessagesLoading(false);\n        }\n    };\n    const connectToChat = (chatId)=>{\n        const token = localStorage.getItem('pairsona_token');\n        if (!token) return;\n        // Disconnect existing socket\n        if (socket) {\n            socket.disconnect();\n        }\n        console.log('🚀 Connecting to chat with chatId:', chatId);\n        console.log('🌐 NEXT_PUBLIC_SOCKET_URL:', \"https://api.pairsona.id\");\n        console.log('🌐 NEXT_PUBLIC_API_BASE_URL:', \"https://api.pairsona.id\");\n        console.log('🔑 Token from localStorage:', token);\n        console.log('🔑 Token length:', token.length);\n        console.log('🔑 Token starts with:', token.substring(0, 20) + '...');\n        console.log('🔑 Token ends with:', '...' + token.substring(token.length - 10));\n        // Test if this token works with REST API\n        fetch(\"\".concat(\"https://api.pairsona.id\", \"/auth/profile\"), {\n            headers: {\n                'Authorization': \"Bearer \".concat(token)\n            }\n        }).then((res)=>{\n            console.log('REST API test with same token:', res.ok ? 'SUCCESS' : 'FAILED');\n        }).catch((err)=>{\n            console.log('REST API test error:', err);\n        });\n        // Use EXACT same format as backend team\n        console.log('🔧 Using EXACT backend format...');\n        // Clean token (remove any whitespace)\n        const cleanToken = token.trim();\n        console.log('Clean token length:', cleanToken.length);\n        // Try both SOCKET_URL and API_BASE_URL\n        const baseUrl = \"https://api.pairsona.id\" || 0;\n        const socketUrl = \"\".concat(baseUrl, \"/chat\");\n        console.log('🌐 Base URL used:', baseUrl);\n        console.log('🌐 Full Socket URL:', socketUrl);\n        // Log exact configuration being sent\n        const config = {\n            transports: [\n                'websocket'\n            ],\n            auth: {\n                token: cleanToken\n            },\n            extraHeaders: {\n                Authorization: \"Bearer \".concat(cleanToken)\n            },\n            query: {\n                chatId: chatId\n            }\n        };\n        console.log('📋 Exact Socket.IO config:', JSON.stringify(config, null, 2));\n        const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_7__.io)(socketUrl, config);\n        // Log socket instance details\n        console.log('🔌 Socket instance created');\n        console.log('🔌 Socket.io version:', newSocket.io.engine.constructor.name);\n        newSocket.on('connect', ()=>{\n            console.log('✅ Connected to chat server, chatId:', chatId);\n            console.log('Socket ID:', newSocket.id);\n            console.log('🔍 Checking if chatId is valid...');\n            // Send a test event to see if backend responds\n            newSocket.emit('ping', {\n                chatId: chatId\n            });\n        });\n        newSocket.on('message', (message)=>{\n            console.log('📨 New message received:', message);\n            setMessages((prev)=>[\n                    ...prev,\n                    message\n                ]);\n            // Update last message in chats list\n            setChats((prev)=>prev.map((chat)=>chat.id === message.chatId ? {\n                        ...chat,\n                        lastMessage: {\n                            id: message.id,\n                            senderId: message.senderId,\n                            message: message.message,\n                            sentAt: message.sentAt\n                        }\n                    } : chat));\n        });\n        newSocket.on('disconnect', (reason)=>{\n            console.log('❌ Disconnected from chat server, reason:', reason);\n        });\n        newSocket.on('connect_error', (error)=>{\n            console.error('🚫 Socket connection error:', error);\n            console.error('Error details:', error.message);\n            setError('Failed to connect to chat server. Please try again.');\n        });\n        newSocket.on('error', (error)=>{\n            console.error('⚠️ Socket error:', error);\n            setError('Connection error. Please try again.');\n        });\n        // Additional debugging events\n        newSocket.on('connect_timeout', ()=>{\n            console.error('⏰ Connection timeout');\n        });\n        newSocket.on('reconnect', (attemptNumber)=>{\n            console.log('🔄 Reconnected after', attemptNumber, 'attempts');\n        });\n        newSocket.on('reconnect_error', (error)=>{\n            console.error('🔄❌ Reconnection error:', error);\n        });\n        setSocket(newSocket);\n    };\n    const handleChatSelect = (chat)=>{\n        console.log('Selecting chat:', chat);\n        setSelectedChat(chat);\n        setMessages([]);\n        fetchMessages(chat.id);\n        // Try both chatId and matchId\n        console.log('🔄 Trying connection with chatId:', chat.id);\n        connectToChat(chat.id);\n    // If that fails, we could try with matchId\n    // setTimeout(() => {\n    //   if (!socket || !socket.connected) {\n    //     console.log('🔄 Retrying with matchId:', chat.matchId);\n    //     connectToChat(chat.matchId);\n    //   }\n    // }, 2000);\n    };\n    const sendMessage = async ()=>{\n        if (!newMessage.trim() || !selectedChat || !socket || sending) return;\n        console.log('Sending message:', newMessage.trim());\n        console.log('Socket connected:', socket.connected);\n        console.log('Selected chat:', selectedChat.id);\n        try {\n            setSending(true);\n            if (!socket.connected) {\n                console.error('Socket not connected, attempting to reconnect...');\n                connectToChat(selectedChat.id);\n                setError('Connection lost. Please try again.');\n                return;\n            }\n            socket.emit('sendMessage', {\n                message: newMessage.trim()\n            });\n            console.log('Message emitted successfully');\n            setNewMessage('');\n        } catch (err) {\n            console.error('Error sending message:', err);\n            setError('Failed to send message');\n        } finally{\n            setSending(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const formatTime = (dateString)=>{\n        return new Date(dateString).toLocaleTimeString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    const formatLastMessageTime = (dateString)=>{\n        const now = new Date();\n        const messageDate = new Date(dateString);\n        const diffInHours = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return 'Now';\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n        return messageDate.toLocaleDateString();\n    };\n    const getInitials = (name)=>{\n        return name.split(' ').map((n)=>n[0]).join('').toUpperCase();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Chat\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Loading your conversations...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"md:col-span-1 h-[calc(100vh-200px)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2 text-[#D0544D]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Conversations\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: Array.from({\n                                            length: 3\n                                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 rounded-full bg-gray-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, i, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"md:col-span-2 h-[calc(100vh-200px)]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-8 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-8 h-8 animate-spin text-[#D0544D]\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n            lineNumber: 375,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Chat\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Connect with your matches.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 mb-4\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchChats,\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n            lineNumber: 416,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Chat\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Connect with your matches.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                lineNumber: 434,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"md:col-span-1 h-[calc(100vh-200px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-[#D0544D]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Conversations\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-0\",\n                                children: chats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No conversations yet\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Start connecting with your matches to begin chatting!\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divide-y\",\n                                    children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 cursor-pointer hover:bg-[#F2E7DB]/50 transition-colors \".concat((selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id) === chat.id ? 'bg-[#F2E7DB]/30' : ''),\n                                            onClick: ()=>handleChatSelect(chat),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                                className: \"w-10 h-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                                        src: chat.user.image || undefined,\n                                                                        alt: chat.user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                        className: \"bg-[#D0544D]/20 text-[#D0544D]\",\n                                                                        children: getInitials(chat.user.name)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            chat.user.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium truncate\",\n                                                                children: chat.user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground truncate\",\n                                                                children: chat.lastMessage ? chat.lastMessage.message : 'No messages yet'\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: chat.lastMessage ? formatLastMessageTime(chat.lastMessage.sentAt) : ''\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, chat.id, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"md:col-span-2 h-[calc(100vh-200px)] flex flex-col\",\n                        children: selectedChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                    className: \"border-b\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mr-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                            className: \"w-8 h-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                                    src: selectedChat.user.image || undefined,\n                                                                    alt: selectedChat.user.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                    className: \"bg-[#D0544D]/20 text-[#D0544D]\",\n                                                                    children: getInitials(selectedChat.user.name)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedChat.user.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: selectedChat.user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: selectedChat.user.isOnline ? 'Online' : 'Offline'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"flex-1 overflow-auto p-4\",\n                                    children: messagesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 animate-spin text-[#D0544D]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                        children: \"No messages yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"Start the conversation by sending a message!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 23\n                                            }, this) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(message.senderId === currentUserId ? 'justify-end' : 'justify-start'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg p-3 max-w-[80%] \".concat(message.senderId === currentUserId ? 'bg-[#D0544D] text-white' : 'bg-[#F2E7DB]'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: message.message\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1 \".concat(message.senderId === currentUserId ? 'text-white/70' : 'text-muted-foreground'),\n                                                                children: formatTime(message.sentAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, message.id, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 25\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: messagesEndRef\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                type: \"text\",\n                                                placeholder: \"Type a message...\",\n                                                value: newMessage,\n                                                onChange: (e)=>setNewMessage(e.target.value),\n                                                onKeyDown: handleKeyDown,\n                                                disabled: sending,\n                                                className: \"flex-1 focus:ring-[#D0544D] focus:border-[#D0544D]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: sendMessage,\n                                                disabled: !newMessage.trim() || sending,\n                                                className: \"bg-[#D0544D] hover:bg-[#D0544D]/90 text-white\",\n                                                children: sending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"flex-1 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"mx-auto h-16 w-16 text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-medium text-gray-900 mb-2\",\n                                        children: \"Select a conversation\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Choose a conversation from the sidebar to start chatting\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n        lineNumber: 433,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"S6ZjTg6MVYCshZyj22E3f9HdYt8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/chat/page.tsx\n"));

/***/ })

});