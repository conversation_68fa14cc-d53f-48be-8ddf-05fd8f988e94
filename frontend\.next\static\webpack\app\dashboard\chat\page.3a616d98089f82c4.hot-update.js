"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/chat/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/chat/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/dashboard/chat/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2,MessageSquare,Send,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ChatPage() {\n    _s();\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [selectedChat, setSelectedChat] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [messagesLoading, setMessagesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [currentUserId, setCurrentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            fetchChats();\n            getCurrentUserId();\n            return ({\n                \"ChatPage.useEffect\": ()=>{\n                    if (socket) {\n                        console.log('Cleaning up socket connection');\n                        socket.disconnect();\n                    }\n                }\n            })[\"ChatPage.useEffect\"];\n        }\n    }[\"ChatPage.useEffect\"], [\n        socket\n    ]); // Add socket to dependency array\n    // Auto-select first chat when chats are loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            if (chats.length > 0 && !selectedChat) {\n                console.log('Auto-selecting first chat:', chats[0]);\n                handleChatSelect(chats[0]);\n            }\n        }\n    }[\"ChatPage.useEffect\"], [\n        chats,\n        selectedChat\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatPage.useEffect\"], [\n        messages\n    ]);\n    const getCurrentUserId = async ()=>{\n        try {\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) return;\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/auth/profile\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const profile = await response.json();\n                setCurrentUserId(profile.id);\n            }\n        } catch (err) {\n            console.error('Failed to get current user ID:', err);\n        }\n    };\n    const fetchChats = async ()=>{\n        try {\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) {\n                router.replace('/login');\n                return;\n            }\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/chats\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                if (response.status === 401) {\n                    localStorage.removeItem('pairsona_token');\n                    router.replace('/login');\n                    return;\n                }\n                throw new Error('Failed to fetch chats');\n            }\n            const chatsData = await response.json();\n            setChats(chatsData);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load chats');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchMessages = async (chatId)=>{\n        try {\n            setMessagesLoading(true);\n            const token = localStorage.getItem('pairsona_token');\n            if (!token) throw new Error('No token available');\n            const response = await fetch(\"\".concat(\"https://api.pairsona.id\", \"/chats/\").concat(chatId, \"/messages?page=1&limit=50\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to fetch messages: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const messagesData = await response.json();\n            setMessages(messagesData.data.reverse()); // Reverse to show oldest first\n            return messagesData; // Return data for success verification\n        } catch (err) {\n            console.error('Error fetching messages:', err);\n            setError(err instanceof Error ? err.message : 'Failed to load messages');\n            throw err; // Re-throw for handleChatSelect to catch\n        } finally{\n            setMessagesLoading(false);\n        }\n    };\n    const connectToChat = (chatId)=>{\n        const token = localStorage.getItem('pairsona_token');\n        if (!token) return;\n        // Disconnect existing socket\n        if (socket) {\n            socket.disconnect();\n        }\n        const socketUrl = \"\".concat(\"https://api.pairsona.id\", \"/chat\");\n        const config = {\n            transports: [\n                'websocket'\n            ],\n            auth: {\n                authorization: \"Bearer \".concat(token)\n            },\n            query: {\n                chatId: chatId\n            }\n        };\n        const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_7__.io)(socketUrl, config);\n        newSocket.on('connect', ()=>{\n        // Connection successful\n        });\n        newSocket.on('message', (message)=>{\n            setMessages((prev)=>[\n                    ...prev,\n                    message\n                ]);\n            // Update last message in chats list\n            setChats((prev)=>prev.map((chat)=>chat.id === message.chatId ? {\n                        ...chat,\n                        lastMessage: {\n                            id: message.id,\n                            senderId: message.senderId,\n                            message: message.message,\n                            sentAt: message.sentAt\n                        }\n                    } : chat));\n        });\n        newSocket.on('disconnect', (reason)=>{\n            if (reason === 'io server disconnect') {\n                setError('Unable to connect to chat. Please check your authentication.');\n            }\n        });\n        newSocket.on('connect_error', (error)=>{\n            if (error.message && (error.message.includes('Authentication') || error.message.includes('Unauthorized'))) {\n                setError('Authentication failed. Please refresh the page and try again.');\n            } else {\n                setError('Failed to connect to chat server. Please try again.');\n            }\n        });\n        newSocket.on('error', ()=>{\n            setError('Connection error. Please try again.');\n        });\n        setSocket(newSocket);\n    };\n    const handleChatSelect = async (chat)=>{\n        setSelectedChat(chat);\n        setMessages([]);\n        try {\n            await fetchMessages(chat.id);\n            connectToChat(chat.id);\n        } catch (error) {\n            setError('Unable to access this chat. Please try another conversation.');\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!newMessage.trim() || !selectedChat || !socket || sending) return;\n        try {\n            setSending(true);\n            if (!socket.connected) {\n                connectToChat(selectedChat.id);\n                setError('Connection lost. Please try again.');\n                return;\n            }\n            socket.emit('sendMessage', {\n                message: newMessage.trim()\n            });\n            setNewMessage('');\n        } catch (err) {\n            setError('Failed to send message');\n        } finally{\n            setSending(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const formatTime = (dateString)=>{\n        return new Date(dateString).toLocaleTimeString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n        });\n    };\n    const formatLastMessageTime = (dateString)=>{\n        const now = new Date();\n        const messageDate = new Date(dateString);\n        const diffInHours = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return 'Now';\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n        return messageDate.toLocaleDateString();\n    };\n    const getInitials = (name)=>{\n        return name.split(' ').map((n)=>n[0]).join('').toUpperCase();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Chat\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Loading your conversations...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"md:col-span-1 h-[calc(100vh-200px)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2 text-[#D0544D]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Conversations\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: Array.from({\n                                            length: 3\n                                        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 rounded-full bg-gray-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, i, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"md:col-span-2 h-[calc(100vh-200px)]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-8 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-8 h-8 animate-spin text-[#D0544D]\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n            lineNumber: 310,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold tracking-tight\",\n                            children: \"Chat\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Connect with your matches.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 mb-4\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: fetchChats,\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n            lineNumber: 351,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Chat\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Connect with your matches.\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"md:col-span-1 h-[calc(100vh-200px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-[#D0544D]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Conversations\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-0\",\n                                children: chats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No conversations yet\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Start connecting with your matches to begin chatting!\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"divide-y\",\n                                    children: chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 cursor-pointer hover:bg-[#F2E7DB]/50 transition-colors \".concat((selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id) === chat.id ? 'bg-[#F2E7DB]/30' : ''),\n                                            onClick: ()=>handleChatSelect(chat),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                                className: \"w-10 h-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                                        src: chat.user.image || undefined,\n                                                                        alt: chat.user.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                        className: \"bg-[#D0544D]/20 text-[#D0544D]\",\n                                                                        children: getInitials(chat.user.name)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            chat.user.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium truncate\",\n                                                                children: chat.user.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground truncate\",\n                                                                children: chat.lastMessage ? chat.lastMessage.message : 'No messages yet'\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: chat.lastMessage ? formatLastMessageTime(chat.lastMessage.sentAt) : ''\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, chat.id, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"md:col-span-2 h-[calc(100vh-200px)] flex flex-col\",\n                        children: selectedChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                    className: \"border-b\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mr-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                            className: \"w-8 h-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                                    src: selectedChat.user.image || undefined,\n                                                                    alt: selectedChat.user.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                    className: \"bg-[#D0544D]/20 text-[#D0544D]\",\n                                                                    children: getInitials(selectedChat.user.name)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedChat.user.isOnline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: selectedChat.user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: selectedChat.user.isOnline ? 'Online' : 'Offline'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"flex-1 overflow-auto p-4\",\n                                    children: messagesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-6 h-6 animate-spin text-[#D0544D]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                        children: \"No messages yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"Start the conversation by sending a message!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 23\n                                            }, this) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(message.senderId === currentUserId ? 'justify-end' : 'justify-start'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg p-3 max-w-[80%] \".concat(message.senderId === currentUserId ? 'bg-[#D0544D] text-white' : 'bg-[#F2E7DB]'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: message.message\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1 \".concat(message.senderId === currentUserId ? 'text-white/70' : 'text-muted-foreground'),\n                                                                children: formatTime(message.sentAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, message.id, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 25\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                ref: messagesEndRef\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-t\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                type: \"text\",\n                                                placeholder: \"Type a message...\",\n                                                value: newMessage,\n                                                onChange: (e)=>setNewMessage(e.target.value),\n                                                onKeyDown: handleKeyDown,\n                                                disabled: sending,\n                                                className: \"flex-1 focus:ring-[#D0544D] focus:border-[#D0544D]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: sendMessage,\n                                                disabled: !newMessage.trim() || sending,\n                                                className: \"bg-[#D0544D] hover:bg-[#D0544D]/90 text-white\",\n                                                children: sending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"flex-1 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_MessageSquare_Send_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"mx-auto h-16 w-16 text-gray-400 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-medium text-gray-900 mb-2\",\n                                        children: \"Select a conversation\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500\",\n                                        children: \"Choose a conversation from the sidebar to start chatting\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n                lineNumber: 374,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\pairsona\\\\frontend\\\\src\\\\app\\\\dashboard\\\\chat\\\\page.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"S6ZjTg6MVYCshZyj22E3f9HdYt8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/chat/page.tsx\n"));

/***/ })

});