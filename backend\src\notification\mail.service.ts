import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Mailjet from 'node-mailjet';

export enum EmailTemplate {
  USER_SIGNUP = 7034260,
  SUBSCRIPTION_REMINDER = 7034379,
  SUBSCRIPTION_CANCELLED = 7034380
}

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);
  private readonly mailjet: Mailjet.Client;

  constructor(
    private readonly configService: ConfigService
  ) {
    this.mailjet = new Mailjet.Client({
      apiKey: this.configService.get<string>('app.mailjet.publicKey'),
      apiSecret: this.configService.get<string>('app.mailjet.privateKey'),
    });
  }

  sendMail(to: string, subject: string, templateId: EmailTemplate, variables: any) {
    this.mailjet
    .post('send', { version: 'v3.1' })
    .request({
      Messages: [
        {
          From: {
            Email: this.configService.get<string>('app.mailjet.senderEmail'),
            Name: this.configService.get<string>('app.mailjet.senderName'),
          },
          To: [{ Email: to }],
          Subject: subject,
          TemplateID: templateId,
          TemplateLanguage: true,
          Variables: variables
        }
      ]
    }).then((res) => {
      // Only log the essential information to avoid circular reference issues
      this.logger.log(`Email sent successfully to ${to}`);
    }).catch((err) => {
      this.logger.error(`Failed to send email to ${to}: ${err.message || err}`);
    });
  }
}
