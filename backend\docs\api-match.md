# Match API Documentation

This document provides detailed information about the Match API endpoints, request/response formats, and examples.

## Table of Contents
- [Get User Matches](#get-user-matches)
- [Invite to Match](#invite-to-match)
- [Accept Match](#accept-match)
- [Reject Match](#reject-match)
- [Data Types](#data-types)
  - [Match Response](#match-response)
  - [Match Details](#match-details)
  - [Match Status](#match-status)
  - [Invitation Type](#invitation-type)

## Base URL
```
{{base_url}}/match
```

## Authentication
All endpoints require JWT authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Get User Matches

Retrieves paginated matches for the authenticated user with filtering options.

### Endpoint
```
GET /
```

### Query Parameters
| Parameter | Type | Required | Description | Default |
|-----------|------|----------|-------------|---------|
| page | integer | No | Page number | 1 |
| limit | integer | No | Number of items per page | 10 |
| type | string | No | Filter by invitation type (incoming, outgoing, connected) | - |

### Invitation Types
- `incoming`: Matches where the user has been invited by someone else
- `outgoing`: Matches where the user has sent an invitation
- `connected`: Matches that have been accepted by both users

### Response
```json
{
  "data": [
    {
      "id": "60d5ec9f2f8e4c3d4c8b4567",
      "user": {
        "id": "60d5ec9f2f8e4c3d4c8b4568",
        "name": "John Doe",
        "image": "https://example.com/images/john.jpg",
        "religion": "Islam",
        "gender": "Male",
        "age": 28,
        "occupation": "Software Engineer",
        "isSmoker": false,
        "acceptDifferentReligion": true,
        "about": "I love hiking and reading books."
    },
    "matchScore": 85.5,
    "matchDetails": {
      "extraversion": 4.2,
      "agreeableness": 3.8,
      "conscientiousness": 4.0,
      "negativeEmotionality": 2.1,
      "openMindedness": 4.5,
      "selfDisclosure": 3.9
    },
    "interpretation": "⭐ Kecocokan sangat tinggi",
    "invitedBy": "60d5ec9f2f8e4c3d4c8b4569",
    "acceptedBy": null,
    "rejectedBy": null,
    "status": "pending",
    "isConnected": false
  },
  // ... more matches
]
```

## Invite to Match

Sends a match invitation to another user. This changes the match status to `INVITED`.

### Endpoint
```
POST /:matchId/invite
```

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| matchId | string | Yes | ID of the match to invite to |

### Response
```json
{
  "message": "Match invitation sent successfully"
}
```

### Possible Errors
- `404 Not Found`: Match not found
- `400 Bad Request`: User is not a member of this match
- `400 Bad Request`: Match has already been invited by another user
- `400 Bad Request`: Match has been rejected by the other user

## Accept Match

Accepts a match invitation. This changes the match status to `ACCEPTED`.

### Endpoint
```
POST /:matchId/accept
```

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| matchId | string | Yes | ID of the match to accept |

### Response
```json
{
  "message": "Match accepted successfully"
}
```

### Possible Errors
- `404 Not Found`: Match not found
- `400 Bad Request`: User is not a member of this match
- `400 Bad Request`: Match has not been invited yet
- `400 Bad Request`: User is the one who sent the invitation

## Reject Match

Rejects a match invitation. This changes the match status to `REJECTED`.

### Endpoint
```
POST /:matchId/reject
```

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| matchId | string | Yes | ID of the match to reject |

### Response
```json
{
  "message": "Match rejected successfully"
}
```

### Possible Errors
- `404 Not Found`: Match not found
- `400 Bad Request`: User is not a member of this match
- `400 Bad Request`: Match has not been invited yet
- `400 Bad Request`: User is the one who sent the invitation
- `400 Bad Request`: Match has already been accepted or rejected

## Data Types

### Match Status
```typescript
export enum MatchStatus {
  PENDING = 'pending',     // Initial state, no actions taken
  INVITED = 'invited',     // One user has sent an invitation
  ACCEPTED = 'accepted',   // The other user has accepted the invitation
  REJECTED = 'rejected',   // The other user has rejected the invitation
  EXPIRED = 'expired',     // The match has expired
}
```

### Invitation Type
```typescript
export enum InvitationType {
  INCOMING = 'incoming',   // Matches where user has been invited
  OUTGOING = 'outgoing',   // Matches where user sent the invitation
  CONNECTED = 'connected'  // Matches that have been accepted
}
```

### Match Response
```typescript
{
  id: string;               // Match ID
  user: {                   // The other user in the match
    id: string;             // User ID
    name: string;           // User's full name
    image: string | null;   // URL to user's profile image (or null)
    religion: string;       // User's religion
    gender: string;         // User's gender
    age: number;            // User's age
    occupation: string;     // User's occupation
    isSmoker: boolean;      // Whether the user smokes
    acceptDifferentReligion: boolean; // Whether user accepts different religion
    about: string;          // User's bio/description
  };
  matchScore: number;       // Compatibility score (0-100)
  matchDetails: MatchDetails; // Detailed compatibility scores
  interpretation: string;   // Human-readable interpretation of the match score
  invitedBy: string | null; // User ID who sent the invitation
  acceptedBy: string | null; // User ID who accepted the match
  rejectedBy: string | null; // User ID who rejected the match
  status: MatchStatus;      // Current status of the match
  isConnected: boolean;     // Whether the match is connected (both accepted)
}
```

### Match Details
Detailed compatibility scores between users:
```typescript
{
  extraversion: number;       // Extraversion compatibility (1-5)
  agreeableness: number;      // Agreeableness compatibility (1-5)
  conscientiousness: number;  // Conscientiousness compatibility (1-5)
  negativeEmotionality: number; // Negative emotionality compatibility (1-5)
  openMindedness: number;     // Open-mindedness compatibility (1-5)
  sharedInterests: number;    // Shared interests compatibility (1-5)
  values: number;            // Shared values compatibility (1-5)
  lifestyle: number;         // Lifestyle compatibility (1-5)
  futurePlans: number;       // Future plans compatibility (1-5)
  communication: number;     // Communication style compatibility (1-5)
}
