import { Logger, UnauthorizedException } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer
} from '@nestjs/websockets';
import { JwtService } from '@nestjs/jwt';
import { JwtPayload } from 'src/auth/interfaces/jwt-payload.interface';
import { ChatService } from './chat.service';
import { Types } from 'mongoose';
import { UserService } from 'src/user/user.service';
import { UserOnlineService } from './user-online.service';

@WebSocketGateway({
  transports: ['websocket'],
  namespace: '/chat',
  cors: {
    origin: ['http://localhost:3000', 'https://app.pairsona.id'],
  },
})
export class ChatGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  private readonly logger = new Logger(ChatGateway.name);

  @WebSocketServer() server: Server;

  constructor(
    private jwtService: JwtService,
    private chatService: ChatService,
    private userService: UserService,
    private userOnlineService: UserOnlineService
  ) {}

  /**
   * Initializes the webchat gateway.
   */
  async afterInit() {
    this.logger.log('Webchat Gateway Initialized');
  }

  /**
   * Handles the send message event.
   * 
   * @param socket The socket to handle.
   * @param data The data to handle.
   * @throws Error if the chat id is invalid or the chat is not found.
   */
  @SubscribeMessage('sendMessage')
  async handleSendMessage(socket: Socket, data: any) {
    try {
      this.logger.log(`Message received from client id: ${socket.id}, with user id: ${socket.data.user.id}`);

      const message = await this.chatService.sendMessage({
        chatId: socket.data.user.chatId,
        senderId: socket.data.user.id,
        message: data.message
      });
      
      this.emitToSocket(socket.data.user.chatId, 'message', {
        id: message.id,
        senderId: message.senderId,
        message: message.message,
        sentAt: message.createdAt
      });
    } catch (error) {
      this.logger.error(error.message);
    }
  }

  /**
   * Handles the socket disconnection.
   * 
   * @param socket The socket to handle.
   */
  async handleDisconnect(socket: Socket): Promise<void> {
    this.logger.log(`Client disconnected: ${socket.id}`);

    if (socket.data.user) {
      await this.userOnlineService.setUserOffline(socket.data.user.id);
    }
  }

  /**
   * Emits an event to a specific socket.
   * 
   * @param chatId The chat id.
   * @param event The event to emit.
   * @param payload The payload to emit.
   * @returns A promise that resolves when the event is emitted.
   */
  private async emitToSocket(
    chatId: string,
    event: string,
    payload: any,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server.to(chatId).emit(event, payload, (response: any) => {
        if (response && response.error) {
          reject(new Error(response.error));
        } else {
          resolve();
        }
      });
    });
  }
  
  /**
   * Initializes the user connection.
   * 
   * @param userPayload The user payload.
   * @param socket The socket to initialize.
   * @throws Error if the chat id is invalid or the chat is not found.
   */
  private async initializeUserConnection(
    userPayload: JwtPayload,
    socket: Socket,
  ): Promise<void> {
    try {
      // `socket.handshake.query.chatId` can be chat id or match id
      const chatId = socket.handshake.query.chatId as string;

      if (!Types.ObjectId.isValid(chatId)) {
        throw new Error('Invalid chat id');
      }

      let chat = await this.chatService.getChatById(chatId);

      if (!chat) {
        throw new Error('Chat not found');
      }

      const user = await this.userService.findOne(userPayload.sub);

      if (!user) {
        throw new Error('User not found');
      }

      // Update user online status
      await this.userOnlineService.setUserOnline(user.id);

      socket.data.user = {
        id: user.id,
        name: user.name,
        image: user.image?.url || '',
        chatId: chat.id
      }

      socket.join(chat.id);

      this.logger.log(`Client connected: ${socket.id}, User ID: ${userPayload.sub}, Chat ID: ${chat.id}`);
    } catch (error) {
      this.logger.error(error.message);
      throw error;
    }
  }

  /**
   * Handles the socket connection.
   * 
   * @param socket The socket to handle.
   * @throws UnauthorizedException if the socket is not authenticated.
   */
  async handleConnection(socket: Socket): Promise<void> {
    try {
      const user = this.authenticateSocket(socket);
      await this.initializeUserConnection(user, socket);
    } catch (error) {
      this.handleConnectionError(socket, error);
    }
  }

  /**
   * Authenticates the socket using the JWT token.
   * 
   * @param socket The socket to authenticate.
   * @returns The JWT payload.
   * @throws UnauthorizedException if the token is invalid or missing.
   */
  private authenticateSocket(socket: Socket): JwtPayload {
    const token = this.extractJwtToken(socket);
    return this.jwtService.verify<JwtPayload>(token);
  }

  /**
   * Handles connection errors.
   * 
   * @param socket The socket that caused the error.
   * @param error The error that occurred.
   */
  private handleConnectionError(socket: Socket, error: Error): void {
    this.logger.error(`Connection error for socket ${socket.id}: ${error.message}`);

    socket.emit('exception', 'Authentication error');
    socket.disconnect();
  }

  /**
   * Extracts the JWT token from the socket's handshake headers or auth object.
   * 
   * @param socket The socket to extract the token from.
   * @returns The JWT token.
   * @throws UnauthorizedException if no authorization header is found.
   */
  private extractJwtToken(socket: Socket): string {
    const authHeader = socket.handshake.headers.authorization || socket.handshake.auth.authorization;

    if (!authHeader) {
      throw new UnauthorizedException('No authorization header found');
    }

    const [bearer, token] = authHeader.split(' ');

    if (bearer !== 'Bearer' || !token) {
      throw new UnauthorizedException('Invalid or missing token');
    }

    return token;
  }
}