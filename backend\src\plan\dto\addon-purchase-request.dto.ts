import { IsString, <PERSON>Number, Min, IsArray, ValidateNested, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';

export class ItemPurchaseRequestDto {
  @IsString()
  id: string;

  @IsNumber()
  @Min(1)
  quantity: number;
}

export class AddonPurchaseRequestDto {
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one item must be provided' })
  @ValidateNested({ each: true })
  @Type(() => ItemPurchaseRequestDto)
  items: ItemPurchaseRequestDto[];
}

  
