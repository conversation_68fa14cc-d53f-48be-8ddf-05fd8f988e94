import { MatchDetails } from "../schemas/match.schema";

export class MatchResponseDto {
  id: string;
  user: {
    id: string;
    name: string;
    image: string;
    religion: string;
    gender: string;
    age: number;
    occupation: string;
    isSmoker: boolean;
    acceptDifferentReligion: boolean;
    about: string;
  };
  matchScore: number;
  matchDetails: MatchDetails;
  interpretation: string;
  rejectedBy: string | null;
  acceptedBy: string | null;
  invitedBy: string | null;
  status: string;
  isConnected: boolean;
}
    