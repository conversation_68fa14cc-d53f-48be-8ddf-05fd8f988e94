import { BadRequestException, forwardRef, Inject, Injectable, Logger, NotFoundException, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Resource, Subscription, SubscriptionDocument, SubscriptionStatus } from '../schemas/subscription.schema';
import { PlanService } from './plan.service';
import { PlanInterval, PlanStatus } from '../schemas/plan.schema';
import { InvoiceService } from './invoice.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { NotificationEvent } from 'src/notification/notification.service';

export const TRIAL_DAYS = 7;
export const GRACE_PERIOD_DAYS = 3;

export enum SubscriptionUsageType {
  CONSULTATIONS = 'psychologistConsultationsCount',
  CHATS = 'chatsCount'
}

/**
 * Map subscription usage type to plan resource
 */
export const RESOURCE_USAGE_MAP: Record<SubscriptionUsageType, string> = {
  'psychologistConsultationsCount': 'psychologistConsultations',
  'chatsCount': 'chats'
}

@Injectable()
export class SubscriptionService implements OnModuleInit {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    @InjectModel(Subscription.name) private subscriptionModel: Model<SubscriptionDocument>,
    private readonly planService: PlanService,
    @Inject(forwardRef(() => InvoiceService)) private readonly invoiceService: InvoiceService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  onModuleInit() {
    // this listen all usage events
    this.logger.log('Listening to plan.resource.used events');
    this.eventEmitter.on('plan.resource.used', (data) => {
      this.logger.log(`Resource used: ${JSON.stringify(data)} by user ${data.userId}`);
      this.updateSubscriptionUsage(data.userId, data.type, data.amount);
    });
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async cancelExpiredSubscriptions() {
    this.logger.log('Checking for expired subscriptions');

    // for active subscriptions
    const subscriptions = await this.subscriptionModel.find({
      status: SubscriptionStatus.ACTIVE,
      endDate: {
        $lte: new Date(new Date().setDate(new Date().getDate() - GRACE_PERIOD_DAYS))
      }
    }).exec();

    subscriptions.forEach((subscription) => {
      this.logger.log(`Active subscription expired: ${subscription.userId}`);
      this.logger.log(`Found ${JSON.stringify(subscription)}`);

      this.cancelSubscription(subscription.userId);
      this.eventEmitter.emit(NotificationEvent.SUBSCRIPTION_CANCELLED, {
        userId: subscription.userId,
        subscription
      });
    });

    // for trial subscriptions
    const trialSubscriptions = await this.subscriptionModel.find({
      status: SubscriptionStatus.TRIAL,
      endDate: {
        $lte: new Date()
      }
    }).exec();

    trialSubscriptions.forEach((subscription) => {
      this.logger.log(`Trial subscription expired: ${subscription.userId}`);
      this.cancelSubscription(subscription.userId);
      this.eventEmitter.emit(NotificationEvent.SUBSCRIPTION_CANCELLED, {
        userId: subscription.userId,
        subscription
      });
    });
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async remindExpiredSubscriptions() {
    this.logger.log('Checking for subscriptions that are about to expire');

    // for active subscriptions
    const subscriptions = await this.subscriptionModel.find({
      status: SubscriptionStatus.ACTIVE,
      endDate: {
        $lte: new Date(new Date().setDate(new Date().getDate()))
      }
    }).exec();

    subscriptions.forEach((subscription) => {
      this.logger.log(`Active subscription about to expire: ${subscription.userId}`);
      this.eventEmitter.emit(NotificationEvent.SUBSCRIPTION_REMINDER, {
        userId: subscription.userId,
        subscription
      });
    });

    // for trial subscriptions
    const trialSubscriptions = await this.subscriptionModel.find({
      status: SubscriptionStatus.TRIAL,
      endDate: {
        $lte: new Date(new Date().setDate(new Date().getDate() + GRACE_PERIOD_DAYS))
      }
    }).exec();

    trialSubscriptions.forEach((subscription) => {
      this.logger.log(`Trial subscription about to expire: ${subscription.userId}`);
      this.eventEmitter.emit(NotificationEvent.SUBSCRIPTION_REMINDER, {
        userId: subscription.userId,
        subscription
      });
    });
  }

  /**
   * Activate subscription
   * @param userId 
   * @param planId 
   * @param invoiceId 
   * @returns 
   */
  async activateSubscription(userId: string, planId: string, invoiceId: string) {
    try {
      const plan = await this.planService.findOnePlan(planId);

      if (!plan) {
        throw new NotFoundException(`Plan ${planId} not found`);
      }

      // set end date based on plan interval
      let endDate: Date;
      
      if (plan.interval === PlanInterval.MONTHLY) {
        endDate = new Date(new Date().setMonth(new Date().getMonth() + 1));
      } else if (plan.interval === PlanInterval.QUARTERLY) {
        endDate = new Date(new Date().setMonth(new Date().getMonth() + 3));
      } else if (plan.interval === PlanInterval.YEARLY) {
        endDate = new Date(new Date().setFullYear(new Date().getFullYear() + 1));
      } else {
        throw new BadRequestException(`Invalid plan interval: ${plan.interval}`);
      }

      // check current usage
      const currentUsage = await this.subscriptionModel.findOne({userId}).exec();
      
      let psychologistConsultationsCount = 0;

      if (currentUsage) {
        this.logger.log(`Current usage found for user ${userId}: ${JSON.stringify(currentUsage)}`);
        
        // add current usage to new subscription
        psychologistConsultationsCount = currentUsage.usage.psychologistConsultationsCount;
        
        // add current add on usage to new subscription
        psychologistConsultationsCount += currentUsage.addOnUsage.psychologistConsultationsCount;
      } else {
        this.logger.log(`No current usage found for user ${userId}`);
      }

      const subscription = await this.subscriptionModel.findOneAndUpdate({
        userId
      }, {
        planId: plan.id,
        planRef: plan,
        status: SubscriptionStatus.ACTIVE,
        startDate: new Date(),
        endDate,
        usage: {
          psychologistConsultationsCount,
          chatsCount: 0
        },
        resources: {
          psychologistConsultations: plan.psychologistConsultations,
          chats: plan.chats
        },
        addOnResources: {
          psychologistConsultations: currentUsage?.addOnResources.psychologistConsultations || 0,
          chats: currentUsage?.addOnResources.chats || 0
        },
        addOnUsage: {
          psychologistConsultationsCount: currentUsage?.addOnUsage.psychologistConsultationsCount || 0,
          chatsCount: currentUsage?.addOnUsage.chatsCount || 0
        },
        $push: {
          invoiceIds: invoiceId
        }
      }, {
        new: true,
        upsert: true
      }).exec();

      return subscription;
    } catch (error) {
      this.logger.error(`Error activating subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create trial subscription
   * @param userId 
   * @param planId 
   * @returns 
   */
  async createTrialSubscription(userId: string, planId: string) {
    try {
      const plan = await this.planService.findOnePlan(planId);

      if (!plan) {
        throw new NotFoundException(`Plan ${planId} not found`);
      }

      const subscription = await this.subscriptionModel.findOneAndUpdate({
        userId
      }, {
        planId: plan.id,
        planRef: plan,
        status: SubscriptionStatus.TRIAL,
        startDate: new Date(),
        endDate: new Date(new Date().setDate(new Date().getDate() + TRIAL_DAYS)),
        usage: {
          psychologistConsultationsCount: 0,
          chatsCount: 0
        },
        resources: {
          psychologistConsultations: plan.psychologistConsultations,
          chats: plan.chats
        },
        addOnResources: {
          psychologistConsultations: 0,
          chats: 0
        },
        addOnUsage: {
          psychologistConsultationsCount: 0,
          chatsCount: 0
        },
      }, {
        new: true,
        upsert: true
      }).exec();

      return subscription;
    } catch (error) {
      this.logger.error(`Error creating trial subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cancel subscription
   * @param userId 
   * @returns 
   */
  async cancelSubscription(userId: string) {
    try {
      const subscription = await this.subscriptionModel.findOneAndUpdate({
        userId
      }, {
        status: SubscriptionStatus.CANCELLED,
        cancelledAt: new Date()
      }, {
        new: true
      }).exec();

      return subscription;
    } catch (error) {
      this.logger.error(`Error cancelling subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get user subscription
   * @param userId 
   * @returns 
   */
  async getUserSubscription(userId: string) {
    try {
      const subscription = await this.subscriptionModel.findOne({
        userId
      }).populate('planRef').exec();

      return subscription;
    } catch (error) {
      this.logger.error(`Error getting user subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete user subscription
   * @param userId 
   * @returns 
   */
  async deleteUserSubscription(userId: string) {
    try {
      const subscription = await this.subscriptionModel.deleteOne({
        userId
      }).exec();

      return subscription;
    } catch (error) {
      this.logger.error(`Error deleting subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update subscription usage
   * @param userId 
   * @param type 
   * @param amount 
   * @returns 
   */
  async updateSubscriptionUsage(userId: string, type: SubscriptionUsageType, amount: number): Promise<void> {
    try {
      const subscription = await this.subscriptionModel.findOne({
        userId
      }).exec();

      if (subscription) {
        const planUsage = {
          usage: subscription.usage[type] ?? 0,
          resources: subscription.resources[RESOURCE_USAGE_MAP[type]] ?? 0
        }
  
        const addonUsage = {
          usage: subscription.addOnUsage[type] ?? 0,
          resources: subscription.addOnResources[RESOURCE_USAGE_MAP[type]] ?? 0
        }

        const updateSubscription = {
          usage: {
            ...subscription.usage
          },
          addOnUsage: {
            ...subscription.addOnUsage
          },
          resources: {
            ...subscription.resources
          },
          addOnResources: {
            ...subscription.addOnResources
          }
        }

        if (amount < 0) {
          // Decrease usage for deletion resources
          const absAmount = Math.abs(amount);
          
          // Prioritize reducing addon usage first
          if (addonUsage.usage > 0) {
            const addonReductionAmount = Math.min(addonUsage.usage, absAmount);
            
            // Reduce addon usage
            updateSubscription.addOnUsage[type] = addonUsage.usage - addonReductionAmount;
            
            // Return resources to addon pool
            updateSubscription.addOnResources[RESOURCE_USAGE_MAP[type]] = addonUsage.resources + addonReductionAmount;
            
            // If we still need to reduce more from plan usage
            const remainingReduction = absAmount - addonReductionAmount;
            if (remainingReduction > 0) {
              // Ensure plan usage never goes below 0
              const planReductionAmount = Math.min(planUsage.usage, remainingReduction);
              updateSubscription.usage[type] = planUsage.usage - planReductionAmount;
            }
          } else {
            // No addon usage to reduce, only reduce plan usage
            // Ensure never less than 0
            const planReductionAmount = Math.min(planUsage.usage, absAmount);
            updateSubscription.usage[type] = planUsage.usage - planReductionAmount;
          }
        } else {
          // Increase usage for creation resources
          if (addonUsage.resources > 0) {
            if (addonUsage.resources - amount >= 0) {
              // Add addon usage
              updateSubscription.addOnUsage[type] = addonUsage.usage + amount;
              
              // Subtract addon resources
              updateSubscription.addOnResources[RESOURCE_USAGE_MAP[type]] = addonUsage.resources - amount;
            } else {
              // Amount to be added to addon usage
              const addonUsageAmount = addonUsage.resources;

              // Amount to be added to plan usage
              const planUsageAmount = amount - addonUsage.resources;

              // Add addon usage
              updateSubscription.addOnUsage[type] = addonUsage.usage + addonUsageAmount;
              
              // Subtract addon resources
              updateSubscription.addOnResources[RESOURCE_USAGE_MAP[type]] = addonUsage.resources - addonUsageAmount;

              // Add plan usage
              // Should check never greater than plan limit
              updateSubscription.usage[type] = planUsage.usage + planUsageAmount;
            }
          } else {
            // Update plan usage
            // Should check never greater than plan limit
            if (planUsage.usage + amount > subscription.resources[RESOURCE_USAGE_MAP[type]]) {
              amount = subscription.resources[RESOURCE_USAGE_MAP[type]] - planUsage.usage;
            }
            
            updateSubscription.usage[type] = planUsage.usage + amount;
          }
        }

        await this.subscriptionModel.updateOne({ userId }, updateSubscription).exec();
      } else {
        this.logger.warn(`User ${userId} has no subscription`);
      }
    } catch (error) {
      this.logger.error(`Error updating subscription usage: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Reset subscription usage to 0
   * @param userId 
   * @returns 
   */
  async resetSubscriptionUsage(userId: string) {
    try {
      const subscription = await this.subscriptionModel.findOneAndUpdate({
        userId
      }, {
        usage: {
          psychologistConsultationsCount: 0,
          chatsCount: 0
        }
      }, {
        new: true
      }).exec();

      return subscription;
    } catch (error) {
      this.logger.error(`Error resetting subscription usage: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Enroll user in a plan
   * @param userId 
   * @param planId 
   * @returns 
   */
  async enrollSubscription(userId: string, planId: string, isTrial: boolean = false) {
    try {
      const plan = await this.planService.findOnePlan(planId);

      if (!plan) {
        throw new NotFoundException(`Plan ${planId} not found`);
      }
      
      if (plan.status !== PlanStatus.ACTIVE) {
        throw new BadRequestException(`Plan ${planId} is not active`);
      }
      
      const subscription = await this.subscriptionModel.findOne({
        userId
      }).exec();

      if (subscription) {
        if (subscription.planId === planId && subscription.status === SubscriptionStatus.ACTIVE) {
          throw new BadRequestException(`User already has an active subscription on the same plan ${planId}`);
        }

        if (subscription.usage.psychologistConsultationsCount > subscription.planRef.psychologistConsultations) {
          throw new BadRequestException(`User has exceeded the maximum number of psychologist consultations for plan ${planId}`);
        }

        if (subscription.usage.chatsCount > subscription.planRef.chats) {
          throw new BadRequestException(`User has exceeded the maximum number of messages for plan ${planId}`);
        }
      } else {
        if (isTrial) {
          await this.createTrialSubscription(userId, planId);
          return await this.invoiceService.createTrialSubscriptionInvoice(userId, planId);
        }
      }

      // check if invoice already exists
      const pendingInvoice = await this.invoiceService.getPendingInvoiceForPlan(userId, planId);
      
      if (pendingInvoice) {
        return await this.invoiceService.generateInvoicePayment(pendingInvoice.id);
      }

      const invoice = await this.invoiceService.createSubscriptionInvoice(userId, planId);
      return await this.invoiceService.generateInvoicePayment(invoice.id);

      // BYPASS: activate subscription
      // return await this.activateSubscription(userId, planId, invoice.id);
    } catch (error) {
      this.logger.error(`Error requesting subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if user has enough resources to perform an operation
   * 
   * This function checks both addon resources and plan resources.
   * It prioritizes addon resources first, then falls back to plan resources if needed.
   * 
   * @param userId User ID
   * @param type Type of resource to check
   * @param amount Amount of resource needed (always positive)
   * @returns True if user has enough resources, throws exception otherwise
   */
  async canUseResource(userId: string, type: SubscriptionUsageType, amount: number): Promise<boolean> {
    try {
      // Validate input
      if (amount <= 0) {
        throw new BadRequestException('Resource amount must be positive');
      }

      const subscription = await this.subscriptionModel.findOne({
        userId
      }).populate('planRef').exec();

      if (!subscription) {
        throw new NotFoundException(`Subscription not found for user ${userId}`);
      }

      if (![SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIAL].includes(subscription.status)) {
        throw new BadRequestException(`User ${userId} has no active subscription`);
      }

      // Get resource usage and limits
      const resourceKey = RESOURCE_USAGE_MAP[type];
      const planUsage = {
        usage: subscription.usage[type] ?? 0,
        resources: subscription.resources[resourceKey] ?? 0
      }

      const addonUsage = {
        usage: subscription.addOnUsage[type] ?? 0,
        resources: subscription.addOnResources[resourceKey] ?? 0
      }

      // Calculate total available resources
      const planAvailable = planUsage.resources - planUsage.usage;
      const addonAvailable = addonUsage.resources;
      const totalAvailable = planAvailable + addonAvailable;

      // Check if total available resources are sufficient
      if (totalAvailable < amount) {
        throw new BadRequestException(
          `Insufficient ${type} resources. Requested: ${amount}, Available: ${totalAvailable} ` +
          `(Plan: ${planAvailable}, Addon: ${addonAvailable})`
        );
      }

      // Check addon resources first
      if (addonAvailable > 0) {
        if (addonAvailable >= amount) {
          // Addon resources are sufficient
          return true;
        } else {
          // Need to use both addon and plan resources
          const remainingAmount = amount - addonAvailable;
          
          // Check if plan resources are sufficient for the remaining amount
          if (planAvailable < remainingAmount) {
            throw new BadRequestException(
              `Insufficient plan ${type} resources after using addon resources. ` +
              `Remaining needed: ${remainingAmount}, Plan available: ${planAvailable}`
            );
          }
        }
      } else {
        // No addon resources, check plan resources
        if (planAvailable < amount) {
          throw new BadRequestException(
            `Insufficient plan ${type} resources. Requested: ${amount}, Available: ${planAvailable}`
          );
        }
      }

      return true;
    } catch (error) {
      this.logger.error(`Error validating subscription usage: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add addons to subscription
   * @param userId User ID
   * @param addon Addon
   */
  async addAddonsByUserId(userId: string, addon: Resource) {
    try {
      const subscription = await this.subscriptionModel.findOneAndUpdate({
        userId
      }, {
        $inc: {
          'addOnResources.psychologistConsultations': addon.psychologistConsultations,
          'addOnResources.chats': addon.chats
        }
      }, {
        new: true
      }).exec();

      return subscription;
    } catch (error) {
      this.logger.error(`Error adding addons: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get available resources for a user
   * @param userId User ID
   * @param type Type of resource
   * @returns Available resources
   */
  async getAvailableResources(userId: string, type: SubscriptionUsageType): Promise<{
    plan: { available: number, total: number },
    addon: { available: number, total: number }
  }> {
    const subscription = await this.subscriptionModel.findOne({ userId }).exec();
    
    if (!subscription) {
      throw new NotFoundException(`User ${userId} has no subscription`);
    }
    
    const resourceKey = RESOURCE_USAGE_MAP[type];
    
    return {
      plan: {
        available: (subscription.resources[resourceKey] ?? 0) - (subscription.usage[type] ?? 0),
        total: subscription.resources[resourceKey] ?? 0
      },
      addon: {
        available: subscription.addOnResources[resourceKey] ?? 0,
        total: (subscription.addOnResources[resourceKey] ?? 0) + (subscription.addOnUsage[type] ?? 0)
      }
    };
  }
}
