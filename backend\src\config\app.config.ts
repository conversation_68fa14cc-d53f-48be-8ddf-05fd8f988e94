import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  port: parseInt(process.env.PORT || '4000', 10),
  
  // MongoDB config
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/nestjs-auth',
  },

  // Redis
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB || '0', 10),
  },
  
  // JWT config
  jwt: {
    secret: process.env.JWT_SECRET || '',
    expiresIn: process.env.JWT_EXPIRATION || '1d',
  },
  
  // CORS config
  cors: {
    enabled: true,
    origin: process.env.CORS_ORIGIN || '*',
  },

  // FazPass config
  fazpass: {
    baseUrl: process.env.FAZPASS_BASE_URL || 'https://api.fazpass.com',
    merchantKey: process.env.FAZPASS_MERCHANT_KEY || '',
    smsGatewayKey: process.env.FAZPASS_SMS_GATEWAY_KEY || '',
    waGatewayKey: process.env.FAZPASS_WA_GATEWAY_KEY || '',
    emailGatewayKey: process.env.FAZPASS_EMAIL_GATEWAY_KEY || '',
    brandName: process.env.FAZPASS_BRAND_NAME || '',
  },

  // Midtrans config
  midtrans: {
    clientKey: process.env.MIDTRANS_CLIENT_KEY || '',
    serverKey: process.env.MIDTRANS_SERVER_KEY || '',
    environment: process.env.MIDTRANS_ENVIRONMENT || 'sandbox',
  },

  // Mailjet config
  mailjet: {
    publicKey: process.env.MAILJET_PUBLIC_KEY || '',
    privateKey: process.env.MAILJET_PRIVATE_KEY || '',
    senderEmail: process.env.MAILJET_SENDER_EMAIL || '',
    senderName: process.env.MAILJET_SENDER_NAME || '',
  },

  // S3 config
  s3: {
    cdnUrl: process.env.S3_CDN_URL || '',
    endpoint: process.env.S3_ENDPOINT || '',
    accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
    region: process.env.S3_REGION || '',
    bucket: process.env.S3_BUCKET || ''
  }
}));
