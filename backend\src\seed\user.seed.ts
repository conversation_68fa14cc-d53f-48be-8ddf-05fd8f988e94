import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument, UserStatus, Gender, Religion } from '../user/schemas/user.schema';

@Injectable()
export class UserSeeder {
  private readonly logger = new Logger(UserSeeder.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}

  async seed() {
    try {
      // Check if users already exist
      const count = await this.userModel.countDocuments().exec();
      if (count > 0) {
        this.logger.log('Users already seeded');
        return;
      }

      const users = this.getDummyUsers();
      const createdUsers = await this.userModel.insertMany(users);
      this.logger.log(`Seeded ${createdUsers.length} users`);
      
      return createdUsers;
    } catch (error) {
      this.logger.error('Error seeding users:', error);
      throw error;
    }
  }

  private getDummyUsers(): Partial<User>[] {
    const currentYear = new Date().getFullYear();
    
    return [
      {
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        verified: true,
        phoneNumber: '6281234567890',
        image: undefined,
        dateOfBirth: new Date(currentYear - 25, 0, 15),
        photos: undefined,
        gender: Gender.MALE,
        religion: Religion.ISLAM,
        occupation: 'Software Engineer',
        isSmoker: false,
        acceptDifferentReligion: true,
        about: 'Saya seorang software engineer yang suka belajar hal baru dan berpetualang.',
        psychTestCompleted: true,
        status: UserStatus.ACTIVE
      },
      {
        name: 'Anisa Rahmawati',
        email: '<EMAIL>',
        verified: true,
        phoneNumber: '6281234567891',
        image: undefined,
        dateOfBirth: new Date(currentYear - 23, 5, 20),
        photos: undefined,
        gender: Gender.FEMALE,
        religion: Religion.ISLAM,
        occupation: 'Doctor',
        isSmoker: false,
        acceptDifferentReligion: false,
        about: 'Dokter umum yang menyukai traveling dan kuliner.',
        psychTestCompleted: true,
        status: UserStatus.ACTIVE
      },
      {
        name: 'Dewi Lestari',
        email: '<EMAIL>',
        verified: true,
        phoneNumber: '6281234567892',
        image: undefined,
        dateOfBirth: new Date(currentYear - 27, 3, 10),
        photos: undefined,
        gender: Gender.FEMALE,
        religion: Religion.KRISTEN,
        occupation: 'Teacher',
        isSmoker: false,
        acceptDifferentReligion: true,
        about: 'Guru yang mencintai dunia pendidikan dan anak-anak.',
        psychTestCompleted: true,
        status: UserStatus.ACTIVE
      },
      {
        name: 'Agus Setiawan',
        email: '<EMAIL>',
        verified: true,
        phoneNumber: '6281234567893',
        image: undefined,
        dateOfBirth: new Date(currentYear - 30, 7, 25),
        photos: undefined,
        gender: Gender.MALE,
        religion: Religion.KATHOLIK,
        occupation: 'Entrepreneur',
        isSmoker: true,
        acceptDifferentReligion: true,
        about: 'Pengusaha muda yang sedang mengembangkan bisnis di bidang kuliner.',
        psychTestCompleted: true,
        status: UserStatus.ACTIVE
      },
      {
        name: 'Rina Wijaya',
        email: '<EMAIL>',
        verified: true,
        phoneNumber: '6281234567894',
        image: undefined,
        dateOfBirth: new Date(currentYear - 28, 2, 12),
        photos: undefined,
        gender: Gender.FEMALE,
        religion: Religion.BUDDHA,
        occupation: 'Designer',
        isSmoker: false,
        acceptDifferentReligion: true,
        about: 'UI/UX Designer yang kreatif dan menyukai seni.',
        psychTestCompleted: true,
        status: UserStatus.ACTIVE
      },
      {
        name: 'Hendra Kurniawan',
        email: '<EMAIL>',
        verified: true,
        phoneNumber: '6281234567895',
        image: undefined,
        dateOfBirth: new Date(currentYear - 26, 9, 5),
        photos: undefined,
        gender: Gender.MALE,
        religion: Religion.ISLAM,
        occupation: 'Marketing',
        isSmoker: true,
        acceptDifferentReligion: false,
        about: 'Marketing professional dengan pengalaman di berbagai industri.',
        psychTestCompleted: true,
        status: UserStatus.ACTIVE
      },
      {
        name: 'Siti Aisyah',
        email: '<EMAIL>',
        verified: true,
        phoneNumber: '*************',
        image: undefined,
        dateOfBirth: new Date(currentYear - 24, 11, 30),
        photos: undefined,
        gender: Gender.FEMALE,
        religion: Religion.ISLAM,
        occupation: 'Accountant',
        isSmoker: false,
        acceptDifferentReligion: true,
        about: 'Akuntan yang teliti dan menyukai tantangan di bidang keuangan.',
        psychTestCompleted: true,
        status: UserStatus.ACTIVE
      },
      {
        name: 'Fajar Nugraha',
        email: '<EMAIL>',
        verified: true,
        phoneNumber: '*************',
        image: undefined,
        dateOfBirth: new Date(currentYear - 29, 4, 18),
        photos: undefined,
        gender: Gender.MALE,
        religion: Religion.KRISTEN,
        occupation: 'Lawyer',
        isSmoker: false,
        acceptDifferentReligion: true,
        about: 'Pengacara yang berdedikasi untuk menegakkan keadilan.',
        psychTestCompleted: true,
        status: UserStatus.ACTIVE
      },
      {
        name: 'Maya Sari',
        email: '<EMAIL>',
        verified: true,
        phoneNumber: '*************',
        image: undefined,
        dateOfBirth: new Date(currentYear - 27, 6, 22),
        photos: undefined,
        gender: Gender.FEMALE,
        religion: Religion.HINDU,
        occupation: 'Doctor',
        isSmoker: false,
        acceptDifferentReligion: true,
        about: 'Dokter spesialis yang peduli dengan kesehatan masyarakat.',
        psychTestCompleted: true,
        status: UserStatus.ACTIVE
      },
      {
        name: 'Rizky Pratama',
        email: '<EMAIL>',
        verified: true,
        phoneNumber: '6281234567899',
        image: undefined,
        dateOfBirth: new Date(currentYear - 31, 8, 14),
        photos: undefined,
        gender: Gender.MALE,
        religion: Religion.ISLAM,
        occupation: 'Software Engineer',
        isSmoker: true,
        acceptDifferentReligion: true,
        about: 'Full-stack developer yang suka memecahkan masalah kompleks.',
        psychTestCompleted: true,
        status: UserStatus.ACTIVE
      }
    ];
  }
}
