import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { UserOnlineDocument } from './schemas/user-online.schema';

@Injectable()
export class UserOnlineService {
  constructor(@InjectModel('UserOnline') private readonly userOnlineModel: Model<UserOnlineDocument>) {}

  async getUserOnline(userId: string): Promise<UserOnlineDocument | null> {
    return await this.userOnlineModel.findOne({ userId }).exec();
  }

  async setUserOnline(userId: string): Promise<UserOnlineDocument | null> {
    return await this.userOnlineModel.findOneAndUpdate({ userId: Types.ObjectId.createFromHexString(userId) }, {
      userId: Types.ObjectId.createFromHexString(userId),
      isOnline: true,
      lastOnlineAt: new Date()
    }, { new: true, upsert: true });
  }

  async setUserOffline(userId: string): Promise<UserOnlineDocument | null> {
    return await this.userOnlineModel.findOneAndUpdate({ userId: Types.ObjectId.createFromHexString(userId) }, {
      userId: Types.ObjectId.createFromHexString(userId),
      isOnline: false,
      lastOnlineAt: new Date()
    }, { new: true, upsert: true });
  }
}
