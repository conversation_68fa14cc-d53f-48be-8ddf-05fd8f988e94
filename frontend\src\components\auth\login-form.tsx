"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

const loginSchema = z.object({
  email: z.string().email({
    message: "Masukkan alamat email yang valid",
  }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export function LoginForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
    },
  });

  async function onSubmit(data: LoginFormValues) {
    setIsLoading(true);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/signin`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
          provider: "email"
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || "Gagal masuk");
      }

      console.log("Login response:", result);
      
      // Store necessary data for OTP verification
      // Jika API tidak mengembalikan sessionId, kita buat sendiri berdasarkan email
      const sessionId = result.sessionId || `login_${data.email}_${Date.now()}`;
      
      sessionStorage.setItem('authSession', JSON.stringify({
        sessionId: sessionId,
        contactMethod: "email",
        contactValue: data.email
      }));
      
      // Redirect to OTP verification page
      router.push('/verify-otp');
    } catch (error) {
      console.error("Login error:", error);
      // Handle error state
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full bg-accent-red hover:bg-accent-red/90 text-white" disabled={isLoading}>
          {isLoading ? "Mengirim..." : "Kirim OTP"}
        </Button>
      </form>
    </Form>
  );
}
