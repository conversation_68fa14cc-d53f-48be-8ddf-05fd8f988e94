import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { Plan } from './plan.schema';

export enum SubscriptionStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
  PENDING = 'pending',
  TRIAL = 'trial',
}

export type Usage = {
  psychologistConsultationsCount: number;
  chatsCount: number;
};

export type Resource = {
  psychologistConsultations: number;
  chats: number;
};

export type SubscriptionDocument = Subscription & Document;

@Schema({ timestamps: true })
export class Subscription {
  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  planId: string;

  @Prop({ 
    type: MongooseSchema.Types.ObjectId, 
    ref: 'Plan', 
    required: true 
  })
  planRef: Plan;

  @Prop({ required: true })
  startDate: Date;

  @Prop({ required: true })
  endDate: Date;

  @Prop({ 
    type: String, 
    enum: SubscriptionStatus, 
    default: SubscriptionStatus.PENDING 
  })
  status: SubscriptionStatus;

  @Prop()
  cancelledAt: Date;

  @Prop()
  lastBillingDate: Date;

  @Prop()
  nextBillingDate: Date;

  @Prop({ default: false })
  autoRenew: boolean;

  @Prop({ type: Object, default: {} })
  usage: Usage;

  @Prop({ type: Object, default: {} })
  resources: Resource;

  @Prop({ type: Object, default: {} })
  addOnResources: Resource;

  @Prop({ type: Object, default: {} })
  addOnUsage: Usage;

  @Prop({ type: [String], default: [] })
  invoiceIds: string[];
}

export const SubscriptionSchema = SchemaFactory.createForClass(Subscription);

// Create a compound index for userId and planId to ensure a user can only have one subscription
SubscriptionSchema.index({ userId: 1, planId: 1 }, { unique: true });
