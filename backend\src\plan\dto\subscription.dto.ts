import { IsBoolean, IsOptional, IsString } from "class-validator";
import { Resource } from "../schemas/subscription.schema";

export class EnrollSubscriptionDto {
  @IsString()
  planId: string;

  @IsOptional()
  @IsBoolean()
  isTrial: boolean;
}

export class SubscriptionPlanResponseDto {
  id: string;
  name: string;
  description: string;
  psychologistConsultations: number;
  chats: number;
}

export class SubscriptionUsageDto {
  psychologistConsultationsCount: number;
  chatsCount: number;
}

export class UserSubscriptionResponseDto {
  id: string;
  userId: string;
  plan: SubscriptionPlanResponseDto;
  startDate: string;
  endDate: string;
  status: string;
  autoRenew: boolean;
  resources: Resource;
  usage: SubscriptionUsageDto;
  addOnUsage: SubscriptionUsageDto;
  addOnResources: Resource;
}

export class SubscriptionResourcesDto {
  psychologistConsultations: {
    plan: {
      available: number;
      total: number;
    };
    addon: {
      available: number;
      total: number;
    };
  };
  chats: {
    plan: {
      available: number;
      total: number;
    };
    addon: {
      available: number;
      total: number;
    };
  };
}
