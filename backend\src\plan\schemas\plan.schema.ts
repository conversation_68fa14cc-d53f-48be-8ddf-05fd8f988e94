import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum PlanStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DEPRECATED = 'deprecated',
}

export enum PlanInterval {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
  LIFETIME = 'lifetime',
}

export type PlanDocument = Plan & Document;

@Schema({ timestamps: true })
export class Plan {
  @Prop({ required: true, unique: true })
  code: string;

  @Prop({ required: true })
  name: string;

  @Prop()
  description: string;

  @Prop({ required: true })
  price: number;

  @Prop({ required: true, enum: PlanInterval, default: PlanInterval.MONTHLY })
  interval: PlanInterval;

  @Prop({ required: true, min: 0 })
  psychologistConsultations: number;

  @Prop({ required: true, min: 0 })
  chats: number;

  @Prop({ type: Object, default: {} })
  features: Record<string, any>;

  @Prop({ 
    type: String, 
    enum: PlanStatus, 
    default: PlanStatus.ACTIVE 
  })
  status: PlanStatus;

  @Prop({ default: 0 })
  sortOrder: number;

  @Prop({ default: false })
  isPopular: boolean;

  @Prop({ default: false })
  isDefault: boolean;

  @Prop({ default: false })
  isHidden: boolean;
}

export const PlanSchema = SchemaFactory.createForClass(Plan);
