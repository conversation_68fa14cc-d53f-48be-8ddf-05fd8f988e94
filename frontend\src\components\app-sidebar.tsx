"use client"

import * as React from "react"
import {
  Heart,
  MessageSquare,
  User,
  CreditCard,
  LogOut,
  HelpCircle,
  LayoutDashboard,
} from "lucide-react"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"

import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const navMainData = [
  {
    title: "Dasbor",
    url: "/dashboard",
    icon: LayoutDashboard,
    isActive: true,
    items: [],
  },
  {
    title: "Pasangan",
    url: "/dashboard/matches",
    icon: Heart,
    items: [],
  },
  {
    title: "Obrolan",
    url: "/dashboard/chat",
    icon: MessageSquare,
    items: [],
  },
  {
    title: "<PERSON><PERSON> <PERSON><PERSON>",
    url: "/dashboard/profile",
    icon: User,
    items: [],
  },
  {
    title: "<PERSON><PERSON><PERSON>",
    url: "/dashboard/subscription",
    icon: CreditCard,
    items: [],
  },
];

const navSecondaryData = [
  {
    title: "Bantuan",
    url: "#",
    icon: HelpCircle,
  },
  {
    title: "Keluar",
    url: "/logout",
    icon: LogOut,
  },
];

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [user, setUser] = useState({
    name: "Memuat...",
    email: "<EMAIL>",
    avatar: null as string | null,
  });
  const router = useRouter();

  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    try {
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('pairsona_token');
          router.replace('/login');
          return;
        }
        throw new Error('Failed to fetch profile');
      }

      const profile = await response.json();
      setUser({
        name: profile.name || 'Pengguna',
        email: profile.email || '<EMAIL>',
        avatar: profile.image || null,
      });
    } catch (err) {
      console.error('Failed to fetch user profile:', err);
      // Keep default values if fetch fails
    }
  };

  return (
    <Sidebar variant="inset" {...props} className="bg-[#D0544D] border-r border-[#B8453F] shadow-sm text-white">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/dashboard" className="flex justify-center">
                <img
                  src="/logowhite.png"
                  alt="Pairsona Logo"
                  className="h-12 w-auto object-contain"
                />
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navMainData} />
        <NavSecondary items={navSecondaryData} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
    </Sidebar>
  )
}
