import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Plan, PlanSchema } from '../plan/schemas/plan.schema';
import { PlanSeeder } from './plan.seed';
import { QuestionSeeder } from './question.seed';
import { UserSeeder } from './user.seed';
import { PersonalityTestSeeder } from './personality-test.seed';
import { SeedService } from './seed.service';
import { User, UserSchema } from '../user/schemas/user.schema';
import { PersonalityModule } from 'src/personality/personality.module';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Plan.name, schema: PlanSchema },
      { name: User.name, schema: UserSchema }
    ]),
    PersonalityModule,
    UserModule
  ],
  providers: [
    SeedService, 
    PlanSeeder, 
    QuestionSeeder, 
    UserSeeder, 
    PersonalityTestSeeder
  ],
  exports: [SeedService],
})
export class SeedModule {}
